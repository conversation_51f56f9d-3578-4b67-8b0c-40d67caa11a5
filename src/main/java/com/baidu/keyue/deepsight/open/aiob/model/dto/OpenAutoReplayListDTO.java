package com.baidu.keyue.deepsight.open.aiob.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 小秘书查询请求对象
 *
 * <AUTHOR>
 * @date 2025/8/13 16:31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OpenAutoReplayListDTO {

    /**
     * 租户id
     */
    @NotBlank(message = "租户id不能为空")
    private String tenantId;

}
