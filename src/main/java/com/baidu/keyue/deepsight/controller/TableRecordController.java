package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.datamanage.request.FileImportFieldMappingRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.FileImportRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.FileImportSaveRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.FileImportTaskDeleteRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.FileImportTaskListRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableDataCurlRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.FieldAiMappingResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.FileImportTaskDeleteResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.FileImportTaskQueryResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.FileImportTaskResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.FileSaveResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.TableSyncDetailResponse;
import com.baidu.keyue.deepsight.service.datamanage.TableContentService;
import com.baidu.keyue.deepsight.service.file.FileService;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import com.baidu.kybase.sdk.user.service.UserAuthService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 数据接入相关接口
 *
 * <AUTHOR>
 * @className TableContentController
 * @description 数据表内容
 * @date 2025/1/8 17:35
 */
@RestController
@RequestMapping("/deepsight/v1/table/content")
@Slf4j
public class TableRecordController {
    @Autowired
    private TableContentService contentService;
    
    @Resource
    private UserAuthService userAuthService;
    
    @Resource
    private FileService fileService;

    /** 数据集导入模板文件BOS 下载URL*/
    @Value("${template.tableContent}")
    private String templateBosUrl;

    /**
     * 数据表内容插入
     *
     * @return
     */
    @PostMapping("/sync")
    public BaseResponse batchSync(HttpServletRequest req,
                                  @RequestBody List<Map<String, Object>> itemMaps) {
        BaseResponse response = BaseResponse.of(ErrorCode.SUCCESS);
        String logId = WebContextHolder.getRequestId();
        try {
            String dataTableId = String.valueOf(req.getAttribute("dataTableId"));
            contentService.handleBatchSync(itemMaps, dataTableId);
        } catch (Exception e) {
            response.setCode(ErrorCode.INTERNAL_ERROR.getCode());
            response.setMessage(e.getMessage());
            log.error("table content sync,lodId:{}", logId, e);
        }
        return response;
    }

    /**
     * 数据接入信息
     *
     * @return
     */
    @PostMapping("/info/sync")
    public BaseResponse<TableSyncDetailResponse> dataCurlInfo(@Valid @RequestBody GetTableDataCurlRequest request) {
        return BaseResponse.of(contentService.getSyncInfo(request));
    }

    /**
     * 文件导入模板
     *
     * @return 文件模板BOS 下载URL
     */
    @GetMapping("/file/download/template")
    public BaseResponse<String> downloadTemplate() {
        return BaseResponse.of(templateBosUrl);
    }
    

    /**
     * 文件上传保存
     */
    @PostMapping(value = "/file/save")
    public BaseResponse<FileSaveResponse> fileSave(@RequestBody @Valid FileImportSaveRequest request) {
        UserAuthInfo userAuthInfo = WebContextHolder.getUserAuthInfo();
        return BaseResponse.of(contentService.fileSave(request, userAuthInfo));
    }

    /**
     * 文件导入数据
     *
     * @param fileImportRequest 文件导入数据请求体
     * @return 导入结果
     */
    @PostMapping("/file/dataImport")
    public BaseResponse<FileImportTaskResponse> fileImport(@RequestBody @Valid FileImportRequest fileImportRequest) {
        UserAuthInfo userAuthInfo = WebContextHolder.getUserAuthInfo();
        return BaseResponse.of(contentService.fileImport(fileImportRequest, userAuthInfo));
    }

    /**
     * 文件导入数据任务列表
     *
     * @param taskListRequest 文件导入数据任务列表请求体
     * @return 导入结果
     */
    @PostMapping("/file/taskList")
    public BaseResponse<List<FileImportTaskQueryResponse>> taskList(@RequestBody @Valid FileImportTaskListRequest taskListRequest) {
        UserAuthInfo userAuthInfo = WebContextHolder.getUserAuthInfo();
        return BaseResponse.of(contentService.fileImportTaskList(taskListRequest, userAuthInfo));
    }

    /**
     * 文件导入数据任务删除
     *
     * @param deleteRequest 文件导入数据任务删除请求体
     * @return 导入结果
     */
    @PostMapping("/file/taskDelete")
    public BaseResponse<FileImportTaskDeleteResponse> taskList(@RequestBody @Valid FileImportTaskDeleteRequest deleteRequest) {
        UserAuthInfo userAuthInfo = WebContextHolder.getUserAuthInfo();
        return BaseResponse.of(contentService.fileImportTaskDelete(deleteRequest, userAuthInfo));
    }

    /**
     * 文件导入数据字段映射
     *
     * @param mappingRequest 文件导入数据任务字段映射请求体
     * @return 映射结果
     */
    @PostMapping("/fieldMapping")
    public BaseResponse<FieldAiMappingResponse> fieldMapping(@RequestBody @Valid FileImportFieldMappingRequest mappingRequest) {
        return BaseResponse.of(contentService.fieldMapping(mappingRequest));
    }

}
