package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.visitor.request.VisitorDeleteRequest;
import com.baidu.keyue.deepsight.models.visitor.request.VisitorSaveRequest;
import com.baidu.keyue.deepsight.models.visitor.request.VisitorQueryRequest;
import com.baidu.keyue.deepsight.service.visitor.VisitorService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 访客管理接口
 * @ClassName VisitorManagementController
 * @Description 访客管理接口
 * <AUTHOR>
 * @Date 2025/2/20 3:40 PM
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/deepsight/v1/operate")
public class VisitorManageController {
    
    @Resource
    private VisitorService visitorService;

    /**
     * 访客查询
     * @param queryRequest
     * @return
     */
    @PostMapping("/query")
    public BaseResponse query(@RequestBody @Valid VisitorQueryRequest queryRequest){
        return BaseResponse.of(visitorService.queryList(queryRequest));
    }

    /**
     * 访客添加
     * @param saveRequest
     * @return
     */
    @PostMapping("/insert")
    public BaseResponse insert(@RequestBody VisitorSaveRequest saveRequest){
        return BaseResponse.of(visitorService.insert(saveRequest));
    }

    /**
     * 访客更新
     * @param saveRequest
     * @return
     */
    @PostMapping("/update")
    public BaseResponse update(@RequestBody VisitorSaveRequest saveRequest){
        return BaseResponse.of(visitorService.update(saveRequest));
    }

    /**
     * 访客删除
     * @param deleteRequest
     * @return
     */
    @PostMapping("/delete")
    public BaseResponse delete(@RequestBody VisitorDeleteRequest deleteRequest){
        visitorService.delete(deleteRequest);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }
}
