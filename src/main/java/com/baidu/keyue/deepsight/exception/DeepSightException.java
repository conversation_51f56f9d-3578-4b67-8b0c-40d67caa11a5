package com.baidu.keyue.deepsight.exception;

public final class DeepSightException {

    public static class ParamsErrorException extends ServiceException {
        public ParamsErrorException(ErrorCode errorCode, String message) {
            super(errorCode, message);
        }
    }

    public static class CatalogOperatorFailedException extends ServiceException {
        public CatalogOperatorFailedException(ErrorCode errorCode, String message) {
            super(errorCode, message);
        }
    }

    public static class InitTenantFailedException extends ServiceException {
        public InitTenantFailedException(ErrorCode errorCode, String message) {
            super(errorCode, message);
        }
    }

    public static class UserLoginException extends ServiceException {
        public UserLoginException(ErrorCode errorCode, String message) {
            super(errorCode, message);
        }
    }

    public static class AuthTokenException extends ServiceException {
        public AuthTokenException(ErrorCode errorCode, String message) {
            super(errorCode, message);
        }
    }

    public static class EsException extends ServiceException {
        public EsException(ErrorCode errorCode, String message) {
            super(errorCode, message);
        }
    }

    public static class LabelOperatorFailedException extends ServiceException {
        public LabelOperatorFailedException(ErrorCode errorCode, String message) {
            super(errorCode, message);
        }
    }

    public static class MemoryExtractOperatorFailedException extends ServiceException {
        public MemoryExtractOperatorFailedException(ErrorCode errorCode, String message) {
            super(errorCode, message);
        }
    }

    public static class UserProfileOperatorFailedException extends ServiceException {
        public UserProfileOperatorFailedException(ErrorCode errorCode, String message) {
            super(errorCode, message);
        }
    }

    public static class TaskInfoNotFoundException extends ServiceException {
        public TaskInfoNotFoundException(Long id) {
            super(ErrorCode.NOT_FOUND, String.format("任务信息不存在，id: %d", id));
        }
    }

    public static class BSCException extends ServiceException {
        public BSCException(String message) {
            super(ErrorCode.INTERNAL_ERROR, message);
        }
    }

    public static class TaskInfoIdInvalidException extends ServiceException {
        public TaskInfoIdInvalidException() {
            super(ErrorCode.BAD_REQUEST, "任务信息ID为空");
        }
    }

    public static class DorisExecException extends ServiceException {
        public DorisExecException() {
            super(ErrorCode.INTERNAL_ERROR, "标签资源表操作失败");
        }

        public DorisExecException(String message) {
            super(ErrorCode.INTERNAL_ERROR, message);
        }
    }

    public static class LabelCountFailedException extends ServiceException {
        public LabelCountFailedException() {
            super(ErrorCode.INTERNAL_ERROR, "执行标签值统计失败");
        }
    }

    public static class LabelExecCalculateFailedException extends ServiceException {
        public LabelExecCalculateFailedException(String message) {
            super(ErrorCode.INTERNAL_ERROR, "提交计算失败: " + message);
        }

        public LabelExecCalculateFailedException() {
            super(ErrorCode.INTERNAL_ERROR, "提交计算失败");
        }
    }

    public static class LabelExecCancelFailedException extends ServiceException {
        public LabelExecCancelFailedException(ErrorCode errorCode, String message) {
            super(errorCode, message);
        }
    }

    public static class LabelExecDistributeFailedException extends ServiceException {
        public LabelExecDistributeFailedException() {
            super(ErrorCode.INTERNAL_ERROR, "执行标签值分布统计失败");
        }
    }

    public static class DataPredictionInitFailedException extends ServiceException {
        public DataPredictionInitFailedException() {
            super(ErrorCode.INTERNAL_ERROR, "初始化预测数据源配置失败");
        }
    }

    public static class DataPredictionOperationFailedException extends ServiceException {
        public DataPredictionOperationFailedException(ErrorCode errorCode, String message) {
            super(errorCode, message);
        }
    }

    public static class DataPredictionGetDataSourceException extends ServiceException {
        public DataPredictionGetDataSourceException(ErrorCode errorCode, String message) {
            super(errorCode, message);
        }
    }

    public static class DataPredictionUpdateDataSourceException extends ServiceException {
        public DataPredictionUpdateDataSourceException(ErrorCode errorCode, String message) {
            super(errorCode, message);
        }
    }

    public static class DataPredictionDataConfigException extends ServiceException {
        public DataPredictionDataConfigException(ErrorCode errorCode, String message) {
            super(errorCode, message);
        }
    }

    public static class DataPredictionUpdatePredictSwitchException extends ServiceException {
        public DataPredictionUpdatePredictSwitchException(ErrorCode errorCode, String message) {
            super(errorCode, message);
        }
    }

    public static class CustomerGroupFailedException extends ServiceException {
        public CustomerGroupFailedException(ErrorCode errorCode, String message) {
            super(errorCode, message);
        }
    }

    public static class BusyRequestException extends ServiceException {
        public BusyRequestException(ErrorCode errorCode) {
            super(errorCode);
        }

        public BusyRequestException(ErrorCode errorCode, String message) {
            super(errorCode, message);
        }
    }

    public static class OperationModeException extends ServiceException {
        public OperationModeException(ErrorCode errorCode, String message) {
            super(errorCode, message);
        }
    }

    public static class IdMappingException extends ServiceException {
        public IdMappingException(ErrorCode errorCode, String message) {
            super(errorCode, message);
        }
    }

    public static class GroupDiffusionCountFailedException extends ServiceException {
        public GroupDiffusionCountFailedException() {
            super(ErrorCode.INTERNAL_ERROR, "执行人群扩散统计总数失败");
        }
    }

    public static class GroupDiffusionDataNullFailedException extends ServiceException {
        public GroupDiffusionDataNullFailedException() {
            super(ErrorCode.INTERNAL_ERROR, "执行人群可用数据为空");
        }
    }

    public static class GroupDiffusionGroupCheckFailedException extends ServiceException {
        public GroupDiffusionGroupCheckFailedException() {
            super(ErrorCode.INTERNAL_ERROR, "人群扩散客群检查异常");
        }

        public GroupDiffusionGroupCheckFailedException(String message) {
            super(ErrorCode.INTERNAL_ERROR, message);
        }
    }

    public static class GroupDiffusionGroupDorisTempResourceFailedException extends ServiceException {
        public GroupDiffusionGroupDorisTempResourceFailedException() {
            super(ErrorCode.INTERNAL_ERROR, "人群扩散临时资源申请失败");
        }
    }

    public static class GroupDiffusionUploadFailedException extends ServiceException {
        public GroupDiffusionUploadFailedException() {
            super(ErrorCode.INTERNAL_ERROR, "人群扩散文件打包失败");
        }
    }

    public static class GroupDiffusionOutputFileMissingException extends ServiceException {
        public GroupDiffusionOutputFileMissingException() {
            super(ErrorCode.INTERNAL_ERROR, "人群扩散结果输出文件不存在");
        }
    }

    public static class GroupDiffusionOutputFileInvalidException extends ServiceException {
        public GroupDiffusionOutputFileInvalidException() {
            super(ErrorCode.INTERNAL_ERROR, "人群扩散结果输出文件无效");
        }
    }

    public static class GroupDiffusionOutputFeatureFileHandleException extends ServiceException {
        public GroupDiffusionOutputFeatureFileHandleException(String message) {
            super(ErrorCode.INTERNAL_ERROR, message);
        }
    }

    public static class GroupDiffusionModelSubmitFailedException extends ServiceException {
        public GroupDiffusionModelSubmitFailedException() {
            super(ErrorCode.INTERNAL_ERROR, "人群扩散任务提交失败");
        }
    }

    public static class NodeMarkDownInvalid extends ServiceException {
        public NodeMarkDownInvalid() {
            super(ErrorCode.BAD_REQUEST, "请在行首位置输入 “#或##” 及一个空格");
        }
    }

    public static class NodePredictFailedException extends ServiceException {
        public NodePredictFailedException() {
            super(ErrorCode.INTERNAL_ERROR, "节点预测算子请求失败");
        }
    }

    public static class MarkDownH1EmptyException extends ServiceException {
        public MarkDownH1EmptyException() {
            super(ErrorCode.BAD_REQUEST, "请在一级标题行首位置输入“#”及一个空格");
        }
    }

    public static class MarkDownH2EmptyException extends ServiceException {
        public MarkDownH2EmptyException() {
            super(ErrorCode.BAD_REQUEST, "请在二级标题行首位置输入“##”及一个空格");
        }
    }

    public static class MarkDownSameH1AndH2Exception extends ServiceException {
        public MarkDownSameH1AndH2Exception() {
            super(ErrorCode.BAD_REQUEST, "一级标题和二级标题内容不能重复");
        }
    }

    public static class MarkDownSameH2Exception extends ServiceException {
        public MarkDownSameH2Exception() {
            super(ErrorCode.BAD_REQUEST, "同一级标题下的二级标题内容不能重复");
        }
    }

    /**
     * 业务异常
     */
    public static class BusinessException extends ServiceException {
        public BusinessException(ErrorCode errorCode) {
            super(errorCode);
        }

        public BusinessException(ErrorCode errorCode, String message) {
            super(errorCode, message);
        }
    }

    public static class SopTaskCfgException extends ServiceException {
        public SopTaskCfgException(ErrorCode errorCode, String message) {
            super(errorCode, message);
        }
    }
}
