package com.baidu.keyue.deepsight.service.builtin;

import com.baidu.keyue.deepsight.enums.BuiltinLabelEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs;

/**
 * 内置标签计算服务接口
 * 负责不同类型内置标签的计算逻辑
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface BuiltinLabelCalculateService {

    /**
     * 处理静态内置标签（标签1-3、6）
     * 复用现有的标签计算逻辑
     * 
     * @param tenantId 租户ID
     * @param label 标签信息
     * @param labelEnum 标签枚举
     */
    void processStaticBuiltinLabel(String tenantId, LabelWithBLOBs label, BuiltinLabelEnum labelEnum);

    /**
     * 处理动态意向标签（标签4：机器人名称_意向标签）
     * 基于customTagList字段进行处理
     * 
     * @param tenantId 租户ID
     * @param label 标签信息
     */
    void processDynamicIntentionLabel(String tenantId, LabelWithBLOBs label);

    /**
     * 处理动态LLM标签（标签5：大模型标签key）
     * 基于tagExtraInfo字段进行处理
     * 
     * @param tenantId 租户ID
     * @param label 标签信息
     */
    void processDynamicLLMLabel(String tenantId, LabelWithBLOBs label);

    /**
     * 处理客服风险标签（标签6：营销电话投诉风险）
     * 基于时间维度和LLM分析进行处理
     * 
     * @param tenantId 租户ID
     * @param label 标签信息
     */
    void processCustomerServiceRiskLabel(String tenantId, LabelWithBLOBs label);
}
