package com.baidu.keyue.deepsight;

import com.baidu.keyue.deepsight.config.BaseUserAuthInfoProcessor;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.service.tenant.TenantInfoService;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.Assert.assertNotNull;

@SpringBootTest(classes = com.baidu.keyue.deepsight.App.class)
@Slf4j
public class LoginSimulationTest {

    @Autowired
    private BaseUserAuthInfoProcessor baseUserAuthInfoProcessor;
    
    @Autowired
    private TenantInfoService tenantInfoService;

    /**
     * 模拟新用户登录触发租户初始化
     */
    @Test
    public void testSimulateNewUserLogin() {
        // 1. 构造用户认证信息
        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(12345678915L);           // 用户ID
        userAuthInfo.setTenantId(15987654321L);         // 租户ID（新租户）
        userAuthInfo.setUserName("testUser6");         // 用户名
        userAuthInfo.setAccountId("testAccount6");    // 账户ID
        // 2. 模拟登录前处理（这会触发租户初始化）
        try {
            baseUserAuthInfoProcessor.before(userAuthInfo);
            log.info("用户登录成功，租户初始化完成");
        } catch (Exception e) {
            log.error("登录失败", e);
        } finally {
            // 3. 清理资源
            baseUserAuthInfoProcessor.after();
        }
        
        // 4. 验证租户是否已初始化
        TenantInfo tenantInfo = tenantInfoService.queryTenantInfo(String.valueOf(userAuthInfo.getTenantId()));
        assertNotNull(tenantInfo);
        log.info("租户信息: {}", tenantInfo);
    }
    
    /**
     * 模拟已存在租户的版本升级
     */
    @Test
    public void testSimulateExistingTenantUpgrade() {
        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(123456789L);
        userAuthInfo.setTenantId(111111111L);         // 已存在的租户ID
        userAuthInfo.setUserName("existingUser");
        userAuthInfo.setAccountId("existingAccount");
        
        try {
            baseUserAuthInfoProcessor.before(userAuthInfo);
            log.info("已存在租户登录成功，版本检查完成");
        } finally {
            baseUserAuthInfoProcessor.after();
        }
    }
}