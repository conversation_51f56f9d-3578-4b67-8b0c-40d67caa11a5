server:
  max-http-header-size: 16384
  port: 8080
mybatis:
  mapper-locations: classpath:mapper/mysqldb/*.xml
spring:
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 100MB
  liquibase:
    enabled: false
    change-log: classpath:/db.changelog/master.xml
    user: root
    password: znkf@2024
    url: jdbc:mysql://************:8037/deep_sight_dev?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai
    drop-first: false
  application:
    name: deep-sight-platform
  jpa:
    show-sql: true
    hibernate:
      show-sql: true
  datasource:
    aiob:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: jdbc:mysql://*************:8306/aiob?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai
      username: root
      password: 123456a?
    mysql:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: jdbc:mysql://************:8037/deep_sight_dev?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai
      username: root
      password: znkf@2024
    doris:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: jdbc:mysql://************:8033/deep_sight_dev?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai
      db: "deep_sight_dev"
      username: admin
      password: znkf_2024
  data:
    elasticsearch:
      # 索引统一前缀
      prefix: dev_
      client:
        reactive:
          hosts: *************
          port: 8200
          username: superuser
          password: Voc@2025

  kafka:
    enable: true
    bootstrap-servers: **************:9095,*************:9095,*************:9095
    producer:
      acks: 1
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer

    consumer:
      group-id: deepsight-platform-group-rd
      enable-auto-commit: false
      auto-offset-reset: latest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      max-poll-records: 300
      auto-commit-interval: 100
    properties:
      security.protocol: SASL_SSL
      ssl:
        protocol: TLSv1.2
        truststore:
          location: client.truststore.jks
          password: bms@kafka
        endpoint:
          identification.algorithm:
          protocols: TLSv1.2,TLSv1.1,TLSv1
      sasl:
        mechanism: PLAIN
        jaas.config: org.apache.kafka.common.security.plain.PlainLoginModule required username="deepsight_root" password="znkf@2024";
#  profiles:
#    active: test

echoPath:
  # 旅程服务地址 "http://************" blb 地址
  url: "http://************"
  # 服务名称
  targetPath: "/echopath/v1"
  dependentUris:
    customerGroupTaskListUri: "/echoPath/customerGroup/task/list"

keyueBase:
  accessKey: dongcha123456
  secretKey: dongcha123456
  # 用户中心配置
  user:
    host: http://*************:8072
    auth:
      # 是否开启LoginFilter进行会话、权限校验
      enable: false
      # 校验url路径模式（多个逗号分隔）
      urlPatterns: '/*'
      # 忽略校验的路径前缀（多个逗号分隔，可为空）
      ignorePathPrefixes: '/deepsight/v1/actuator/health,/deepsight/v1/table/content/sync,/external/deepsight/v1/,/external/deepsight/v1/*,/deepsight/v1/table/content/file/upload'
      # 尝试获取用户信息，但获取失败不阻止后续处理请求（多个逗号分隔，可为空）
      tryGetUserPaths: ''
  message:
    pushUrl: http://*************:8564/message/internalapi/costume/push
  # 运营管理平台配置
  admin:
    host: http://*************:8082
data:
  sync:
    url: http://127.0.0.1:8080

kafka:
  topics:
    dataSync: "deep_sight_append_dataset_data_dev"
    userProfile: "deep_sight_user_profile_dev"

redis:
  host: "************"
  port: "8032"
  password: "znkf_2024"
  prefix: "deepsight_dev-"

bsc:
  # 本地调试："http://bjdd-admin-00.bjdd:8220"
  # bcc 环境访问地址："http://bsc-api.bj.baidubce.com"
  endpoint: "http://bjdd-admin-00.bjdd:8220"
  vpcId: "vpc-f3ndwf8e0077"
  subnetId: "sbn-s72h4xsia6sr"
  logicalZone: "zoneF"
  securityGroupId: "g-p40e1bfwhrar"
  cidr: "**********/24"
  jobPrefix: "deepsight_dev"

customer-calculate:
  sampleNumber: 1000

memory-calculate:
  modelUrl: "http://**************:8156/getCustomerMemory/v1"
  kafkaConfig:
    kafkaTopic: "deep_sight_memory_extract_dev"

datapredict-calculate:
  modelUrl: "http://**************:8156/getCustomerProfile/v1"
  kafkaConfig:
    kafkaTopic: "deep_sight_data_predict_dev"

# 百度数据接口配置
baidu:
  data:
    # idmapping地址
    idMappingUrl: http://*************:8156/meg/cache/MoonService/get_external_mapping_id
    userInfo:
      # 查询meg接口获取用户真实数据
      queryUrl: http://*************:8156/meg/cache/json/v2
  whiteList:
    userTables: "mock_user_1234567890"

management:
  endpoints:
    web:
      exposure:
        include: prometheus
  prometheus:
    metrics:
      export:
        enabled: true
switch:
  # 是否为升级场景，升级时设置为 true，则所有定时任务不会执行
  platformUpgrade: false
  labelCalSchedulerWithBSC: false
  memoryCalScheduler: false
  customerCalSchedulerWithBSC: false
  dataPredictionSchedulerWithBSC: false
  pullBaiduData: false
  customerDiffusion: false
  # 系统启动后公告表&&租户自动升级，默认开启
  tenantUpgradeRunner: false
  # 外呼任务信息同步
  aiobTaskInfoSync: false
  # 接通率告警扫描
  aiobConnectRateAlert: false
  # metric 指标聚合
  aiobSessionMetricAgg: false

ai:
  token: G9js+A7jZfZXOhPv2kOMp6ilUFCGMQsroKgRWxUE8T/RCNUrmhvaQR9pvv90679I7Jz0hSAGQ6tSfcrQS8l9k9fiX0kzMUTgEgk3cjOuLnBk7FFlJTJLYN7MvcCFuMEaFUCkHF+LWuWJBIPXcsqbkA==
  # AI 生成字段枚举
  fieldEnumApi:
    url: http://ievalue-api.baidu-int.com/openApi/v1/prompt/service/gzjy1elmymbvf4e5y8c5
  # AI 生成字段信息
  fieldInfoApi:
    url: http://ievalue-api.baidu-int.com/openApi/v1/prompt/service/z3vyibt1l9sdmjvfcsae
  # AI 字段映射相关配置
  qianfan:
    url: "https://qianfan.baidubce.com/v2/chat/completions"
    authorization: "Bearer bce-v3/ALTAK-oQ1IVFtBEDVCPgh9JXmNx/9f292d372979f53e676a0b59f94a4b3cd07ebc6f"
  template:
    fieldMapping: "ai/fieldMappingTemplate.txt"
    genField: "ai/genFieldTemplate.txt"
    genEnum: "ai/genEnumTemplate.txt"
file:
  upload:
    # 文件导入数据支持后缀
    suffixNames: "xls,xlsx,csv"
    # 文件导入数据英文字段正则校验
    enFiledPattern: "^[A-Za-z0-9_]+$"
bos:
  # 用户的Access Key ID
  accessKeyId: "ALTAKqvVoHzcZcPj5FAVHIaG3M"
  # 用户的Secret Access Key
  secretAccessKey: "014609949f104765ae39cabe73f42fc2"
  # 用户自己指定地域的官方域名
  endpoint: "https://bj.bcebos.com"
  # 运行环境：dev、sandbox、pro
  env: "dev"
  # 预制桶名
  bucket:
    # 文件导入数据上传桶
    dataSync: "insight-data-sync"
    diffusion: "customer-diffusion"
  # STS URL durationSeconds为过期时间，单位s
  stsUrl: "https://sts.bj.baidubce.com/v1/sessionToken?durationSeconds=600"
deepSight:
  kafka:
    topic:
      # 客服kafka topic
      keyue: "deep_sight_ai_conversation_data_sync"
      # 外呼kafka topic
      aiob: "deep_sight_aiob_data_sync"
      # id-mapping 的topic
      idMapping: "deep_sight_idmapping_dev"
      # sop topic
      sop: "deep_sight_aiob_sop_dev"
      debug: "deep_sight_aiob_debug_data_sync"
# 发版版本号，以1.0 beat版本为version-1递增
app-version: 4

diffusion-calculate:
  modelUrl: "http://yq01-aip-gpu-test06.yq01:8155/crowd_diffusion/v1/create"
template:
  # 人群导入模板BOS分享链接
  customerGroup: "https://insight-data-sync.bj.bcebos.com/v1/template/%E5%AF%BC%E5%85%A5%E4%BA%BA%E7%BE%A4%E5%8C%85%E6%A8%A1%E6%9D%BF.zip?authorization=bce-auth-v1%2FALTAKqvVoHzcZcPj5FAVHIaG3M%2F2025-03-28T07%3A33%3A55Z%2F-1%2Fhost%2Fa7439cd452576485d54612a02f78d74a6c3ef784a745180153140be57f3d9869"
  # 数据集导入模板BOS分享链接
  tableContent: "https://insight-data-sync.bj.bcebos.com/v1/template/%E6%96%87%E4%BB%B6%E5%AF%BC%E5%85%A5%E6%95%B0%E6%8D%AE%E6%A8%A1%E6%9D%BF.zip?authorization=bce-auth-v1%2FALTAKqvVoHzcZcPj5FAVHIaG3M%2F2025-03-28T07%3A33%3A35Z%2F-1%2Fhost%2F2ac8aac58b197563027403e0063e8861401d22fda5446b25949f50f095321273"
# 人群扩散配置
customer-group:
  # 种子人群最大占比
  seed-proportion: 0.5
  # 特征筛选系统推荐比例, 默认30
  system-recommend: 30
  # 导入人群忽略的匹配字段
  import-ignore-match-field: "age,age_group,city,gender,register_time,update_time,tags,area,country,district,membership_level,os,province,user_type,income_level,education_level,life_stage,industry,occupation,city_level,bd_gender,bd_age_group,bd_education_level,bd_occupation,bd_industry,bd_life_stage,bd_marriage_status,bd_consume_level,bd_consume_intent,bd_geographic_location,bd_interests,deepsight_datetime,deepsight_update_datetime,device_model"

node-predict:
  node-summary-url: "http://*************:8067/ProcessNode/getProcessNodeSummary/v1"

aiob:
  diagram-record-view-url: "http://************:8735/open/api/v2/diagram/version/record/view"
  diagram-record-url: "http://************:8735/open/api/v2/diagram/version/record"
  # 外呼服务地址
  url: "http://*************:8841"
  # 旅程请求路径
  requestPath: "/deepsight/v1/aiob"
  debugPath: "aiob-server/internal/robot2/getPublishVersionInfo"
  detailPath: "aiob-server/internal/task/detail"
  # 服务名称 "/aiob-server"
  targetPath: ""
  # 外呼任务列表
  taskList: "/aiob-server/internal/task/list"
  # 外呼任务刷新获取信息批量最大值
  queryTaskInfoMax: 300

# 外呼通话记录统计指标
aiob-agg:
  status: false
  day: 90
# 数据集管理相关配置
data-table-manager:
  # 数据集显示列默认展示字段个数（排序取前几）
  default-show-fields-size: 6
# safe sdk配置文件路径，项目外使用绝对路径
safe:
  sdk-config-path: ${SAFE_SDK_PATH:safesdk.properties}
  # okHttp配置:ip、host白名单，无域名时Host=IP,逗号分割。需要配置BSC、ECHO等使用WebClient\RestTemplateUtils访问的服务，具体需要配置服务参考文档：
  # https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/53G-5UwZAd/B5g1beJD1U/OjfZQDIZcr3tbk
  whitelist:
    ips: ${OKHTTP_WHITE_LIST_IP:***************,************,**************,*************}
    hosts: ${OKHTTP_WHITE_LIST_HOST:bsc-api.bj.baidubce.com,************,qianfan.baidubce.com}
    
dial-online-time:
  online-time: "2025-07-31 23:59:59"