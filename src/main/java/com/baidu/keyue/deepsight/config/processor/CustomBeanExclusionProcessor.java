package com.baidu.keyue.deepsight.config.processor;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.type.classreading.CachingMetadataReaderFactory;
import org.springframework.core.type.classreading.MetadataReader;

@Slf4j
public class CustomBeanExclusionProcessor implements BeanDefinitionRegistryPostProcessor {

    private final Boolean platformUpgrade;
    private final String[] excludedPackages;

    public CustomBeanExclusionProcessor(Environment environment) {
        this.platformUpgrade = environment.getProperty("switch.platformUpgrade", Boolean.class, false);
        this.excludedPackages = environment.getProperty("custom.excluded-packages.scheduler", String[].class,
                new String[]{"com.baidu.keyue.deepsight.schedule"});
    }

    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
        CachingMetadataReaderFactory metadataReaderFactory = new CachingMetadataReaderFactory();
        for (String beanName : registry.getBeanDefinitionNames()) {
            BeanDefinition bd = registry.getBeanDefinition(beanName);
            String className = bd.getBeanClassName();
            try {
                // 如果 className 为空（如 @Bean 工厂方法或被代理类）
                if (className == null && bd.getSource() instanceof Resource) {
                    // 此方法尝试通过 Resource 读取 class 信息
                    MetadataReader reader = metadataReaderFactory.getMetadataReader((Resource) bd.getSource());
                    className = reader.getClassMetadata().getClassName();
                }

                // 升级时，排除相关的bean，默认会排除所有定时任务的bean
                if (className != null
                        && BooleanUtils.isTrue(platformUpgrade)
                        && StringUtils.containsAny(className, excludedPackages)) {
                    registry.removeBeanDefinition(beanName);
                    log.info("platformUpgrade: {}, Excluded bean: {}, (beanName = {} )",
                            platformUpgrade, className, beanName);
                }

            } catch (Exception e) {
                // 防止 MetadataReader 加载失败，继续处理
                log.error("platformUpgrade: {}, Failed to resolve class for bean: {}, reason: {}",
                        platformUpgrade, beanName, e.getMessage(), e);
            }
        }
    }


    @Override
    public void postProcessBeanFactory(org.springframework.beans.factory.config.ConfigurableListableBeanFactory beanFactory) throws BeansException {
        // No-op
    }
}