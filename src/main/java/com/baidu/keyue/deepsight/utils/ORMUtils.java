package com.baidu.keyue.deepsight.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.database.service.DorisUtils;
import com.baidu.keyue.deepsight.enums.AiobFailReasonEnum;
import com.baidu.keyue.deepsight.enums.AiobFailTypeEnum;
import com.baidu.keyue.deepsight.enums.AiobSortFieldEnum;
import com.baidu.keyue.deepsight.enums.AiobTaskStatusEnum;
import com.baidu.keyue.deepsight.enums.AlertConfigTypeEnum;
import com.baidu.keyue.deepsight.enums.AlertTimeTypeEnum;
import com.baidu.keyue.deepsight.enums.DateLineTypeEnum;
import com.baidu.keyue.deepsight.enums.DialMetricQueryTypeEnum;
import com.baidu.keyue.deepsight.enums.OperationModeEnum;
import com.baidu.keyue.deepsight.enums.PredictTypeEnum;
import com.baidu.keyue.deepsight.enums.SortTypeEnum;
import com.baidu.keyue.deepsight.models.agg.AiobAggMetricCal;
import com.baidu.keyue.deepsight.models.dial.CallCoreMetricsRequest;
import com.baidu.keyue.deepsight.models.dial.ConnectionRateTrendDetailRequest;
import com.baidu.keyue.deepsight.models.dial.ConnectionRateTrendRequest;
import com.baidu.keyue.deepsight.models.dial.ThirtyDayRankingRequest;
import com.baidu.keyue.deepsight.models.diffusion.DiffusionResponseItem;
import com.baidu.keyue.deepsight.models.doris.TableDescribeDto;
import com.baidu.keyue.deepsight.models.meg.Attribute;
import com.baidu.keyue.deepsight.models.meg.Item;
import com.baidu.keyue.deepsight.models.meg.MEGBaiduData;
import com.baidu.keyue.deepsight.models.meg.MEGIdEnum;
import com.baidu.keyue.deepsight.models.profile.UserProfileDto;
import com.baidu.keyue.deepsight.mysqldb.entity.AlertConfig;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.InsertSetMoreStep;
import org.jooq.InsertSetStep;
import org.jooq.InsertValuesStep4;
import org.jooq.Record;
import org.jooq.Record1;
import org.jooq.Record2;
import org.jooq.SQLDialect;
import org.jooq.SelectOnConditionStep;
import org.jooq.Table;
import org.jooq.UpdateSetMoreStep;
import org.jooq.UpdateSetStep;
import org.jooq.conf.ParamType;
import org.jooq.conf.Settings;
import org.jooq.impl.DSL;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.jooq.impl.DSL.condition;
import static org.jooq.impl.DSL.count;
import static org.jooq.impl.DSL.countDistinct;
import static org.jooq.impl.DSL.exists;
import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.hour;
import static org.jooq.impl.DSL.isoDayOfWeek;
import static org.jooq.impl.DSL.name;
import static org.jooq.impl.DSL.round;
import static org.jooq.impl.DSL.selectOne;
import static org.jooq.impl.DSL.sum;
import static org.jooq.impl.DSL.table;
import static org.jooq.impl.DSL.using;
import static org.jooq.impl.DSL.when;

/**
 * ORM sql 生成工具，只做 sql 生成，不做执行
 * <a href="https://www.jooq.org/doc/3.18/manual-single-page/">jooq文档</a>
 */
@UtilityClass
public class ORMUtils {

    static {
        System.setProperty("org.jooq.no-tips", "true");
        System.setProperty("org.jooq.no-logo", "true");
    }

    public static final Settings INLINED_SETTINGS = new Settings().withParamType(ParamType.INLINED);

    private final Integer roundDecimalsTwo = 2;

    /**
     * 使用外呼对话表时，需要关联外呼记录表获取手机号码
     *
     * @param talkTable
     * @param sessionTable
     * @param timeFilter
     * @return
     */
    public String getMemoryCalculateAiobTalkData(String talkTable, String sessionTable, String timeFilter) {
        Settings settings = new Settings().withParamType(ParamType.INLINED);

        Field<?>[] selectFields = new Field[]{
                field(name("t", "roleType")).as("role"),
                field(name("t", "content")),
                field(name("t", "sessionId")),
                field(name("s", "mobile")).as("mobile"),
        };

        String sql = using(SQLDialect.MYSQL, settings)
                .select(selectFields)
                .from(table(name(talkTable)).as("t"))
                .leftJoin(table(name(sessionTable)).as("s"))
                .on(field(name("t", "sessionId")).eq(field(name("s", "sessionId"))))
                .where(field(name("t", "deepsight_datetime")).gt(timeFilter))
                .getSQL();

        return sql;
    }

    public String getMemoryCalculateAiobSession(String table, String timeFilter) {
        Settings settings = new Settings().withParamType(ParamType.INLINED);

        Field<?>[] selectFields = new Field[]{
                field(name("oneId")),
                field(name("sessionId")),
                field(name("conversationContent")),
        };

        String sql = using(SQLDialect.MYSQL, settings)
                .select(selectFields)
                .from(table(name(table)))
                .where(field(name("deepsight_datetime")).greaterOrEqual(timeFilter),
                        field(name("oneId")).notEqual(""),
                        field(name("conversationContent")).notEqual(""))
                .getSQL();

        return sql;
    }

    /**
     * 查询某一个任务下，指定机器人场景的「接通」会话
     */
    public String getSOPAiobConnectedSession(String table, String robotVer, Integer robotSceneFilter) {
        Settings settings = new Settings().withParamType(ParamType.INLINED);
        Field<?>[] selectFields = new Field[]{
                field(name("oneId")),
                field(name("sessionId")),
                field(name("taskId")),
                field(name("robotScene")),
                field(name("sipCode")),
                field(name("botVersionId")),
                field(name("startTime")),
                field(name("customTagList")),
        };
        return using(SQLDialect.MYSQL, settings)
                .select(selectFields)
                .from(table(name(table)))
                .where(
                        field(name("botVersionId")).equal(robotVer),
                        field(name("robotScene")).equal(robotSceneFilter),
                        field(name("sipCode")).equal("200")
                )
                .getSQL();
    }

    public String getDataPredictSql(String mockUserTable, String memoryTable, String timeFilter) {
        Settings settings = new Settings().withParamType(ParamType.INLINED);

        Field<?>[] selectFields = new Field[]{
                field(name("m", "external_id")).as("id"),
                field(name("m", "oneId")),
                field(name("m", "memory_content")).as("content"),
                field(name("m", "memory_type")).as("type"),
                field(name("u", "mobile")),
        };

        String sql = using(SQLDialect.MYSQL, settings)
                .select(selectFields)
                .from(table(name(memoryTable)).as("m"))
                .leftJoin(table(name(mockUserTable)).as("u"))
                .on(field(name("m", "oneId")).eq(field(name("u", "oneId"))))
                .where(field(name("m", "deepsight_datetime")).gt(timeFilter))
                .getSQL();

        return sql;
    }

    public String updateUserMegData(String tableName, String oneId, MEGBaiduData megData) {
        List<String> baiduUIDs = megData.getMegIds().get(MEGIdEnum.baiduid);
        List<String> userUIDs = megData.getMegIds().get(MEGIdEnum.userid);
        List<String> cUIDs = megData.getMegIds().get(MEGIdEnum.cuid);
        List<String> imei = megData.getMegIds().get(MEGIdEnum.imei);
        List<String> mac = megData.getMegIds().get(MEGIdEnum.mac);
        List<String> idfa = megData.getMegIds().get(MEGIdEnum.idfa);
        List<String> oaID = megData.getMegIds().get(MEGIdEnum.oaid);
        List<Attribute> attributes = megData.getAttributes();


        if (CollectionUtils.isEmpty(baiduUIDs) && CollectionUtils.isEmpty(userUIDs)
                && CollectionUtils.isEmpty(cUIDs) && CollectionUtils.isEmpty(imei) && CollectionUtils.isEmpty(mac)
                && CollectionUtils.isEmpty(idfa) && CollectionUtils.isEmpty(oaID) && CollectionUtils.isEmpty(attributes)) {
            return "";
        }
        String now = DatetimeUtils.formatDate(LocalDateTime.now());

        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        UpdateSetStep update = dsl.update(table(name(tableName)));
        UpdateSetMoreStep moreStep = update.set(field(name(Constants.DORIS_DEFAULT_DATA_UPDATE_DATE_FIELD)), now);

        if (CollectionUtils.isNotEmpty(baiduUIDs)) {
            moreStep = moreStep.set(field(name("BAIDUID")), baiduUIDs.get(0));
        }
        if (CollectionUtils.isNotEmpty(userUIDs)) {
            moreStep = moreStep.set(field(name("USERID")), userUIDs.get(0));
        }
        if (CollectionUtils.isNotEmpty(cUIDs)) {
            moreStep = moreStep.set(field(name("CUID")), cUIDs.get(0));
        }
        if (CollectionUtils.isNotEmpty(imei)) {
            moreStep = moreStep.set(field(name("IMEI")), imei.get(0));
        }
        if (CollectionUtils.isNotEmpty(mac)) {
            moreStep = moreStep.set(field(name("MAC")), mac.get(0));
        }
        if (CollectionUtils.isNotEmpty(idfa)) {
            moreStep = moreStep.set(field(name("IDFA")), idfa.get(0));
        }
        if (CollectionUtils.isNotEmpty(oaID)) {
            moreStep = moreStep.set(field(name("OAID")), oaID.get(0));
        }

        for (Attribute attribute : attributes) {
            String name = attribute.getDescription().getName();
            PredictTypeEnum predictType = PredictTypeEnum.getByDesc(name);
            if (predictType == null) {
                continue;
            }
            StringBuilder data = new StringBuilder();
            if (attribute.getItem() == null || attribute.getItem().isEmpty()) {
                continue;
            }
            for (Item item : attribute.getItem()) {
                data.append(item.getValue()).append(",");
            }
            if (data.length() > 0) {
                data.deleteCharAt(data.length() - 1);
            }
            if (data.length() > 500) {
                data.delete(500, data.length());
            }
            String baiduField = predictType.getBaiduField();

            moreStep = moreStep.set(field(name(baiduField)), data.toString());

        }

        moreStep.where(field(name(Constants.TABLE_USER_ONE_ID)).eq(oneId));

        return moreStep.getSQL();
    }

    public String genInsertUserProfileSql(UserProfileDto userProfileDto, String tableName, String oneId, Map<String, String> userProfileTableSchema) {
        String now = DatetimeUtils.formatDate(LocalDateTime.now());
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);

        InsertSetStep insert = dsl.insertInto(table(name(tableName)));

        InsertSetMoreStep moreStep = insert
                .set(field(name(Constants.TABLE_USER_ONE_ID)), oneId)
                .set(field(name(Constants.DORIS_DEFAULT_DATA_UPDATE_DATE_FIELD)), now);
        for (String key : userProfileTableSchema.keySet()) {
            Object v = userProfileDto.getUserProfileRow().get(key);
            if (Objects.nonNull(v)) {
                if (v instanceof LocalDateTime dateTime) {
                    moreStep.set(field(name(key)), DatetimeUtils.formatDate(dateTime));
                } else if (v instanceof List list) {
                    moreStep.set(field(name(key)), String.format("[%s]", String.join(Constants.SEPARATOR, list)));
                } else {
                    moreStep.set(field(name(key)), v);
                }
            }
        }

        return moreStep.getSQL();
    }

    public String genUpdateUserProfileSql(UserProfileDto userProfileDto, String tableName, String oneId, Map<String, String> userProfileTableSchema) {
        String now = DatetimeUtils.formatDate(LocalDateTime.now());
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        UpdateSetStep update = dsl.update(table(name(tableName)));
        UpdateSetMoreStep moreStep = update.set(field(name(Constants.DORIS_DEFAULT_DATA_UPDATE_DATE_FIELD)), now);
        for (String key : userProfileTableSchema.keySet()) {
            Object v = userProfileDto.getUserProfileRow().get(key);
            if (Objects.nonNull(v)) {
                if (v instanceof LocalDateTime dateTime) {
                    moreStep.set(field(name(key)), DatetimeUtils.formatDate(dateTime));
                } else if (v instanceof List list) {
                    moreStep.set(field(name(key)), String.format("[%s]", String.join(Constants.SEPARATOR, list)));
                } else {
                    moreStep.set(field(name(key)), v);
                }
            }
        }
        moreStep.where(field(name(Constants.TABLE_USER_ONE_ID)).eq(oneId));
        return moreStep.getSQL();
    }

    public String generateLoadOneIdSetWithTime(String tableName, String timeFilter) {
        Settings settings = new Settings().withParamType(ParamType.INLINED);

        String sql = using(SQLDialect.MYSQL, settings)
                .selectDistinct(field(name("oneId")))
                .from(table(name(tableName)))
                .where(field(name(Constants.DORIS_DEFAULT_DATA_UPDATE_DATE_FIELD)).gt(timeFilter),
                        field(name("oneId")).notEqual(""))
                .getSQL();

        return sql;
    }

    public Pair<String, String> generateFetchPredictGroupDataSql(
            String mockUserTableName, List<TableDescribeDto> tableSchemas, List<Long> customerGroupIds, Float threshold,
            OperationModeEnum operationMode) {
        HashSet<String> exclusions = Sets.newHashSet();
        exclusions.add(Constants.DORIS_DEFAULT_DATA_INSERT_DATE_FIELD);
        exclusions.add(Constants.DORIS_DEFAULT_DATA_UPDATE_DATE_FIELD);
        exclusions.add(Constants.TABLE_USER_ONE_ID);

        List<String> groupCondition = customerGroupIds.stream()
                .map(id -> String.format("`%s` = '1'", DorisUtils.generateCustomerGroupFieldName(id))).toList();

        String whereGroupCondition = StringUtils.join(groupCondition, " OR ");

        List<String> caseStatements = tableSchemas.stream()
                .filter(item -> {
                    if (exclusions.contains(item.getFieldName())) {
                        return false;
                    }

                    if (StringUtils.startsWith(item.getFieldName(), "idm_")
                            || StringUtils.startsWith(item.getFieldName(), "bd_")
                            || StringUtils.startsWith(item.getFieldName(), "predict_")
                    ) {
                        return false;
                    }

                    if (OperationModeEnum.OPERATION_BY_SELF.equals(operationMode)
                            && StringUtils.startsWith(item.getFieldName(), "merge_")) {
                        return false;
                    }

                    return true;
                }).map(item -> {
                    // CASE WHEN `user_id` IS NOT NULL THEN 1 ELSE 0 END
                    // CASE WHEN `IMEI` != '' THEN 1 ELSE 0 END
                    // CASE WHEN array_size(`idm_cuid`) > 0 THEN 1 ELSE 0 END
                    if (item.defaultNull()) {
                        return String.format(" CASE WHEN `%s` IS NOT NULL THEN 1 ELSE 0 END ", item.getFieldName());
                    } else if (item.defaultBlankString()) {
                        return String.format(" CASE WHEN `%s` != '' THEN 1 ELSE 0 END ", item.getFieldName());
                    } else if (item.defaultBlankArray()) {
                        return String.format(" CASE WHEN array_size(`%s`) > 0 THEN 1 ELSE 0 END ", item.getFieldName());
                    } else {
                        return "";
                    }
                })
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        int thresholdCount = (int) (threshold / 100 * caseStatements.size());
        String caseSql = String.join(" + ", caseStatements);

        // 组装 sql
        String finalSqlTemplate = String.format("""
                FROM (
                    SELECT oneId,
                        (%s) AS non_null_count
                    FROM %s
                    WHERE %s
                    order by deepsight_datetime
                ) AS subquery
                WHERE oneId != '' AND subquery.non_null_count >= %d""", caseSql, mockUserTableName, whereGroupCondition, thresholdCount);

        return new ImmutablePair<>("SELECT COUNT(*) " + finalSqlTemplate, "SELECT * " + finalSqlTemplate);
    }

    public String generateOneIdRetrieveSql(String mockUserTable, List<String> oneIds) {
        Settings settings = new Settings().withParamType(ParamType.INLINED);

        Field<?>[] selectFields = new Field[]{
                field(name(Constants.TABLE_USER_ONE_ID)),
                field(name("mobile")),
                field(name("CUID")),
        };

        return using(SQLDialect.MYSQL, settings)
                .select(selectFields)
                .from(table(name(mockUserTable)))
                .where(field(name(Constants.TABLE_USER_ONE_ID)).in(oneIds))
                .getSQL();
    }

    public String generateBatchInsertDiffusion(String tableName, List<DiffusionResponseItem> batch, String createTime) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);

        InsertValuesStep4 step = dsl.insertInto(table(name(tableName)))
                .columns(field(name(Constants.TABLE_USER_ONE_ID)),
                        field(name(Constants.DORIS_SCORE_FIELD_NAME)),
                        field(name(Constants.DORIS_DEFAULT_DATA_INSERT_DATE_FIELD)),
                        field(name(Constants.DORIS_DEFAULT_DATA_UPDATE_DATE_FIELD)))
                .values(batch.get(0).getOneId(), String.format("%f", batch.get(0).getScore()), createTime, createTime);

        for (int i = 1; i < batch.size(); i++) {
            step.values(batch.get(i).getOneId(), String.format("%.8f", batch.get(i).getScore()), createTime, createTime);
        }

        return step.getSQL();
    }

    public String generateDiffusionFilterSeedGroupOneIds(String tableName, String groupField, List<String> seedOneIds) {
        Settings settings = new Settings().withParamType(ParamType.INLINED);
        Field<?>[] selectFields = new Field[]{
                field(name(Constants.TABLE_USER_ONE_ID))
        };

        String sql = using(SQLDialect.MYSQL, settings)
                .select(selectFields)
                .from(table(name(tableName)))
                .where(
                        field(name(Constants.TABLE_USER_ONE_ID)).in(seedOneIds),
                        field(name(groupField)).eq('0')
                )
                .getSQL();

        return sql;
    }

    public String generateBatchUpdateDiffusion(String tableName, DiffusionResponseItem predict, String updateTime) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);

        return dsl.update(DSL.table(DSL.name(tableName)))
                .set(DSL.field(Constants.DORIS_SCORE_FIELD_NAME), String.format("%.8f", predict.getScore()))
                .set(DSL.field(Constants.DORIS_DEFAULT_DATA_UPDATE_DATE_FIELD), updateTime)
                .where(DSL.field(Constants.TABLE_USER_ONE_ID).eq(predict.getOneId()))
                .getSQL();
    }

    public String generateClearCustomerGroupDataSql(String tableName, String field) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        return dsl.update(DSL.table(DSL.name(tableName)))
                .set(DSL.field(field), "0")
                .getSQL();
    }

    public String generateClearDiffusionTempTableDataSql(String tableName) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        return dsl.delete(DSL.table(DSL.name(tableName)))
                .where(DSL.field(Constants.TABLE_USER_ONE_ID).notEqual(""))
                .getSQL();
    }

    public String generateMigrateDiffusionDataToCustomerGroup(String tableName, String diffusionTableName, String field) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        return dsl.update(DSL.table(DSL.name(tableName)))
                .set(DSL.field(field), "1")
                .where(DSL.field(Constants.TABLE_USER_ONE_ID).in(
                        dsl.select(DSL.field(Constants.TABLE_USER_ONE_ID)).from(table(name(diffusionTableName)))
                ))
                .getSQL();
    }

    public String generateNodeUVSQL(String tableName, String taskId, Date startTime, Date endTime) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("task_id")).eq(taskId));
        if (Objects.nonNull(startTime)) {
            conditions.add(field(name("cal_date")).greaterOrEqual(DatetimeUtils.formatDate(startTime)));
        }
        if (Objects.nonNull(endTime)) {
            conditions.add(field(name("cal_date")).lessThan(DatetimeUtils.formatDate(endTime)));
        }
        return dsl.select(
                        field(name("node_id")), count(field(name(Constants.TABLE_USER_ONE_ID))).as("node_uv")).from(table(name(tableName)))
                .where(conditions.toArray(new Condition[0]))
                .groupBy(field(name("node_id")))
                .getSQL();
    }

    public String generateNodeHangUpSQL(String tableName, String taskId, Date startTime, Date endTime) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("task_id")).eq(taskId));
        conditions.add(field(name("hangup")).eq(1));
        if (Objects.nonNull(startTime)) {
            conditions.add(field(name("cal_date")).greaterOrEqual(DatetimeUtils.formatDate(startTime)));
        }
        if (Objects.nonNull(endTime)) {
            conditions.add(field(name("cal_date")).lessThan(DatetimeUtils.formatDate(endTime)));
        }
        return dsl.select(
                        field(name("node_id")), count(field(name(Constants.TABLE_USER_ONE_ID))).as("node_uv")).from(table(name(tableName)))
                .where(conditions.toArray(new Condition[0]))
                .groupBy(field(name("node_id")))
                .getSQL();
    }

    public String generateQueryTaskNameByTaskIdSQL(String tableName, String taskId) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("taskId")).eq(taskId));
        return dsl.select(field(name("taskName"))
                        , field(name("robotScene"))
                        , field(name("sessionId"))
                        , field(name("robotId"))).from(table(name(tableName)))
                .where(conditions.toArray(new Condition[0]))
                .limit(1)
                .getSQL();
    }

    public String generateQueryAgentIdBySQL(String tableName, String sessionId, String nodeId) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("sessionId")).eq(sessionId));
        conditions.add(field(name("nodeId")).eq(nodeId));
        return dsl.select(field(name("agent_id")), field(name("version_id"))).from(table(name(tableName)))
                .where(conditions.toArray(new Condition[0]))
                .limit(1)
                .getSQL();
    }

    public String generateCheckDuplicateDataSql(String tableName, String id, String field) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name(field)).eq(id));
        return dsl.select(count()).from(table(name(tableName)))
                .where(conditions.toArray(new Condition[0]))
                .limit(1)
                .getSQL();
    }

    public String generateQueryBasicInfoWithOneIds(String mockUserTableName, List<String> oneIds) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);

        List<String> keys = Lists.newArrayList(Constants.TABLE_USER_ONE_ID);
        keys.addAll(Constants.DIFFUSION_BASIC_INFO_WITH_SELF_OPERATION.keySet());

        List<Field<?>> fields = keys.stream().map(k -> {
            return field(name(k));
        }).collect(Collectors.toList());

        return dsl
                .select(fields)
                .from(table(name(mockUserTableName)))
                .where(field(name(Constants.TABLE_USER_ONE_ID)).in(oneIds))
                .getSQL();
    }

    /**
     * 从外呼指标统计表，获取昨天新增的 oneId
     *
     * @param aggTable  外呼指标统计表
     * @param yesterday 昨天的日期 yyyy-MM-dd
     * @return sql
     */
    public String generateNewOneIdsFromSessionAggTableInYesterday(String aggTable, String yesterday) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        return dsl
                .selectDistinct(DSL.field(Constants.TABLE_USER_ONE_ID))
                .from(DSL.table(DSL.name(aggTable)))
                .where(field("call_date").eq(yesterday))
                .getSQL();
    }

    /**
     * 从外呼指标统计表，查询指定时间范围的统计明细
     *
     * @param aggTable  外呼指标统计表
     * @param startDate 开始日期 yyyy-MM-dd
     * @param endDate   结束日期 yyyy-MM-dd
     * @param oneId     String oneId
     * @return sql
     */
    public String generateQuerySessionAggRecordsWith(String aggTable, String startDate, String endDate, String oneId) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        return dsl
                .select()
                .from(DSL.table(DSL.name(aggTable)))
                .where(
                        field(Constants.TABLE_USER_ONE_ID).eq(oneId),
                        field("call_date").lessOrEqual(endDate),
                        field("call_date").greaterOrEqual(startDate)
                )
                .getSQL();
    }

    public String generateInsertUserMetricSql(String tableName, AiobAggMetricCal metricCal) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);

        return dsl.insertInto(table(name(tableName)))
                .columns(field(name(Constants.TABLE_USER_ONE_ID)),
                        field(name("connect_rate")),
                        field(name("time_bucket_statistics")),
                        field(name("first_round_hangup_rate")),
                        field(name("avg_rounds")),
                        field(name("avg_duration")),
                        field(name("last_call_date"))
                )
                .values(
                        metricCal.getOneId(),
                        String.format("%f", metricCal.getConnectRate()),
                        JsonUtils.toJsonWithOutException(metricCal.getTimeBucketStatistics()),
                        String.format("%f", metricCal.getFirstRoundHangupRate()),
                        String.format("%f", metricCal.getAvgRounds()),
                        String.format("%f", metricCal.getAvgDuration()),
                        metricCal.getLastCallDate()
                ).getSQL();
    }

    public String clearMemoryExtractResult(String tableName, String oneId) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        return dsl.delete(DSL.table(DSL.name(tableName)))
                .where(DSL.field(Constants.TABLE_USER_ONE_ID).eq(oneId))
                .getSQL();
    }

    public String generateWholeNodeMetric(String tableName, String taskId, String robotId, String robotVer, String topicId, Date startTime, Date endTime) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);

        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("task_id")).eq(taskId));
        conditions.add(field(name("node_id")).notEqual(""));
        if (StringUtils.isNotBlank(robotId)) {
            conditions.add(field(name("robot_id")).eq(robotId));
        }
        if (StringUtils.isNotBlank(robotVer)) {
            conditions.add(field(name("robot_ver")).eq(robotVer));
        }
        if (StringUtils.isNotBlank(topicId)) {
            conditions.add(field(name("topic_id")).eq(topicId));
        }
        if (Objects.nonNull(startTime)) {
            conditions.add(field(name("cal_date")).greaterOrEqual(DatetimeUtils.formatDate(startTime)));
        }
        if (Objects.nonNull(endTime)) {
            conditions.add(field(name("cal_date")).lessThan(DatetimeUtils.formatDate(endTime)));
        }

        return dsl.select(
                        field(name("node_id")),
                        countDistinct(field(name(Constants.TABLE_USER_ONE_ID))).as("node_uv"),
                        count(field(name(Constants.TABLE_USER_ONE_ID))).as("node_pv")
                ).from(table(name(tableName)))
                .where(conditions.toArray(new Condition[0]))
                .groupBy(field(name("node_id")))
                .getSQL();
    }

    public String generateWholeNodeHangupMetric(String tableName, String taskId, String robotId, String robotVer, String topicId, Date startTime, Date endTime) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);

        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("task_id")).eq(taskId));
        conditions.add(field(name("hangup")).eq(1));
        if (StringUtils.isNotBlank(robotId)) {
            conditions.add(field(name("robot_id")).eq(robotId));
        }
        if (StringUtils.isNotBlank(robotVer)) {
            conditions.add(field(name("robot_ver")).eq(robotVer));
        }
        if (StringUtils.isNotBlank(topicId)) {
            conditions.add(field(name("topic_id")).eq(topicId));
        }
        if (Objects.nonNull(startTime)) {
            conditions.add(field(name("cal_date")).greaterOrEqual(DatetimeUtils.formatDate(startTime)));
        }
        if (Objects.nonNull(endTime)) {
            conditions.add(field(name("cal_date")).lessThan(DatetimeUtils.formatDate(endTime)));
        }

        return dsl.select(
                        field(name("node_id")),
                        countDistinct(field(name(Constants.TABLE_USER_ONE_ID))).as("node_uv"),
                        count(field(name(Constants.TABLE_USER_ONE_ID))).as("node_pv")
                ).from(table(name(tableName)))
                .where(conditions.toArray(new Condition[0]))
                .groupBy(field(name("node_id")))
                .getSQL();
    }

    public String generateWholeEdgeMetric(String tableName, String taskId, String robotId, String robotVer, String topicId, Date startTime, Date endTime) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);

        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("task_id")).eq(taskId));
        if (StringUtils.isNotBlank(robotId)) {
            conditions.add(field(name("robot_id")).eq(robotId));
        }
        if (StringUtils.isNotBlank(robotVer)) {
            conditions.add(field(name("robot_ver")).eq(robotVer));
        }
        if (StringUtils.isNotBlank(topicId)) {
            conditions.add(field(name("topic_id")).eq(topicId));
        }
        if (Objects.nonNull(startTime)) {
            conditions.add(field(name("cal_date")).greaterOrEqual(DatetimeUtils.formatDate(startTime)));
        }
        if (Objects.nonNull(endTime)) {
            conditions.add(field(name("cal_date")).lessThan(DatetimeUtils.formatDate(endTime)));
        }

        return dsl.select(
                        field(name("from_node")),
                        field(name("end_node")),
                        count(field(name(Constants.TABLE_USER_ONE_ID))).as("edge_count"),
                        countDistinct(field(name(Constants.TABLE_USER_ONE_ID))).as("edge_count_uv")
                ).from(table(name(tableName)))
                .where(conditions.toArray(new Condition[0]))
                .groupBy(field(name("from_node")), field(name("end_node")))
                .getSQL();
    }

    public String generateWholeIntentMetric(String tableName, List<String> endNodeIds, String taskId, String robotId,
                                            String robotVer, String topicId, Date startTime, Date endTime) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);

        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("task_id")).eq(taskId));
        if (CollectionUtils.isNotEmpty(endNodeIds)) {
            conditions.add(field(name("node_id")).in(endNodeIds));
        }
        if (StringUtils.isNotBlank(robotId)) {
            conditions.add(field(name("robot_id")).eq(robotId));
        }
        if (StringUtils.isNotBlank(robotVer)) {
            conditions.add(field(name("robot_ver")).eq(robotVer));
        }
        if (StringUtils.isNotBlank(topicId)) {
            conditions.add(field(name("topic_id")).eq(topicId));
        }
        if (Objects.nonNull(startTime)) {
            conditions.add(field(name("cal_date")).greaterOrEqual(DatetimeUtils.formatDate(startTime)));
        }
        if (Objects.nonNull(endTime)) {
            conditions.add(field(name("cal_date")).lessThan(DatetimeUtils.formatDate(endTime)));
        }

        String sql = dsl.select(
                        field(name("node_id")),
                        field(name("tag")),
                        countDistinct(field(name(Constants.TABLE_USER_ONE_ID))).as("node_uv"),
                        count(field(name(Constants.TABLE_USER_ONE_ID))).as("node_pv")
                ).from(table(name(tableName)))
                .where(conditions.toArray(new Condition[0]))
                .groupBy(field(name("node_id")), field(name("tag")))
                .getSQL();

        return sql.replace(String.format("`%s`", tableName), String.format("`%s` LATERAL VIEW EXPLODE(intent) tag_table AS tag", tableName));
    }

    public String generateGetAllFromNodesSQL(String tableName, String currNodeId, String taskId, String robotId,
                                             String robotVer, String topicId, Date startTime, Date endTime) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);

        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("end_node")).eq(currNodeId));
        conditions.add(field(name("task_id")).eq(taskId));
        if (StringUtils.isNotBlank(robotId)) {
            conditions.add(field(name("robot_id")).eq(robotId));
        }
        if (StringUtils.isNotBlank(robotVer)) {
            conditions.add(field(name("robot_ver")).eq(robotVer));
        }
        if (StringUtils.isNotBlank(topicId)) {
            conditions.add(field(name("topic_id")).eq(topicId));
        }
        if (Objects.nonNull(startTime)) {
            conditions.add(field(name("cal_date")).greaterOrEqual(DatetimeUtils.formatDate(startTime)));
        }
        if (Objects.nonNull(endTime)) {
            conditions.add(field(name("cal_date")).lessThan(DatetimeUtils.formatDate(endTime)));
        }

        return dsl.selectDistinct(field(name("from_node")))
                .from(table(name(tableName)))
                .where(conditions.toArray(new Condition[0]))
                .getSQL();
    }

    public String generateWholeNodeMetricWithIdsSQL(String tableName, List<String> nodeIds, String taskId, String robotId,
                                                    String robotVer, String topicId, Date startTime, Date endTime) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);

        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("task_id")).eq(taskId));
        conditions.add(field(name("node_id")).in(nodeIds));
        if (StringUtils.isNotBlank(robotId)) {
            conditions.add(field(name("robot_id")).eq(robotId));
        }
        if (StringUtils.isNotBlank(robotVer)) {
            conditions.add(field(name("robot_ver")).eq(robotVer));
        }
        if (StringUtils.isNotBlank(topicId)) {
            conditions.add(field(name("topic_id")).eq(topicId));
        }
        if (Objects.nonNull(startTime)) {
            conditions.add(field(name("cal_date")).greaterOrEqual(DatetimeUtils.formatDate(startTime)));
        }
        if (Objects.nonNull(endTime)) {
            conditions.add(field(name("cal_date")).lessThan(DatetimeUtils.formatDate(endTime)));
        }

        return dsl.select(
                        field(name("node_id")),
                        countDistinct(field(name(Constants.TABLE_USER_ONE_ID))).as("node_uv"),
                        count(field(name(Constants.TABLE_USER_ONE_ID))).as("node_pv")
                ).from(table(name(tableName)))
                .where(conditions.toArray(new Condition[0]))
                .groupBy(field(name("node_id")))
                .getSQL();
    }

    public String generateCurrNodeToForwardNodeEdgeMetricSQL(String tableName, String currNodeId,
                                                             String taskId, String robotId, String robotVer, String topicId, Date startTime, Date endTime) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);

        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("task_id")).eq(taskId));
        conditions.add(field(name("from_node")).eq(currNodeId));
        if (StringUtils.isNotBlank(robotId)) {
            conditions.add(field(name("robot_id")).eq(robotId));
        }
        if (StringUtils.isNotBlank(robotVer)) {
            conditions.add(field(name("robot_ver")).eq(robotVer));
        }
        if (StringUtils.isNotBlank(topicId)) {
            conditions.add(field(name("topic_id")).eq(topicId));
        }
        if (Objects.nonNull(startTime)) {
            conditions.add(field(name("cal_date")).greaterOrEqual(DatetimeUtils.formatDate(startTime)));
        }
        if (Objects.nonNull(endTime)) {
            conditions.add(field(name("cal_date")).lessThan(DatetimeUtils.formatDate(endTime)));
        }

        return dsl.select(
                        field(name("end_node")),
                        countDistinct(field(name(Constants.TABLE_USER_ONE_ID))).as("node_uv"),
                        count(field(name(Constants.TABLE_USER_ONE_ID))).as("node_pv")
                ).from(table(name(tableName)))
                .where(conditions.toArray(new Condition[0]))
                .groupBy(field(name("end_node")))
                .getSQL();
    }

    public String generatePreviewNodeToCurrNodeEdgeMetricSQL(String tableName, List<String> previewNodeIds, String currNodeId,
                                                             String taskId, String robotId, String robotVer, String topicId, Date startTime, Date endTime) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);

        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("task_id")).eq(taskId));
        conditions.add(field(name("from_node")).in(previewNodeIds));
        conditions.add(field(name("end_node")).in(currNodeId));
        if (StringUtils.isNotBlank(robotId)) {
            conditions.add(field(name("robot_id")).eq(robotId));
        }
        if (StringUtils.isNotBlank(robotVer)) {
            conditions.add(field(name("robot_ver")).eq(robotVer));
        }
        if (StringUtils.isNotBlank(topicId)) {
            conditions.add(field(name("topic_id")).eq(topicId));
        }
        if (Objects.nonNull(startTime)) {
            conditions.add(field(name("cal_date")).greaterOrEqual(DatetimeUtils.formatDate(startTime)));
        }
        if (Objects.nonNull(endTime)) {
            conditions.add(field(name("cal_date")).lessThan(DatetimeUtils.formatDate(endTime)));
        }

        return dsl.select(
                        field(name("from_node")),
                        count(field(name(Constants.TABLE_USER_ONE_ID))).as("edge_count"),
                        countDistinct(field(name(Constants.TABLE_USER_ONE_ID))).as("edge_count_uv")
                ).from(table(name(tableName)))
                .where(conditions.toArray(new Condition[0]))
                .groupBy(field(name("from_node")))
                .getSQL();
    }

    public String generateDailyCurrNodeToForwardNodeEdgeMetricSQL(String tableName, String currNodeId,
                                                                  String taskId, String robotId, String robotVer, String topicId, Date startTime, Date endTime) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);

        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("task_id")).eq(taskId));
        conditions.add(field(name("from_node")).eq(currNodeId));
        if (StringUtils.isNotBlank(robotId)) {
            conditions.add(field(name("robot_id")).eq(robotId));
        }
        if (StringUtils.isNotBlank(robotVer)) {
            conditions.add(field(name("robot_ver")).eq(robotVer));
        }
        if (StringUtils.isNotBlank(topicId)) {
            conditions.add(field(name("topic_id")).eq(topicId));
        }
        if (Objects.nonNull(startTime)) {
            conditions.add(field(name("cal_date")).greaterOrEqual(DatetimeUtils.formatDate(startTime)));
        }
        if (Objects.nonNull(endTime)) {
            conditions.add(field(name("cal_date")).lessThan(DatetimeUtils.formatDate(endTime)));
        }

        String sql = dsl.select(
                        field(name("end_node")),
                        field(name("cal_date")).as("day"),
                        countDistinct(field(name(Constants.TABLE_USER_ONE_ID))).as("node_uv"),
                        count(field(name(Constants.TABLE_USER_ONE_ID))).as("node_pv")
                ).from(table(name(tableName)))
                .where(conditions.toArray(new Condition[0]))
                .groupBy(field(name("end_node")), field(name("day")))
                .getSQL();

        return sql.replace("`cal_date` as `day`", "to_date(`cal_date`) as `day`");
    }

    public String generateCountSessionConnectedCallSQL(String tableName, String taskId, String robotId, String robotVer) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);

        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("taskId")).eq(taskId));
        conditions.add(field(name("sipCode")).eq("200"));
        if (StringUtils.isNotBlank(robotId)) {
            conditions.add(field(name("robotId")).eq(robotId));
        }
        if (StringUtils.isNotBlank(robotVer)) {
            conditions.add(field(name("botVersionId")).eq(robotVer));
        }
        return dsl.select(
                        countDistinct(field(name("sessionId"))).as("count")
                )
                .from(table(name(tableName)))
                .where(conditions.toArray(new Condition[0]))
                .getSQL();
    }

    public String generateCountAnalysedMetricSQL(
            String sessionTableName, String nodeMetricTableName, String taskId, String robotId, String robotVer) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);

        Table<Record> nt = table(name(nodeMetricTableName)).as("n");

        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("n", "task_id")).eq(taskId));
        conditions.add(field(name("n", "robot_id")).eq(robotId));
        conditions.add(field(name("n", "robot_ver")).eq(robotVer));

        return dsl.select(
                        countDistinct(field(name("n", "sessionId"))).as("count")
                )
                .from(nt)
                .where(conditions.toArray(new Condition[0]))
                .getSQL();
    }

    public String generateDailyCurrNodeMetric(String tableName, String currNodeId, String taskId, String robotId,
                                              String robotVer, String topicId, Date startTime, Date endTime) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);

        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("task_id")).eq(taskId));
        conditions.add(field(name("node_id")).eq(currNodeId));
        if (StringUtils.isNotBlank(robotId)) {
            conditions.add(field(name("robot_id")).eq(robotId));
        }
        if (StringUtils.isNotBlank(robotVer)) {
            conditions.add(field(name("robot_ver")).eq(robotVer));
        }
        if (StringUtils.isNotBlank(topicId)) {
            conditions.add(field(name("topic_id")).eq(topicId));
        }
        if (Objects.nonNull(startTime)) {
            conditions.add(field(name("cal_date")).greaterOrEqual(DatetimeUtils.formatDate(startTime)));
        }
        if (Objects.nonNull(endTime)) {
            conditions.add(field(name("cal_date")).lessThan(DatetimeUtils.formatDate(endTime)));
        }

        String sql = dsl.select(
                        field(name("cal_date")).as("day"),
                        countDistinct(field(name(Constants.TABLE_USER_ONE_ID))).as("node_uv"),
                        count(field(name(Constants.TABLE_USER_ONE_ID))).as("node_pv")
                ).from(table(name(tableName)))
                .where(conditions.toArray(new Condition[0]))
                .groupBy(field(name("day")))
                .getSQL();

        return sql.replace("`cal_date` as `day`", "to_date(`cal_date`) as `day`");
    }

    public String generateNodeMetricWithIdsSQL(String tableName, List<String> nodeIds, String taskId, String robotId,
                                               String robotVer, String topicId, Date startTime, Date endTime) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);

        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("task_id")).eq(taskId));
        conditions.add(field(name("node_id")).in(nodeIds));
        if (StringUtils.isNotBlank(robotId)) {
            conditions.add(field(name("robot_id")).eq(robotId));
        }
        if (StringUtils.isNotBlank(robotVer)) {
            conditions.add(field(name("robot_ver")).eq(robotVer));
        }
        if (StringUtils.isNotBlank(topicId)) {
            conditions.add(field(name("topic_id")).eq(topicId));
        }
        if (Objects.nonNull(startTime)) {
            conditions.add(field(name("cal_date")).greaterOrEqual(DatetimeUtils.formatDate(startTime)));
        }
        if (Objects.nonNull(endTime)) {
            conditions.add(field(name("cal_date")).lessThan(DatetimeUtils.formatDate(endTime)));
        }

        return dsl.select(
                        countDistinct(field(name(Constants.TABLE_USER_ONE_ID))).as("node_uv"),
                        countDistinct(field(name("sessionId"))).as("node_pv")
                ).from(table(name(tableName)))
                .where(conditions.toArray(new Condition[0]))
                .getSQL();
    }

    public String generateClearSOPMetricDataSql(String tableName, String robotVer) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        return dsl.delete(DSL.table(DSL.name(tableName)))
                .where(field(name("robot_ver")).eq(robotVer))
                .getSQL();
    }

    /**
     * 根据手机号或userid查询用户基础信息
     *
     * @param tableName 表名
     * @param userId    mock_user表ID
     * @param mobile    手机号
     * @param secretKey 加密key
     * @return sql
     */
    public static String generateQueryUserByIdOrMobileList(String tableName, String userId, Long mobile, String secretKey) {
        List<Condition> conditions = new ArrayList<>();
        if (StrUtil.isNotBlank(userId)) {
            conditions.add(field(name("user_id")).eq(userId));
        }
        if (mobile != null) {
            // 初始为 true
            Condition condition = DSL.noCondition();
            condition = condition.and(
                    DSL.field("array_contains({0}, {1})", Boolean.class,
                            DSL.field("`mobile_list`"),
                            DSL.inline(AESUtils.encrypt(mobile.toString(), secretKey)))
            );
            conditions.add(condition);
        }
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        return dsl.select()
                .from(tableName)
                .where(conditions)
                .getSQL();
    }

    public String generateFlexibleIdTransferSql(String tableName, String agentId, String versionId) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("agent_id")).eq(agentId));
        conditions.add(field(name("version_id")).eq(versionId));

        return dsl.select(
                        field(name("robot_id")),
                        field(name("robot_ver"))
                ).from(table(name(tableName)))
                .where(conditions.toArray(new Condition[0]))
                .limit(1)
                .getSQL();
    }

    public String generateQueryTaskScene(String tableName, String taskId) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("taskId")).eq(taskId));
        conditions.add(field(name("sipCode")).eq("200"));

        return dsl.select(
                        field(name("robotScene"))
                ).from(table(name(tableName)))
                .where(conditions.toArray(new Condition[0]))
                .limit(1)
                .getSQL();
    }

    public String generateCountFlexibleSessionConnectedCallSQL(
            String sessionTableName, String debugTableName, String taskId, String robotId, String robotVer) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);

        Table<Record> st = table(name(sessionTableName)).as("s");
        Table<Record> nt = table(name(debugTableName)).as("n");

        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("s", "taskId")).eq(taskId));
        conditions.add(field(name("s", "sipCode")).eq("200"));
        conditions.add(exists(
                selectOne().from(nt).where(
                        field(name("n", "sessionId")).eq(field(name("s", "sessionId"))),
                        field(name("n", "agent_id")).eq(robotId),
                        field(name("n", "version_id")).eq(robotVer)
                )
        ));

        return dsl.select(
                        countDistinct(field(name("s", "sessionId"))).as("count")
                )
                .from(st)
                .where(conditions.toArray(new Condition[0]))
                .getSQL();
    }

    public String generateCountFlexibleProcessedCallSQL(
            String sessionTableName, String nodeTableName, String taskId, String robotId, String robotVer) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);

        Table<Record> st = table(name(sessionTableName)).as("s");
        Table<Record> nt = table(name(nodeTableName)).as("n");

        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("n", "task_id")).eq(taskId));
        conditions.add(field(name("n", "robot_id")).eq(robotId));
        conditions.add(field(name("n", "robot_ver")).eq(robotVer));

        return dsl.select(
                        countDistinct(field(name("s", "sessionId"))).as("count")
                )
                .from(st)
                .join(nt)
                .on(field(name("n", "sessionId")).eq(field(name("s", "sessionId"))))
                .where(conditions.toArray(new Condition[0]))
                .getSQL();
    }

    public String generateCountSopUserDetailSQL(String nodeTableName, String taskId, String robotId, String robotVer, Date startTime, Date endTime, String nodeId) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        Table<Record> nt = table(name(nodeTableName));
        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("node_id")).eq(nodeId));
        conditions.add(field(name("task_id")).eq(taskId));
        conditions.add(field(name("robot_id")).eq(robotId));
        conditions.add(field(name("robot_ver")).eq(robotVer));
        if (Objects.nonNull(startTime)) {
            conditions.add(field(name("cal_date")).greaterOrEqual(DatetimeUtils.formatDate(startTime)));
        }
        if (Objects.nonNull(endTime)) {
            conditions.add(field(name("cal_date")).lessThan(DatetimeUtils.formatDate(endTime)));
        }

        return dsl.select(countDistinct(field(name(Constants.TABLE_USER_ONE_ID))).as("count"))
                .from(nt)
                .where(conditions.toArray(new Condition[0]))
                .getSQL();
    }

    public String generateCountSopUserDetailWithIntentSQL(String nodeTableName, String taskId, String robotId, String robotVer, Date startTime, Date endTime, String intent) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        Table<Record> nt = table(name(nodeTableName));
        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("intent")).eq(intent));
        conditions.add(field(name("task_id")).eq(taskId));
        conditions.add(field(name("robot_id")).eq(robotId));
        conditions.add(field(name("robot_ver")).eq(robotVer));
        if (Objects.nonNull(startTime)) {
            conditions.add(field(name("cal_date")).greaterOrEqual(DatetimeUtils.formatDate(startTime)));
        }
        if (Objects.nonNull(endTime)) {
            conditions.add(field(name("cal_date")).lessThan(DatetimeUtils.formatDate(endTime)));
        }

        String sql = dsl.select(countDistinct(field(name(Constants.TABLE_USER_ONE_ID))).as("count"))
                .from(nt)
                .where(conditions.toArray(new Condition[0]))
                .getSQL();
        sql = StringUtils.replaceOnce(sql,
                String.format("`intent` = '%s'", intent),
                String.format("array_contains(`intent`, '%s')", intent)
        );
        return sql;
    }


    public String generateQuerySopUserDetailSQL(String nodeTableName, String taskId, String robotId, String robotVer,
                                                Date startTime, Date endTime, String nodeId, Integer pageNo, Integer pageSize) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        Table<Record> nt = table(name(nodeTableName));
        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("node_id")).eq(nodeId));
        conditions.add(field(name("task_id")).eq(taskId));
        conditions.add(field(name("robot_id")).eq(robotId));
        conditions.add(field(name("robot_ver")).eq(robotVer));
        if (Objects.nonNull(startTime)) {
            conditions.add(field(name("cal_date")).greaterOrEqual(DatetimeUtils.formatDate(startTime)));
        }
        if (Objects.nonNull(endTime)) {
            conditions.add(field(name("cal_date")).lessThan(DatetimeUtils.formatDate(endTime)));
        }

        String sql = dsl.select(
                        field(name(Constants.TABLE_USER_ONE_ID)),
                        field(name("sessionId")).as("sessionIds")
                )
                .from(nt)
                .where(conditions.toArray(new Condition[0]))
                .groupBy(field(name(Constants.TABLE_USER_ONE_ID)))
                .orderBy(field(name(Constants.TABLE_USER_ONE_ID)).asc())
                .limit((pageNo - 1) * pageSize, pageSize)
                .getSQL();
        sql = StringUtils.replaceOnce(sql,
                "`sessionId` as `sessionIds`",
                "GROUP_CONCAT(`sessionId`, ',') as `sessionIds`"
        );
        return sql;
    }

    public String generateQuerySopUserDetailWithIntentSQL(String nodeTableName, String taskId, String robotId, String robotVer,
                                                          Date startTime, Date endTime, String intent, Integer pageNo, Integer pageSize) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        Table<Record> nt = table(name(nodeTableName));
        List<Condition> conditions = Lists.newArrayList();
        conditions.add(field(name("intent")).eq(intent));
        conditions.add(field(name("task_id")).eq(taskId));
        conditions.add(field(name("robot_id")).eq(robotId));
        conditions.add(field(name("robot_ver")).eq(robotVer));
        if (Objects.nonNull(startTime)) {
            conditions.add(field(name("cal_date")).greaterOrEqual(DatetimeUtils.formatDate(startTime)));
        }
        if (Objects.nonNull(endTime)) {
            conditions.add(field(name("cal_date")).lessThan(DatetimeUtils.formatDate(endTime)));
        }

        String sql = dsl.select(
                        field(name(Constants.TABLE_USER_ONE_ID)),
                        field(name("sessionId")).as("sessionIds")
                )
                .from(nt)
                .where(conditions.toArray(new Condition[0]))
                .groupBy(field(name(Constants.TABLE_USER_ONE_ID)))
                .orderBy(field(name(Constants.TABLE_USER_ONE_ID)).asc())
                .limit((pageNo - 1) * pageSize, pageSize)
                .getSQL();
        sql = StringUtils.replaceOnce(sql,
                String.format("`intent` = '%s'", intent),
                String.format("array_contains(`intent`, '%s')", intent)
        );
        sql = StringUtils.replaceOnce(sql,
                "`sessionId` as `sessionIds`",
                "GROUP_CONCAT(`sessionId`, ',') as `sessionIds`"
        );
        return sql;
    }

    public String generateQuerySessionStartTimeWithIdsSQL(String sessionTable, List<String> sessionIds) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        return dsl.select(
                        field(name("sessionId")),
                        field(name("startTime")),
                        field(name("mobile")),
                        field(name("oneId"))
                ).from(table(name(sessionTable)))
                .where(field(name("sessionId")).in(sessionIds))
                .getSQL();
    }

    /**
     * 生成查询外呼record表nodeInfo的SQL
     * 用于小秘书识别的前置条件检查
     *
     * @param recordTableName record表名
     * @param sessionId 会话ID
     * @return SQL语句
     */
    public String generateQueryRecordNodeInfoSQL(String recordTableName, String sessionId) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        List<Condition> conditions = Lists.newArrayList();

        // 基本条件
        conditions.add(field(name("sessionId")).eq(sessionId));
        conditions.add(field(name("nodeInfo")).isNotNull());
        conditions.add(field(name("nodeInfo")).ne(""));

        return dsl.select(field(name("nodeInfo")))
                .from(table(name(recordTableName)))
                .where(conditions.toArray(new Condition[0]))
                .orderBy(field(name("createTime")).desc())
                .limit(5)
                .getSQL();
    }

    /**
     * 呼通率核心统计指标
     * 拨通数、接通数、接通率
     *
     * @param tableName 表明
     * @param startTime 统计开始时间
     * @param endTime   统计结束时间
     * @return SQL
     */
    public static String generateCallAnalysisCoreMetrics(String tableName, Date startTime, Date endTime) {
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        Field<Integer> dialCount = count().as("dial_count");
        Field<Object> connectedCount = field("SUM(IF(sipCode = '200', 1, 0))").as("connected_count");
        Field<Object> connectRate = field("ROUND(SUM(IF(sipCode = '200', 1, 0 )) / COUNT(*), 2)").as("connect_rate");
        return dsl.select(dialCount, connectedCount, connectRate)
                .from(table(name(tableName)))
                .where(field(name("deepsight_datetime")).ge(DatetimeUtils.formatDate(startTime)))
                .and(field(name("deepsight_datetime")).lt(DatetimeUtils.formatDate(endTime)))
                .getSQL();
    }

    /**
     * 呼通分析-呼通趋势SQL
     * 支持手动指定时间范围
     * 支持手动查询指定号线、任务、机器人
     *
     * @param request  eq
     * @param tenantId 租户名
     * @return
     */
    public static String generateConnectionRateTrend(ConnectionRateTrendRequest request, String tenantId) {
        Date startTime = request.getStartTime();
        Date endTime = request.getEndTime();
        String fieldName = request.getTargetFieldName();
        List<String> configTargets = request.getConfigTargets();
        String tableName = TenantUtils.generateAiobSessionTableName(tenantId);
        List<Field> select = new ArrayList<>();
        List<Field> groupBy = new ArrayList<>();
        List<Condition> where = new ArrayList<>();
        String timeName = "dataTime";
        // 默认按照24小时区间统计，按照小时统计
        Date now = new Date();
        Date start = startTime != null ? startTime : DateUtil.offsetDay(now, -1);
        Date end = endTime != null ? endTime : now;
        Field<Object> timeField = field("DATE_FORMAT(deepsight_datetime, '%Y-%m-%d %H:00:00')").as(timeName);
        Field<Object> groupField = field(name(timeName));
        // 统计近30天，按照天统计
        if (Objects.equals(DateLineTypeEnum.DAY, request.getType())) {
            start = startTime != null ? startTime : DateUtil.offsetDay(now, -30);
            timeField = field("DATE(deepsight_datetime)").as(timeName);
        }
        // 指定类型code批量查询
        if (StrUtil.isNotBlank(fieldName) && CollUtil.isNotEmpty(configTargets)) {
            select.add(field(name(fieldName)).as("targetId"));
            where.add(field(name(fieldName)).in(configTargets));
            groupBy.add(field(name(fieldName)).as("targetId"));
        } else {
            select.add(count().as("dial_count"));
            select.add(field("SUM(IF(sipCode = '200', 1, 0))").as("connected_count"));
        }
        groupBy.add(groupField);
        select.add(timeField);
        select.add(field("ROUND(SUM(IF(sipCode = '200', 1, 0 )) / COUNT(*), 2)").as("connect_rate"));
        where.add(field(name("deepsight_datetime")).ge(DatetimeUtils.formatDate(start)));
        where.add(field(name("deepsight_datetime")).lt(DatetimeUtils.formatDate(end)));
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        return dsl.select(select)
                .from(table(name(tableName)))
                .where(where)
                .groupBy(groupBy)
                .getSQL();
    }


    /**
     * 统计告警天数
     *
     * @param tenantId     租户ID
     * @param configType   配置类型：LING、TASK、ROBOT
     * @param configTarget 配置目标ID：callNum\task_id\robot_id
     * @param startTime    统计起始时间
     * @param endTime      统计结束时间
     * @return 告警条数SQL
     */
    public static String generateQueryAiobAlertDaysSql(String tenantId, AlertConfigTypeEnum configType, String configTarget, Date startTime, Date endTime) {
        String tableName = Constants.DORIS_ALERT_RECORD;
        Field<Integer> countField = countDistinct(field(name("alarm_date"))).as("total");
        List<Condition> conditions = new ArrayList<>();
        conditions.add(condition(field(name("tenant_id")).eq(tenantId)));
        if (startTime != null) {
            conditions.add(condition(field(name("create_time")).ge(startTime)));
        }
        if (endTime != null) {
            conditions.add(condition(field(name("create_time")).lt(endTime)));
        }
        if (configType != null) {
            conditions.add(condition(field(name("config_type")).eq(configType.getValue())));
        }
        if (StrUtil.isNotBlank(configTarget)) {
            conditions.add(condition(field(name("config_target")).eq(configTarget)));
        }
        return using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS)
                .select(countField)
                .from(table(name(tableName)))
                .where(conditions)
                .getSQL();
    }

    /**
     * 外呼呼通率30天排行榜分页查询
     *
     * @param request   请求体
     * @param tableName 表名
     * @param tenantId  租户ID
     * @return SQL
     */
    public static String generateAiobThirtyDayRankingPageSql(ThirtyDayRankingRequest request, String tableName, String tenantId) {
        int offset = request.getOffset();
        Integer pageSize = request.getPageSize();
        final String dialCountStr = "dial_count";
        final String connectedCountStr = "connected_count";
        final String connectRateStr = "connect_rate";
        final String alarmDaysStr = "alarmDays";
        final String unConnectedCountStr = "un_connected_count";
        final String lineStr = "lineReason";
        final String platFormStr = "platFormReason";
        DateTime endTime = request.getEndTime();
        DateTime startTime = request.getStartTime();
        // session条件字段
        List<Condition> sessionConditions = new ArrayList<>();
        if (request.getSource() != null) {
            sessionConditions.add(condition(field(name("didOwner")).eq(request.getSource().getCode())));
        }
        if (request.getLineStatus() != null) {
            sessionConditions.add(condition(field(name("lineStatus")).eq(request.getLineStatus().getValue())));
        }
        if (request.getTaskStatus() != null) {
            sessionConditions.add(condition(field(name("taskStatus")).eq(request.getTaskStatus().getCode())));
        }
        if (request.getRobotType() != null) {
            sessionConditions.add(condition(field(name("robotScene")).eq(request.getRobotType().getCode())));
        }
        sessionConditions.add(condition(field(name("deepsight_datetime")).ge(startTime)));
        sessionConditions.add(condition(field(name("deepsight_datetime")).lt(endTime)));
        // session 查询字段：分组属性字段、特殊聚合字段
        List<Field> selectFields = new ArrayList<>();
        List<Field> groupFields = new ArrayList<>();
        AlertConfigTypeEnum type = request.getType();
        switch (type) {
            case LINE -> {
                selectFields.add(field(name("callerNum")));
                selectFields.add(field("ROUND(SUM(IF(`dicCategory`='号线原因未接通', 1, 0))/SUM(IF(`endType` = 0, 1, 0)),2)").as(lineStr));
                groupFields.add(field(name("callerNum")));
                sessionConditions.add(condition(field(name("callerNum")).isNotNull()));
            }
            case TASK -> {
                selectFields.add(field(name("taskId")));
                selectFields.add(field("ROUND(SUM(IF(`dicCategory`='平台规则限制未接通', 1, 0))/SUM(IF(`endType` = 0, 1, 0)),2)").as(platFormStr));
                groupFields.add(field(name("taskId")));
                sessionConditions.add(condition(field(name("taskId")).isNotNull()));
            }
            case ROBOT -> {
                selectFields.add(field(name("robotId")));
                groupFields.add(field(name("robotId")));
                sessionConditions.add(condition(field(name("robotId")).isNotNull()));
            }
        }
        // 通用select 聚合字段
        selectFields.add(count().as(dialCountStr));
        selectFields.add(field("SUM(IF(`sipCode` = '200', 1, 0))").as(connectedCountStr));
        selectFields.add(field("SUM(IF(`endType` = 0, 1, 0))").as(unConnectedCountStr));
        selectFields.add(field("ROUND(SUM(IF(`sipCode` = '200', 1, 0 )) / COUNT(*), 2)").as(connectRateStr));
        selectFields.set(0, selectFields.get(0).as("id"));
        // session 分组
        Table<Record> sessionTableTem = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS)
                .select(selectFields)
                .from(table(name(tableName)))
                .where(sessionConditions)
                .groupBy(groupFields)
                .asTable();
        // 查询告警表
        Table<Record2<Object, Integer>> alarmTableTem = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS)
                .select(
                        field(name("config_target")).as("configTarget"),
                        countDistinct(field(name("alarm_date"))).as(alarmDaysStr)
                )
                .from(name(Constants.DORIS_ALERT_RECORD))
                .where(condition(field(name("config_type")).eq(type.getValue())),
                        condition(field(name("create_time")).ge(startTime)),
                        condition(field(name("create_time")).lt(endTime))
                )
                .and(field(name("tenant_id")).eq(tenantId))
                .groupBy(field(name("config_target")))
                .asTable();
        // 联表查询
        SelectOnConditionStep<Record> conditionStep = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS)
                .select()
                .from(sessionTableTem)
                .leftJoin(alarmTableTem)
                .on(sessionTableTem.field(selectFields.get(0)).eq(alarmTableTem.field(field(name("configTarget")))));
        String finalSql = null;
        // 排序字段
        Field<Object> sortField = null;
        AiobSortFieldEnum sortFieldEnum = request.getSortField();
        if (sortFieldEnum != null) {
            switch (sortFieldEnum) {
                case CALL_COUNT -> sortField = field(name(dialCountStr));
                case CONNECTED_COUNT -> sortField = field(name(connectedCountStr));
                case CONNECTED_RATE -> sortField = field(name(connectRateStr));
                case ALARM_DAYS -> sortField = field(name(alarmDaysStr));
                case UNCONNECTED_COUNT -> sortField = field(name(unConnectedCountStr));
                case LINE_REASON_RATE -> {
                    if (Objects.equals(type, AlertConfigTypeEnum.LINE)) {
                        sortField = field(name(lineStr));
                    }
                }
                case PLATFORM_REASON_RATE -> {
                    if (Objects.equals(type, AlertConfigTypeEnum.TASK)) {
                        sortField = field(name(platFormStr));
                    }
                }
            }
        }
        Field idField = selectFields.get(0);
        if (sortField != null) {
            SortTypeEnum sortType = request.getSortOrder() == null ? SortTypeEnum.ASC : request.getSortOrder();
            switch (sortType) {
                case ASC ->
                        finalSql = conditionStep.orderBy(sortField.asc(), idField.asc()).limit(offset, pageSize).getSQL();
                case DESC ->
                        finalSql = conditionStep.orderBy(sortField.desc(), idField.asc()).limit(offset, pageSize).getSQL();
            }
        } else {
            finalSql = conditionStep.orderBy(idField.asc()).limit(offset, pageSize).getSQL();
        }
        return finalSql;
    }

    /**
     * 外呼呼通率30天排行榜计数
     *
     * @param request   请求体
     * @param tableName 表名
     * @param tenantId  租户ID
     * @return SQL
     */
    public static String generateAiobThirtyDayRankingCountSql(ThirtyDayRankingRequest request, String tableName, String tenantId) {
        // 计算偏移时间
        DateTime endTime = request.getEndTime();
        DateTime startTime = request.getStartTime();
        // session条件字段
        List<Condition> sessionConditions = new ArrayList<>();
        if (request.getSource() != null) {
            sessionConditions.add(condition(field(name("didOwner")).eq(request.getSource().getCode().toString())));
        }
        if (request.getLineStatus() != null) {
            sessionConditions.add(condition(field(name("lineStatus")).eq(request.getLineStatus().getValue())));
        }
        if (request.getTaskStatus() != null) {
            sessionConditions.add(condition(field(name("taskStatus")).eq(request.getTaskStatus().getCode())));
        }
        if (request.getRobotType() != null) {
            sessionConditions.add(condition(field(name("robotScene")).eq(request.getRobotType().getCode())));
        }
        sessionConditions.add(condition(field(name("deepsight_datetime")).ge(startTime)));
        sessionConditions.add(condition(field(name("deepsight_datetime")).lt(endTime)));
        // session 查询字段：分组属性字段、特殊聚合字段
        List<Field> groupFields = new ArrayList<>();
        AlertConfigTypeEnum type = request.getType();
        switch (type) {
            case LINE -> {
                groupFields.add(field(name("callerNum")));
                sessionConditions.add(condition(field(name("callerNum")).isNotNull()));
            }
            case TASK -> {
                groupFields.add(field(name("taskId")));
                sessionConditions.add(condition(field(name("taskId")).isNotNull()));
            }
            case ROBOT -> {
                groupFields.add(field(name("robotId")));
                sessionConditions.add(condition(field(name("robotId")).isNotNull()));
            }
        }
        Table<Record1<Object>> table = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS)
                .select(field("1"))
                .from(table(name(tableName)))
                .where(sessionConditions)
                .groupBy(groupFields)
                .asTable();
        return using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS)
                .select(count())
                .from(table)
                .getSQL();
    }


    /**
     * 统计session表taskIds总数
     *
     * @param tableName 表名
     * @return SQL
     */
    public static String generateCountSessionTaskIds(String tableName) {
        Field<Object> field = field(name("taskId"));
        return using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS)
                .select(count(field))
                .from(name(tableName))
                .groupBy(field)
                .orderBy(field)
                .getSQL();
    }

    /**
     * 查询外呼记录表taskIds
     *
     * @param tableName 表名
     * @param offset    偏移
     * @param pageSize  查询条数
     * @return SQL
     */
    public static String generateQuerySessionTaskIds(String tableName, int offset, int pageSize) {
        Field<Object> field = field(name("taskId"));
        return using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS)
                .select(field)
                .from(name(tableName))
                .groupBy(field)
                .orderBy(field)
                .limit(offset, pageSize)
                .getSQL();
    }

    /**
     * 条件更新外呼记录表任务信息
     *
     * @param tableName  表名
     * @param taskId     任务ID
     * @param statusEnum 状态枚举
     * @param taskName   任务名
     * @return
     */
    public static String generateSessionTaskInfoUpdate(String tableName, Long taskId, AiobTaskStatusEnum statusEnum, String taskName) {
        UpdateSetMoreStep<Record> updateSetMoreStep = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS)
                .update(table(name(tableName)))
                .set(field(name("taskStatus")), statusEnum.getCode())
                .set(field(name("updateTime")), new DateTime());
        if (StrUtil.isNotBlank(taskName)) {
            updateSetMoreStep = updateSetMoreStep.set(field(name("taskName")), taskName);
        }
        return updateSetMoreStep.where(condition(field(name("taskId")).eq(taskId)))
                .getSQL();
    }

    /**
     * 统计告警配置下的接通率数据
     *
     * @param config 告警配置
     * @param now    当前时间
     * @return
     */
    public static String generateConnectionRateQuery(AlertConfig config, Date now) {
        String tableName = TenantUtils.generateAiobSessionTableName(config.getTenantId());
        List<Field> select = new ArrayList<>();
        List<Condition> where = new ArrayList<>();
        AlertConfigTypeEnum typeEnum = AlertConfigTypeEnum.createByValue(config.getConfigType());
        String target = config.getConfigTarget();
        switch (typeEnum) {
            case LINE -> where.add(condition(field(name("callerNum")).eq(target)));
            case TASK -> where.add(condition(field(name("taskId")).eq(target)));
            case ROBOT -> where.add(condition(field(name("robotId")).eq(target)));
        }
        AlertTimeTypeEnum timeTypeEnum = AlertTimeTypeEnum.createByValue(config.getAlertTime());
        int offset = -1;
        DateTime end = DateUtil.beginOfDay(now);
        switch (timeTypeEnum) {
            case HOUR_24 -> {
                offset = -24;
                end = DateUtil.beginOfHour(now);
            }
            case DAY_7 -> offset = -24 * 7;
            case DAY_30 -> offset = -24 * 30;
        }
        select.add(count().as("dial_count"));
        select.add(field("ROUND(SUM(IF(sipCode = '200', 1, 0 )) / COUNT(*), 2)").as("connect_rate"));
        where.add(field(name("deepsight_datetime")).ge(DatetimeUtils.formatDate(DateUtil.offsetDay(end, offset))));
        where.add(field(name("deepsight_datetime")).lt(DatetimeUtils.formatDate(end)));
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        return dsl.select(select)
                .from(table(name(tableName)))
                .where(where)
                .getSQL();
    }

    /**
     * 写告警记录SQL
     *
     * @param config 告警配置
     * @return sql
     */
    public static String generateConnectRateAlertSave(AlertConfig config) {
        return using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS)
                .insertInto(table(name(Constants.DORIS_ALERT_RECORD)))
                .set(field(name("tenant_id")), config.getTenantId())
                .set(field(name("config_type")), config.getConfigType())
                .set(field(name("config_target")), config.getConfigTarget())
                .set(field(name("alarm_date")), new DateTime())
                .getSQL();

    }

    public static String generateQueryGetAiobConnectionRateTrendSql(ConnectionRateTrendDetailRequest request,
                                                                    String tenantId, Date now, DateTime onlineTime) {
        String lineNum = request.getLineNum() != null ? request.getLineNum() : "";

        String taskId = request.getTaskId() != null ? request.getTaskId() : "";

        String robotId = request.getRobotId() != null ? request.getRobotId() : "";

        Condition condition = DSL.noCondition();
//         保证顺序便于单测
        Map<String, String> orderedMap = new LinkedHashMap<String, String>() {{
            put("LINE", lineNum);
            put("TASK", taskId);
            put("ROBOT", robotId);
        }};
        for (Map.Entry<String, String> entry : orderedMap.entrySet()) {
            if (StringUtils.isNotBlank(entry.getValue())) {
                DialMetricQueryTypeEnum queryType = DialMetricQueryTypeEnum.queryType(entry.getKey());
                String queryField = DialMetricQueryTypeEnum.getQueryField(queryType);
                condition = condition.and(field(name(queryField)).eq(entry.getValue()));
            }
        }
        String timeType = request.getTimeType();

        Field<Object> timeField = field("DATE_FORMAT(deepsight_datetime, '%Y-%m-%d %H:00:00') AS `time`");
        Date endDate = DateUtil.beginOfHour(now);
        Date startDate = DateUtil.offsetDay(endDate, -1);
        if (timeType.equals(AlertTimeTypeEnum.DAY_30.getValue())) {
            timeField = field("DATE_FORMAT(deepsight_datetime, '%Y-%m-%d') AS `time`");
            endDate = DateUtil.beginOfDay(now);
            startDate = DateUtil.offsetDay(endDate, -30);
        }
        startDate = startDate.before(onlineTime) ? onlineTime : startDate;
        String tableName = TenantUtils.generateAiobSessionTableName(tenantId);
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        Field<BigDecimal> rateField = round(
                sum(when(field("sipCode").eq(200), 1).otherwise(0)).mul(100.0).div(count()),
                roundDecimalsTwo)
                .as("connectedCallsRate");

        return dsl.select(
                        timeField,
                        count().as("totalCalls"),
                        rateField
                )
                .from(tableName)
                .where(field("deepsight_datetime").ge(startDate))
                .and(field("deepsight_datetime").lt(endDate))
                .and(condition)
                .groupBy(field(name("time")))
                .getSQL();
    }

    public static String generateQueryAiobRobotOrTaskOrLineListSql(CallCoreMetricsRequest request,
                                                                   String tenantId,
                                                                   DialMetricQueryTypeEnum queryType, DateTime onlineTime) {
        String tableName = TenantUtils.generateAiobSessionTableName(tenantId);
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        Date now = new Date();
        boolean isHour = Objects.equals(request.getDateType(), DateLineTypeEnum.HOUR);
        int offsetDay = isHour ? -1 : -30;
        DateTime endTime = isHour ? DateUtil.beginOfHour(now) : DateUtil.beginOfDay(now);
        Date startDate = DateUtil.offsetDay(endTime, offsetDay);
        startDate = startDate.before(onlineTime) ? onlineTime : startDate;
        List<Field> select = new ArrayList<>();
        List<Condition> where = new ArrayList<>();
        where.add(getAiobDialDetailFilterField(request));
        where.add(field("deepsight_datetime").ge(startDate));
        where.add(field("deepsight_datetime").lt(endTime));
        switch (queryType) {
            case LINE -> {
                select.add(field(name("callerNum")));
                where.add(field(name("callerNum")).isNotNull());
            }
            case TASK -> {
                select.add(field(name("taskId")));
                select.add(field(name("taskName")));
            }
            case ROBOT -> {
                select.add(field(name("robotId")));
                select.add(field(name("robotName")));
            }
        }
        return dsl.selectDistinct(select)
                .from(tableName)
                .where(where)
                .getSQL();
    }

    public static String generateQueryGetRejectRatioReasonsSql(CallCoreMetricsRequest request,
                                                               String tenantId, AiobFailTypeEnum failType, Date now, DateTime onlineTime) {
        String tableName = TenantUtils.generateAiobSessionTableName(tenantId);
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        Field<Object> dicNameField = field(name("dicName"));
        Field<Integer> countField = count();
        Field<BigDecimal> ratioField = round(
                countField.cast(BigDecimal.class)
                        .div(sum(countField).over().cast(BigDecimal.class)).mul(100.0),
                roundDecimalsTwo
        );
        Date endDate = DateUtil.beginOfDay(now);
        Date startDate = DateUtil.offsetDay(endDate, -30);
        startDate = startDate.before(onlineTime) ? onlineTime : startDate;
        return dsl.select(
                        dicNameField.as("reason"),
                        countField.as("count"),
                        ratioField.as("ratio"))
                .from(tableName)
                .where(getAiobDialDetailFilterField(request))
                .and(field("deepsight_datetime").ge(startDate))
                .and(field("deepsight_datetime").lt(endDate))
                .and(field(name("dicCategory")).eq(failType.getName()))
                .groupBy(dicNameField)
                .getSQL();
    }

    public static String generateQueryGetRejectRatioReasonsByDaySql(CallCoreMetricsRequest request,
                                                                    String tenantId, AiobFailTypeEnum failType, Date now, DateTime onlineTime) {
        String tableName = TenantUtils.generateAiobSessionTableName(tenantId);
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        Field<Object> dicNameField = field(name("dicName"));
        Field<Integer> countField = count();
        Field<Object> timeField = field("DATE(deepsight_datetime)");
        Field<BigDecimal> ratioField = round(
                countField.cast(BigDecimal.class)
                        .div(sum(countField).over().partitionBy(timeField).cast(BigDecimal.class)).mul(100.0),
                roundDecimalsTwo
        );
        DateTime endDate = DateUtil.beginOfDay(now);
        Date startDate = DateUtil.offsetDay(endDate, -30);
        startDate = startDate.before(onlineTime) ? onlineTime : startDate;
        return dsl.select(
                        timeField.as("time"),
                        dicNameField.as("reason"),
                        countField.as("count"),
                        ratioField.as("ratio"))
                .from(tableName)
                .where(getAiobDialDetailFilterField(request))
                .and(field("deepsight_datetime").ge(startDate))
                .and(field("deepsight_datetime").lt(endDate))
                .and(field(name("dicCategory")).eq(failType.getName()))
                .groupBy(
                        dicNameField,
                        timeField)
                .getSQL();
    }

    public static String generateQueryGetBusyLocationDistributionSql(CallCoreMetricsRequest request,
                                                                     String tenantId, Date now, DateTime onlineTime) {

        Field<Integer> countField = DSL.count();
        Field<Object> callerCityField = field(name("callerCity"));
        Field<Object> mobileCityField = field(name("mobileCity"));
        Field<BigDecimal> sameCountField = DSL.sum(DSL.when(callerCityField.eq(mobileCityField), 1).otherwise(0));
        Field<BigDecimal> diffCountField = DSL.sum(DSL.when(callerCityField.ne(
                mobileCityField).and(mobileCityField.isNotNull()), 1).otherwise(0));
        Field<BigDecimal> nullCountField = DSL.sum(DSL.when(mobileCityField.isNull(), 1).otherwise(0));

        Field<BigDecimal> samePercentField = round(
                sameCountField.div(countField.cast(BigDecimal.class)),
                roundDecimalsTwo
        );

        Field<BigDecimal> diffPercentField = round(
                diffCountField.div(countField.cast(BigDecimal.class)),
                roundDecimalsTwo
        );

        Field<BigDecimal> nullPercentField = round(
                nullCountField.div(countField.cast(BigDecimal.class)),
                roundDecimalsTwo
        );

        String tableName = TenantUtils.generateAiobSessionTableName(tenantId);
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        Date endDate = DateUtil.beginOfDay(now);
        Date startDate = DateUtil.offsetDay(endDate, -30);
        startDate = startDate.before(onlineTime) ? onlineTime : startDate;
        return dsl.select(
                        countField.as("total"),
                        samePercentField.as("sameRatio"),
                        diffPercentField.as("differentRatio"),
                        nullPercentField.as("nullRatio")
                )
                .from(tableName)
                .where(getAiobDialDetailFilterField(request))
                .and(field(name("dicName")).eq(AiobFailReasonEnum.LINE_BUSY.getTypeName()))
                .and(field("deepsight_datetime").ge(startDate))
                .and(field("deepsight_datetime").lt(endDate))
                .getSQL();
    }

    public static String generateQueryGetUnconnectedHeatmapSql(CallCoreMetricsRequest request,
                                                               String tenantId, Date now, DateTime onlineTime) {

        Field<Integer> countField = DSL.count();
        Field<Timestamp> creatTimeField = field("deepsight_datetime", Timestamp.class);
        Field<Integer> weekField = isoDayOfWeek(creatTimeField);
        Field<Integer> hourField = hour(creatTimeField);

        String tableName = TenantUtils.generateAiobSessionTableName(tenantId);
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        DateTime endDate = DateUtil.beginOfDay(now);
        Date startDate = DateUtil.offsetDay(endDate, -30);
        startDate = startDate.before(onlineTime) ? onlineTime : startDate;
        List<String> dicNames = List.of(AiobFailReasonEnum.LINE_BUSY.getTypeName(),
                AiobFailReasonEnum.NO_ONE_HEARD.getTypeName(),
                AiobFailReasonEnum.RECV_REFUSE.getTypeName());
        return dsl.select(
                        countField.as("count"),
                        weekField.as("weekday"),
                        hourField.as("hour")
                )
                .from(tableName)
                .where(getAiobDialDetailFilterField(request))
                .and(field("deepsight_datetime").ge(startDate))
                .and(field("deepsight_datetime").lt(endDate))
                .and(condition(field(name("endType")).eq(0)))
                .and(field("dicName").in(dicNames))
                .groupBy(
                        weekField,
                        hourField
                )
                .getSQL();
    }

    public static String generateQueryGetLineRejectTrendSql(CallCoreMetricsRequest request,
                                                            String tenantId, Date now, DateTime onlineTime) {

        Field<Object> timeField = field("DATE(deepsight_datetime)");
        DateTime endDate = DateUtil.beginOfDay(now);
        Date startDate = DateUtil.offsetDay(endDate, -30);
        startDate = startDate.before(onlineTime) ? onlineTime : startDate;
        String tableName = TenantUtils.generateAiobSessionTableName(tenantId);
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        return dsl.select(
                        timeField.as("time"),
                        count().as("count")
                )
                .from(tableName)
                .where(getAiobDialDetailFilterField(request))
                .and(field("deepsight_datetime").ge(startDate))
                .and(field("deepsight_datetime").lt(endDate))
                .and(field(name("dicCategory")).eq(AiobFailTypeEnum.LINE.getName()))
                .groupBy(timeField)
                .getSQL();
    }

    public static String generateQueryGetCallResultCompositionSql(CallCoreMetricsRequest request,
                                                                  String tenantId, Date now, DateTime onlineTime) {

        Field<BigDecimal> connectField = round(
                sum(when(field("sipCode").eq(200), 1).otherwise(0)).mul(100.0).div(count()),
                roundDecimalsTwo);
        Field<BigDecimal> platfromRuleField = round(
                sum(when(field("dicCategory").eq(AiobFailTypeEnum.PLATFORM_RULE.getName()), 1).
                        otherwise(0)).mul(100.0).div(count()),
                roundDecimalsTwo);
        Field<BigDecimal> calledField = round(
                sum(when(field("dicCategory").eq(AiobFailTypeEnum.CALLED_UP.getName()), 1).
                        otherwise(0)).mul(100.0).div(count()),
                roundDecimalsTwo);
        Field<BigDecimal> lineField = round(
                sum(when(field("dicCategory").eq(AiobFailTypeEnum.LINE.getName()), 1).
                        otherwise(0)).mul(100.0).div(count()),
                roundDecimalsTwo);

        Date startDate = DateUtil.offsetDay(DateUtil.beginOfDay(now), -30);
        startDate = startDate.before(onlineTime) ? onlineTime : startDate;
        Date endDate = DateUtil.beginOfDay(now);
        String tableName = TenantUtils.generateAiobSessionTableName(tenantId);
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        return dsl.select(
                        connectField.as("已接通（含小秘书接听)"),
                        platfromRuleField.as(AiobFailTypeEnum.PLATFORM_RULE.getName()),
                        calledField.as(AiobFailTypeEnum.CALLED_UP.getName()),
                        lineField.as(AiobFailTypeEnum.LINE.getName())

                )
                .from(tableName)
                .where(getAiobDialDetailFilterField(request))
                .and(field("deepsight_datetime").ge(startDate))
                .and(field("deepsight_datetime").lt(endDate))
                .getSQL();
    }

    public static String generateQueryGetCallDetailCoreMetricsSql(CallCoreMetricsRequest request,
                                                                  String tenantId, Date startTime, DateTime endTime) {

        Field<Integer> totalCountField = count();
        Field<BigDecimal> connectCountField = sum(
                when(field("sipCode").eq(200), 1).otherwise(0));
        Field<BigDecimal> connectRateField = round(
                sum(when(field("sipCode").eq(200), 1).otherwise(0)).mul(100.0).div(count()),
                roundDecimalsTwo);
        Field<BigDecimal> unConnectCountField = sum(
                when(field("sipCode").eq(200), 0).otherwise(1));
        Field<BigDecimal> lineField = round(
                sum(when(field("dicCategory").eq(AiobFailTypeEnum.LINE.getName()), 1).
                        otherwise(0)).mul(100.0).div(sum(when(field("endType").eq(0), 1).
                        otherwise(0))),
                roundDecimalsTwo);

        String tableName = TenantUtils.generateAiobSessionTableName(tenantId);
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        return dsl.select(
                        totalCountField.as("callCount"),
                        connectCountField.as("connectedCount"),
                        unConnectCountField.as("unconnectedCount"),
                        connectRateField.as("connectedRate"),
                        lineField.as("lineReasonUnconnectedRate")
                )
                .from(tableName)
                .where(getAiobDialDetailFilterField(request))
                .and(condition(field(name("deepsight_datetime")).ge(startTime)))
                .and(condition(field(name("deepsight_datetime")).lt(endTime)))
                .getSQL();
    }

    public static String generateQueryGetLineDetailTrendSql(CallCoreMetricsRequest request, String tenantId) {

        Field<Object> timeField = field("deepsight_datetime");
        Field<Object> sourceField = field("didOwner");
        Field<Object> lineStatusField = field("lineStatus");
        String tableName = TenantUtils.generateAiobSessionTableName(tenantId);
        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        request.setType("LINE");
        return dsl.select(
                        sourceField.as("source"),
                        lineStatusField
                )
                .from(tableName)
                .where(getAiobDialDetailFilterField(request))
                .orderBy(timeField.desc())
                .limit(1)
                .getSQL();
    }

    private Condition getAiobDialDetailFilterField(CallCoreMetricsRequest request) {
        Condition condition = DSL.noCondition();
        if (StringUtils.isNotBlank(request.getType())) {
            DialMetricQueryTypeEnum queryType = DialMetricQueryTypeEnum.queryType(request.getType());
            String queryField = DialMetricQueryTypeEnum.getQueryField(queryType);
            condition = condition.and(field(name(queryField)).eq(request.getId()));
        }

        return condition;
    }

    /**
     * 查询告警配置对应的session源信息
     * task、robot类型的名称和状态，用于判断是否需要发送告警站内信
     *
     * @param config 告警配置
     * @param type
     * @return SQL
     */
    public static String generateSessionDetailQuery(AlertConfig config, AlertConfigTypeEnum type) {
        String sessionTableName = TenantUtils.generateAiobSessionTableName(config.getTenantId());
        Condition condition = Objects.equals(AlertConfigTypeEnum.TASK, type) ?
                condition(field(name("taskId")).eq(config.getConfigTarget())) :
                condition(field(name("robotId")).eq(config.getConfigTarget()));
        return using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS)
                .select(field(name("taskName")),
                        field(name("taskStatus")),
                        field(name("robotName")))
                .from(table(name(sessionTableName)))
                .where(condition)
                .limit(1)
                .getSQL();
    }

    /**
     * 查询外呼对话记录简单详情
     *
     * @param configTargets 配置目标ID集合
     * @param tenantId      租户ID
     * @param request       请求体
     * @return SQL
     */
    public static String generateSessionSimpleDetailSql(List<String> configTargets, String tenantId, ThirtyDayRankingRequest request) {
        String tableName = TenantUtils.generateAiobSessionTableName(tenantId);
        // session条件字段
        List<Condition> sessionConditions = new ArrayList<>();
        if (request.getSource() != null) {
            sessionConditions.add(condition(field(name("didOwner")).eq(request.getSource().getCode())));
        }
        if (request.getLineStatus() != null) {
            sessionConditions.add(condition(field(name("lineStatus")).eq(request.getLineStatus().getValue())));
        }
        if (request.getTaskStatus() != null) {
            sessionConditions.add(condition(field(name("taskStatus")).eq(request.getTaskStatus().getCode())));
        }
        if (request.getRobotType() != null) {
            sessionConditions.add(condition(field(name("robotScene")).eq(request.getRobotType().getCode())));
        }
        // session 查询字段：分组属性字段、特殊聚合字段
        List<Field> selectFields = new ArrayList<>();
        AlertConfigTypeEnum type = request.getType();
        switch (type) {
            case LINE -> {
                selectFields.add(field(name("callerNum")));
                selectFields.add(field(name("didOwner")));
                selectFields.add(field(name("lineStatus")));
                selectFields.add(field("ROW_NUMBER() OVER (PARTITION BY `callerNum` ORDER BY `deepsight_datetime` DESC) AS rn"));
                // 增加分组字段展示属性
                sessionConditions.add(condition(field(name("callerNum")).in(configTargets)));
                sessionConditions.add(condition(field(name("didOwner")).isNotNull()));
                sessionConditions.add(condition(field(name("lineStatus")).isNotNull()));
            }
            case TASK -> {
                selectFields.add(field(name("taskId")));
                selectFields.add(field(name("taskName")));
                selectFields.add(field(name("taskStatus")));
                selectFields.add(field("ROW_NUMBER() OVER (PARTITION BY `taskId` ORDER BY `deepsight_datetime` DESC) AS rn"));
                sessionConditions.add(condition(field(name("taskId")).in(configTargets)));
                sessionConditions.add(condition(field(name("taskName")).isNotNull()));
            }
            case ROBOT -> {
                selectFields.add(field(name("robotId")));
                selectFields.add(field(name("robotScene")));
                selectFields.add(field("ROW_NUMBER() OVER (PARTITION BY `robotId` ORDER BY `deepsight_datetime` DESC) AS rn"));
                sessionConditions.add(condition(field(name("robotId")).in(configTargets)));
                sessionConditions.add(condition(field(name("robotScene")).isNotNull()));
            }
        }
        Table<Record> table = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS)
                .select(selectFields)
                .from(table(name(tableName)))
                .where(sessionConditions)
                .asTable();
        List<Field> fields = selectFields.subList(0, selectFields.size() - 1);
        fields.set(0, fields.get(0).as("id"));
        return using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS)
                .select(fields)
                .from(table)
                .where(condition(field("rn").eq(1)))
                .getSQL();
    }
}