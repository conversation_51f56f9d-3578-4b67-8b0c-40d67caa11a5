package com.baidu.keyue.deepsight.schedule;

import com.baidu.keyue.deepsight.database.redis.RedisConfiguration;
import com.baidu.keyue.deepsight.mysqldb.entity.Label;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.service.label.LabelCalculateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class LabelCalScheduler {

    @Value("${switch.labelCalSchedulerWithBSC:false}")
    private boolean labelCalSchedulerWithBSC;

    @Autowired
    private RedisConfiguration redisConfiguration;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private LabelCalculateService labelCalculateService;

    public String generateSchedulerLockKey(String... keys) {
        return StringUtils.join(keys, "-");
    }

    /**
     * 定时触发标签生产任务(BSC 模式)
     * 每一个小时触发一次：
     * 1、从 task_info 表拉 next_exec_date <= 当前时间 && 未删除 的任务
     * 2、规则解析 && 提交执行
     * 3、生成执行记录
     * 4、更新 next_exec_date 执行时间
     */
    @Scheduled(cron = "${cron.labelCalSchedulerWithBSC:0 0 * * * *}")
    public void labelCalSchedulerInBSCMod() {
        if (!labelCalSchedulerWithBSC) {
            log.info("labelCalSchedulerInBSCMod is disabled. quit...");
            return;
        }

        // get lock
        String lockKey = generateSchedulerLockKey(redisConfiguration.getPrefix(), "labelCalScheduler", "Lock");
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("labelCalSchedulerInBSCMod get lock failed.");
            return;
        }

        log.info("labelCalSchedulerInBSCMod start...");
        List<Pair<LabelWithBLOBs, TaskInfo>> labelTaskPairs = labelCalculateService.pullWaitExecLabelTask();
        if (CollectionUtils.isEmpty(labelTaskPairs)) {
            log.info("labelCalSchedulerInBSCMod got empty labelTaskPairs. quit...");
            return;
        }

        try {
            labelTaskPairs.forEach(pair -> {
                try {
                    labelCalculateService.execByScheduler(pair.getLeft(), pair.getRight());
                } catch (Exception e) {
                    log.error("labelCalSchedulerInBSCMod exec task failed, ", e);
                }
            });
        } catch (Exception e) {
            log.error("labelCalSchedulerInBSCMod exec task failed, ", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        log.info("labelCalSchedulerInBSCMod exec finished. quit...");
    }

    /**
     * 清理已标记删除的 doris 宽表标签
     * 每天凌晨 3 点执行，捞更新时间小于 24小时，状态为删除的标签
     */
    @Scheduled(cron = "${cron.labelFieldClean:0 0 3 * * *}")
    public void labelFieldClean() {
        if (!labelCalSchedulerWithBSC) {
            log.info("labelFieldClean is disabled. quit...");
            return;
        }
        // get lock
        String lockKey = generateSchedulerLockKey("labelFieldClean", "Lock");
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("labelFieldClean get lock failed.");
            return;
        }

        log.info("labelFieldClean start...");
        List<Label> deletedLabels = labelCalculateService.queryDeletedLabel(86400);
        if (CollectionUtils.isEmpty(deletedLabels)) {
            log.info("labelFieldClean got empty deletedLabel. quit...");
            return;
        }


        try {
            deletedLabels.forEach(item -> {
                log.debug(String.format("labelFieldClean clean label, id: %s", item.getId()));
                try {
                    // TODO 需要解决频繁修改表结构问题，考虑是否可以统一放到 json 字段中
                    labelCalculateService.invalidLabelDorisFieldClear(item);
                } catch (Exception e) {
                    log.error(String.format("labelFieldClean clean label failed, id: %s, ", item.getId()), e);
                }
            });
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

}
