package com.baidu.keyue.deepsight.service.idmapping.impl;

import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.idmapping.dto.DatasetKafkaMsgDTO;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingDataTableWithRule;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRule;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.context.TestPropertySource;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@TestPropertySource(properties = {"deepSight.kafka.topic.idMapping=test_topic"})
public class IdMappingResetCalculateServiceImplTest{

    @Mock
    private DorisService dorisService;

    @Mock
    private TableFieldMetaInfoMapper fieldMetaInfoMapper;

    @Mock
    private KafkaTemplate<String, String> kafkaTemplate;

    @InjectMocks
    private IdMappingResetCalculateServiceImpl service;

    private IdMappingDataTableWithRule dataTableWithRule;

    private IdMappingRule idMappingRule;

    private TableFieldMetaInfo fieldMetaInfo;

    @BeforeEach
    void setUp() {
        idMappingRule = new IdMappingRule();
        idMappingRule.setEnField("test_field");
    
        dataTableWithRule = new IdMappingDataTableWithRule();
        dataTableWithRule.setDataTableId(1L);
        dataTableWithRule.setTableEnName("test_table");
        dataTableWithRule.setIdMappingRules(Collections.singletonList(idMappingRule));
    
        fieldMetaInfo = new TableFieldMetaInfo();
        fieldMetaInfo.setEnField("primary_field");
        fieldMetaInfo.setFieldTag(TableFieldTagEnum.PRIMARY.getCode());
    }

    @Test
    void refreshTableDataShouldReturnEarlyWhenNoFields() {
        dataTableWithRule.setIdMappingRules(Collections.emptyList());
        service.refreshTableData(dataTableWithRule);
        verifyNoInteractions(dorisService, fieldMetaInfoMapper, kafkaTemplate);
    }

    @Test
    void refreshTableDataShouldThrowExceptionWhenNoPrimaryKey() {
        when(fieldMetaInfoMapper.selectByExample(any(TableFieldMetaInfoCriteria.class)))
                .thenReturn(Collections.emptyList());
    
        DeepSightException.ParamsErrorException exception = assertThrows(DeepSightException.ParamsErrorException.class,
                () -> service.refreshTableData(dataTableWithRule));
    
        assertEquals(ErrorCode.BAD_REQUEST, exception.getErrorCode());
        assertEquals("表不存在主键", exception.getMessage());
    }

    @Test
    void refreshTableDataShouldReturnEarlyWhenTableNotExist() {
        when(fieldMetaInfoMapper.selectByExample(any(TableFieldMetaInfoCriteria.class)))
                .thenReturn(Collections.singletonList(fieldMetaInfo));
        when(dorisService.existTable(anyString())).thenReturn(false);
    
        service.refreshTableData(dataTableWithRule);
    
        verify(dorisService).existTable("test_table");
        verifyNoMoreInteractions(dorisService, kafkaTemplate);
    }

    @Test
    void refreshTableDataShouldReturnEarlyWhenTableEmpty() {
        when(fieldMetaInfoMapper.selectByExample(any(TableFieldMetaInfoCriteria.class)))
                .thenReturn(Collections.singletonList(fieldMetaInfo));
        when(dorisService.existTable(anyString())).thenReturn(true);
        when(dorisService.getSingleTableCount(anyString())).thenReturn(0L);
    
        service.refreshTableData(dataTableWithRule);
    
        verify(dorisService).getSingleTableCount("test_table");
        verifyNoInteractions(kafkaTemplate);
    }

    @Test
    void refreshTableDataShouldSendKafkaMessagesWhenDataExists() {
        when(fieldMetaInfoMapper.selectByExample(any(TableFieldMetaInfoCriteria.class)))
                .thenReturn(Collections.singletonList(fieldMetaInfo));
        when(dorisService.existTable(anyString())).thenReturn(true);
        when(dorisService.getSingleTableCount(anyString())).thenReturn(100L);
        when(dorisService.selectList(anyString())).thenReturn(Collections.emptyList());
        try (var mockedJsonUtils = mockStatic(JsonUtils.class)) {
            mockedJsonUtils.when(() -> JsonUtils.toJsonUnchecked(any(DatasetKafkaMsgDTO.class)))
                    .thenReturn("test_json");
            service.refreshTableData(dataTableWithRule);
        }
    }

    @Test
    void refreshTableDataShouldHandleMultipleBatches() {
        when(fieldMetaInfoMapper.selectByExample(any(TableFieldMetaInfoCriteria.class)))
                .thenReturn(Collections.singletonList(fieldMetaInfo));
        when(dorisService.existTable(anyString())).thenReturn(true);
        when(dorisService.getSingleTableCount(anyString())).thenReturn(1000L);
        when(dorisService.selectList(anyString())).thenReturn(Collections.emptyList());
    
        try (var mockedJsonUtils = mockStatic(JsonUtils.class)) {
            mockedJsonUtils.when(() -> JsonUtils.toJsonUnchecked(any(DatasetKafkaMsgDTO.class)))
                    .thenReturn("test_json");
    
            service.refreshTableData(dataTableWithRule);
    
            verify(dorisService, times(2)).selectList(anyString());
        }
    }

}