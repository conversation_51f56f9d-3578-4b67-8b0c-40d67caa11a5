package com.baidu.keyue.deepsight.open.aiob.service.impl;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.open.aiob.model.vo.OpenAutoReplayListVO;
import com.baidu.keyue.deepsight.open.aiob.service.OpenAutoReplayQueryService;
import com.baidu.keyue.deepsight.service.tool.MobileDecryptUtilsBean;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jooq.DSLContext;
import org.jooq.SQLDialect;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/8/13 16:58
 */
@Service
@Slf4j
public class OpenAutoReplayQueryServiceImpl implements OpenAutoReplayQueryService {

    /**
     * 小秘书最大查询数
     */
    @Value("${aiob.autoReplay.list.maxNum:20000}")
    private long autoReplayListMax;

    @Autowired
    private DorisService dorisService;

    @Autowired
    private MobileDecryptUtilsBean mobileDecryptUtilsBean;

    @Override
    public List<OpenAutoReplayListVO> selectListByTenantId(String tenantId) {
        // sql构造
        DSLContext dsl = DSL.using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        String selectSql = dsl.select(
                DSL.field(Constants.TABLE_USER_ONE_ID),
                DSL.field(Constants.TABLE_MOBILE_FIELD)
                )
                .from(DSL.table(Constants.DORIS_AIOB_SESSION_TABLE + "_" + tenantId))
                .where(DSL.field("is_auto_answer").eq(1))
                .limit(autoReplayListMax)
                .getSQL();
        log.debug("auto replay (xiao mi shu) query sql: " + selectSql);

        // 查询
        List<Map<String, Object>> queryRes = dorisService.selectList(selectSql);
        if (CollectionUtils.isEmpty(queryRes)) {
            return new ArrayList<>();
        }

        // 处理：被叫号码mobile解密
        return queryRes.stream().map(e -> OpenAutoReplayListVO.builder()
                .oneId(e.get(Constants.TABLE_USER_ONE_ID).toString())
                .phoneNumber(mobileDecryptUtilsBean.decryptMobileByTableName(e.get(Constants.TABLE_MOBILE_FIELD).toString(),
                        TenantUtils.generateAiobSessionTableName(tenantId)))
                .build()).collect(Collectors.toList());
    }

}
