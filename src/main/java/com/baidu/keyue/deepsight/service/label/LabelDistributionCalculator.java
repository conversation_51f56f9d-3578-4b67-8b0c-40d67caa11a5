package com.baidu.keyue.deepsight.service.label;

import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.LabelValueSaveModEnum;
import com.baidu.keyue.deepsight.models.label.LabelDistribute;
import com.baidu.keyue.deepsight.models.label.LabelDistributeDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 标签分布计算工具类
 * 抽取自LabelCalculateServiceImpl，提供统一的标签分布计算逻辑
 * 供普通标签和内置标签复用
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LabelDistributionCalculator {

    private final DorisService dorisService;

    /**
     * 计算标签分布统计
     * 复用LabelCalculateServiceImpl中的完整逻辑
     * 
     * @param tableName 表名（如：mock_user_xxx 或 db.table_name）
     * @param fieldName 字段名（如：process_label_123）
     * @param saveModEnum 保存模式（单值/多值）
     * @return 标签分布统计对象
     */
    public LabelDistribute calculateDistribution(String tableName, String fieldName, LabelValueSaveModEnum saveModEnum) {
        try {
            // 1. 查询总用户数
            long totalUsers = dorisService.getSingleTableCount(tableName);

            // 2. 查询有标签值的用户总数
            long labelUsers = countLabelUsers(tableName, fieldName);

            // 3. 查询标签值分布详情（复用现有SQL逻辑）
            List<LabelDistributeDetail> distributeDetails = calculateDistributeDetails(tableName, fieldName, saveModEnum);

            // 4. 构建LabelDistribute对象（复用现有计算逻辑）
            return buildLabelDistribute(totalUsers, labelUsers, distributeDetails);

        } catch (Exception e) {
            log.error("计算标签分布失败: tableName={}, fieldName={}", tableName, fieldName, e);
            return buildEmptyDistribution();
        }
    }

    /**
     * 计算标签分布统计（内置标签专用，默认多值模式）
     * 
     * @param tableName 表名
     * @param fieldName 字段名
     * @return 标签分布统计对象
     */
    public LabelDistribute calculateBuiltinLabelDistribution(String tableName, String fieldName) {
        return calculateDistribution(tableName, fieldName, LabelValueSaveModEnum.MULTI);
    }

    /**
     * 查询有标签值的用户总数
     * 复用LabelCalculateServiceImpl.countLabelAllLines()的逻辑
     */
    private long countLabelUsers(String tableName, String fieldName) throws Exception {
        String sql = String.format(
            "SELECT COUNT(*) FROM %s WHERE %s IS NOT NULL AND cardinality(%s) > 0",
            tableName, fieldName, fieldName
        );
        return dorisService.getCount(sql);
    }

    /**
     * 计算标签值分布详情
     * 直接复用LabelCalculateServiceImpl.calDistribute()中的SQL逻辑
     */
    private List<LabelDistributeDetail> calculateDistributeDetails(String tableName, String fieldName, LabelValueSaveModEnum saveModEnum) throws Exception {
        String distributionSql = buildDistributionSql(tableName, fieldName, saveModEnum);
        List<Map<String, Object>> result = dorisService.selectList(distributionSql);
        
        // 复用LabelCalculateServiceImpl中的结果处理逻辑
        return result.stream().map(row -> {
            String labelValue = (String) row.get("label");
            Long count = (Long) row.get("count");
            if (count == null) {
                count = 0L;
            }
            return new LabelDistributeDetail(labelValue, count, "0.0%");
        }).collect(Collectors.toList());
    }

    /**
     * 构建分布查询SQL
     * 完全复用LabelCalculateServiceImpl.calDistribute()中的SQL模板
     */
    private String buildDistributionSql(String tableName, String fieldName, LabelValueSaveModEnum saveModEnum) {
        if (LabelValueSaveModEnum.MULTI.equals(saveModEnum)) {
            // 多值：统计最多十个标签值的结果（与LabelCalculateServiceImpl完全一致）
            return String.format("""
                SELECT label, COUNT(*) AS count 
                FROM (
                    SELECT element_at(%s, 1) AS label FROM %s 
                    UNION ALL
                    SELECT element_at(%s, 2) AS label FROM %s 
                    UNION ALL
                    SELECT element_at(%s, 3) AS label FROM %s 
                    UNION ALL
                    SELECT element_at(%s, 4) AS label FROM %s 
                    UNION ALL
                    SELECT element_at(%s, 5) AS label FROM %s 
                    UNION ALL
                    SELECT element_at(%s, 6) AS label FROM %s 
                    UNION ALL
                    SELECT element_at(%s, 7) AS label FROM %s 
                    UNION ALL
                    SELECT element_at(%s, 8) AS label FROM %s 
                    UNION ALL
                    SELECT element_at(%s, 9) AS label FROM %s 
                    UNION ALL
                    SELECT element_at(%s, 10) AS label FROM %s 
                ) t 
                WHERE label IS NOT NULL AND label != '' 
                GROUP BY label 
                ORDER BY count DESC
                """,
                fieldName, tableName, fieldName, tableName, fieldName, tableName,
                fieldName, tableName, fieldName, tableName, fieldName, tableName,
                fieldName, tableName, fieldName, tableName, fieldName, tableName,
                fieldName, tableName
            );
        } else {
            // 单值：只统计计算后标签值列表里的第一个标签值（与LabelCalculateServiceImpl完全一致）
            return String.format("""
                SELECT label, COUNT(*) AS count 
                FROM (
                    SELECT element_at(%s, 1) AS label FROM %s 
                ) t 
                WHERE label IS NOT NULL AND label != '' 
                GROUP BY label 
                ORDER BY count DESC
                """,
                fieldName, tableName
            );
        }
    }

    /**
     * 构建LabelDistribute对象
     * 复用LabelCalculateServiceImpl中的计算逻辑
     */
    private LabelDistribute buildLabelDistribute(long totalUsers, long labelUsers, List<LabelDistributeDetail> details) {
        LabelDistribute labelDistribute = new LabelDistribute();
        labelDistribute.setTotal(labelUsers);
        
        // 覆盖率计算（与LabelCalculateServiceImpl完全一致）
        if (totalUsers > 0) {
            labelDistribute.setCoverageRate(String.format("%.2f%%", (double) labelUsers / totalUsers * 100));
        } else {
            labelDistribute.setCoverageRate("0.0%");
        }

        // 标签值占比计算（与LabelCalculateServiceImpl完全一致）
        details.forEach(detail -> {
            if (labelUsers > 0) {
                detail.setPercent(String.format("%.2f%%", (double) detail.getCount() / labelUsers * 100));
            }
        });
        
        labelDistribute.setLabels(details);
        return labelDistribute;
    }

    /**
     * 构建空的分布统计对象
     */
    private LabelDistribute buildEmptyDistribution() {
        LabelDistribute labelDistribute = new LabelDistribute();
        labelDistribute.setTotal(0L);
        labelDistribute.setCoverageRate("0.0%");
        labelDistribute.setLabels(new ArrayList<>());
        return labelDistribute;
    }
}
