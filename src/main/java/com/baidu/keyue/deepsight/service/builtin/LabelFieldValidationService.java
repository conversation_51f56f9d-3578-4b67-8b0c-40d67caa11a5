package com.baidu.keyue.deepsight.service.builtin;

import com.baidu.keyue.deepsight.constants.SqlConstants;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.BuiltinLabelEnum;
import com.baidu.keyue.deepsight.utils.SqlUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 标签字段验证服务
 * 用于在创建标签时检查必需字段是否存在
 * 
 * <AUTHOR>
 * @date 2025/01/15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LabelFieldValidationService {
    
    private final DorisService dorisService;
    
    /**
     * 验证标签创建所需的字段是否存在
     * @param tenantId 租户ID
     * @param labelEnum 标签枚举
     * @return 验证结果
     * <AUTHOR>
     */
    public boolean validateRequiredFields(String tenantId, BuiltinLabelEnum labelEnum) {
        return validateRequiredFields(tenantId, labelEnum, null);
    }
    
    /**
     * 验证标签创建所需的字段是否存在（支持robotName条件）
     * @param tenantId 租户ID
     * @param labelEnum 标签枚举
     * @param robotName 机器人名称（可选，用于标签4-5的维度校验）
     * @return 验证结果
     * <AUTHOR>
     */
    public boolean validateRequiredFields(String tenantId, BuiltinLabelEnum labelEnum, String robotName) {
        try {
            String tableName = getTableName(tenantId, labelEnum);
            List<String> validationFields = labelEnum.getValidationFields();
            
            // 验证所有必需字段
            for (String fieldName : validationFields) {
                if (!hasFieldData(tableName, fieldName, robotName)) {
                    log.warn("字段无数据: {}.{}, robotName: {}", tableName, fieldName, robotName);
                    return false;
                }
            }
            
            log.info("标签 {} 字段验证通过: {}, 验证字段: {}, robotName: {}", 
                labelEnum.getLabelName(), tableName, validationFields, robotName);
            return true;
        } catch (Exception e) {
            log.error("验证标签字段失败，tenantId: {}, label: {}, robotName: {}", 
                tenantId, labelEnum.name(), robotName, e);
            return false;
        }
    }
    
    /**
     * 获取表名
     * @param tenantId 租户ID
     * @param labelEnum 标签枚举
     * @return 表名
     */
    private String getTableName(String tenantId, BuiltinLabelEnum labelEnum) {
        String tablePrefix = labelEnum.getTablePrefix();
        return String.format("%s_%s", tablePrefix, tenantId);
    }

    /**
     * 检查字段是否有数据（支持robotName条件）
     * @param tableName 表名
     * @param fieldName 字段名
     * @param robotName 机器人名称（可选，用于标签4-5的维度校验）
     * @return 是否有数据
     * <AUTHOR>
     */
    private boolean hasFieldData(String tableName, String fieldName, String robotName) {
        try {
            // 自动判断字段类型并构建SQL
            SqlUtils.FieldValidationType fieldType = SqlUtils.determineFieldValidationType(fieldName);
            String whereClause = SqlUtils.buildRobotNameWhereClause(robotName);
            String sql = SqlUtils.buildFieldValidationSQL(tableName, fieldName, fieldType, whereClause);

            List<Map<String, Object>> results = dorisService.selectList(sql);
            if (!results.isEmpty()) {
                Object countObj = results.get(0).get(SqlConstants.FieldNames.COUNT);
                long count = countObj instanceof Number ? ((Number) countObj).longValue() : 0;
                return count > 0;
            }
            return false;
        } catch (Exception e) {
            log.debug("检查字段数据失败: {}.{}, robotName: {}", tableName, fieldName, robotName, e);
            return false;
        }
    }
    


}