package com.baidu.keyue.deepsight.service.tenant;

import com.baidu.keyue.deepsight.database.doris.DorisConfiguration;
import com.baidu.keyue.deepsight.database.doris.DorisTableTemplateConfig;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.rules.response.FilterEnumInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalog;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.LabelCatalogMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.LabelFieldMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.LabelMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TaskInfoMapper;
import com.baidu.keyue.deepsight.service.catalog.LabelCatalogService;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.datamanage.impl.TableRecordCommonService;
import com.baidu.keyue.deepsight.service.rules.RuleParseService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class TenantV3UpgradeHandlerTest {

    @Mock
    private TableFieldMetaInfo tableFieldMetaInfo;

    @InjectMocks
    private TenantV3UpgradeHandler tenantV3UpgradeHandler;

    @Mock
    private DorisService dorisService;

    @Mock
    private LabelCatalogService labelCatalogService;

    @Mock
    private TableRecordCommonService tableRecordCommonService;

    @Mock
    private DataTableInfoMapper dataTableInfoMapper;

    @Mock
    private DorisTableTemplateConfig dorisTableTemplateConfig;

    private TenantInfo tenantInfo;

    @Mock
    private DataTableManageService dataTableManageService;

    @Mock
    private TaskInfoMapper taskInfoMapper;
    @Mock
    private LabelMapper labelMapper;
    @Mock
    private LabelFieldMapper labelFieldMapper;
    @Mock
    private DorisConfiguration dorisConfiguration;
    @Mock
    private RuleParseService ruleParseService;
    @Mock
    private RedissonClient redisson;

    @Mock
    private LabelCatalogMapper labelCatalogMapper;

    private TenantDTO tenantDTO;

    @Mock
    private RLock lock;

    @BeforeEach
    void setUp() {
        tenantInfo = new TenantInfo();
        tenantInfo.setId(1L);
        tenantInfo.setVersion(3);

        tenantDTO = new TenantDTO();
        tenantDTO.setTenantId("testTenant");
        tenantDTO.setTenantInfo(tenantInfo);
        tenantDTO.setIsInit(false);

        ReflectionTestUtils.setField(tenantV3UpgradeHandler, "dorisService", dorisService);
        ReflectionTestUtils.setField(tenantV3UpgradeHandler, "tableRecordCommonService", tableRecordCommonService);
        ReflectionTestUtils.setField(tenantV3UpgradeHandler, "dataTableManageService", dataTableManageService);
        ReflectionTestUtils.setField(tenantV3UpgradeHandler, "dataTableInfoMapper", dataTableInfoMapper);
        ReflectionTestUtils.setField(tenantV3UpgradeHandler, "labelCatalogService", labelCatalogService);
        ReflectionTestUtils.setField(tenantV3UpgradeHandler, "dorisTableTemplateConfig", dorisTableTemplateConfig);
        ReflectionTestUtils.setField(tenantV3UpgradeHandler, "ruleParseService", ruleParseService);
        ReflectionTestUtils.setField(tenantV3UpgradeHandler, "redisson", redisson);
        ReflectionTestUtils.setField(tenantV3UpgradeHandler, "labelMapper", labelMapper);
        ReflectionTestUtils.setField(tenantV3UpgradeHandler, "labelFieldMapper", labelFieldMapper);
        ReflectionTestUtils.setField(tenantV3UpgradeHandler, "taskInfoMapper", taskInfoMapper);
        ReflectionTestUtils.setField(tenantV3UpgradeHandler, "dorisConfiguration", dorisConfiguration);
        ReflectionTestUtils.setField(tenantV3UpgradeHandler, "labelCatalogMapper", labelCatalogMapper);
        ReflectionTestUtils.setField(tenantV3UpgradeHandler, "sqlList",
                List.of("ALTER TABLE `aiob_conversation_session_service_^&` ADD COLUMN `customTagList` ARRAY<VARCHAR(128)> NULL DEFAULT '[]' COMMENT '意向';"));
    }

    @Test
    void testUpdateAiobRecordTableVisible() {
        String tenantId = "testTenant";
        DataTableInfo tableInfo = new DataTableInfo();
        tableInfo.setId(1L);

        when(dataTableManageService.getTableDetailWithTableName(anyString())).thenReturn(tableInfo);

        tenantV3UpgradeHandler.updateAiobRecordTableVisible(tenantId);

        verify(dataTableManageService, times(1)).getTableDetailWithTableName(anyString());
        verify(dataTableInfoMapper, times(1)).updateByPrimaryKeySelective(any());
    }

    @Test
    void testInit() throws IOException {
        when(dorisTableTemplateConfig.getAiobRecordFieldsInfo()).thenReturn(Collections.singletonList(tableFieldMetaInfo));
        when(dorisTableTemplateConfig.getAiobSessionFieldsInfo()).thenReturn(Collections.singletonList(tableFieldMetaInfo));

        tenantV3UpgradeHandler.init();

        verify(dorisTableTemplateConfig, times(1)).getAiobRecordFieldsInfo();
        verify(dorisTableTemplateConfig, times(1)).getAiobSessionFieldsInfo();
        assertEquals(4, tenantV3UpgradeHandler.version);
    }

    @Test
    void testConstructor() {
        TenantV3UpgradeHandler handler = new TenantV3UpgradeHandler(dorisService);
        assertNotNull(handler);
    }

    @Test
    void testOldTableUpgrade() {
        String tenantId = "1";
        assertDoesNotThrow(() -> tenantV3UpgradeHandler.oldTableUpgrade(tenantId));
        verify(dorisService, atLeastOnce()).operationSchema(anyString());
    }

    @Test
    void testCreateLabel() {
        String tenantId = "testTenant";
        LabelCatalog labelCatalog = new LabelCatalog();
        labelCatalog.setId(1L);
        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        VisibleFieldResponse field = new VisibleFieldResponse();
        field.setEnName("gender");
        field.setCnName("性别");
        field.setFieldType("string");
        field.setEnums(Collections.singletonList(new FilterEnumInfo("M", "男", "男性")));
        when(redisson.getLock("createLabeltestTenant性别-")).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        assertDoesNotThrow(() -> tenantV3UpgradeHandler.createLabel(tenantId, labelCatalog, field, dataTableInfo));

        verify(labelCatalogService, atLeastOnce()).getCatalogDetail(anyLong(), eq(tenantId));
    }

    @Test
    void testInit1() throws IOException {
        when(dorisTableTemplateConfig.getAiobRecordFieldsInfo()).thenReturn(List.of(tableFieldMetaInfo));
        when(dorisTableTemplateConfig.getAiobSessionFieldsInfo()).thenReturn(List.of(tableFieldMetaInfo));
        when(tableFieldMetaInfo.getEnField()).thenReturn("testField");

        tenantV3UpgradeHandler.init();

        verify(dorisTableTemplateConfig, times(1)).getAiobRecordFieldsInfo();
        verify(dorisTableTemplateConfig, times(1)).getAiobSessionFieldsInfo();
    }


    @Test
    void testCreateNewLabelCalTask() {
        TriggerModeEnum triggerMod = TriggerModeEnum.MANUAL;
        Long taskId = tenantV3UpgradeHandler.createNewLabelCalTask(triggerMod);
        verify(taskInfoMapper, times(1)).insert(any());
    }

    @Test
    void testConstructor1() {
        DorisService testDorisService = mock(DorisService.class);
        TenantV3UpgradeHandler handler = new TenantV3UpgradeHandler(testDorisService);
        assertNotNull(handler);
    }

    @Test
    void testCreateLabel1() {
        String tenantId = "testTenant";
        LabelCatalog labelCatalog = new LabelCatalog();
        labelCatalog.setId(1L);
        VisibleFieldResponse field = new VisibleFieldResponse();
        field.setEnName("gender");
        field.setCnName("性别");
        field.setFieldType("string");
        field.setEnums(List.of(new FilterEnumInfo("0", "nan", "nan")));
        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        when(labelMapper.countByExample(any())).thenReturn(0L);
        when(redisson.getLock("createLabeltestTenant性别-")).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        assertDoesNotThrow(() -> tenantV3UpgradeHandler.createLabel(tenantId, labelCatalog, field, dataTableInfo));

        verify(labelMapper, atLeastOnce()).insert(any());
    }

    @Test
    void testCreateNewLabelField() {
        String tenantId = "testTenant";
        String labelName = "testLabel";
        when(dorisConfiguration.getDb()).thenReturn("test_db");
        Long fieldId = tenantV3UpgradeHandler.createNewLabelField(tenantId, labelName);
        verify(labelFieldMapper, times(1)).insert(any());
        verify(dorisService, times(2)).operationSchema(anyString());
    }

    @Test
    void testPresetUserLabel() {
        // Arrange
        String tenantId = "1";
        String tableName = "mock_user_1";
        LabelCatalog labelCatalog = new LabelCatalog();
        labelCatalog.setId(1L);
        labelCatalog.setCatalogName("基础标签");

        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);

        VisibleFieldResponse field1 = new VisibleFieldResponse();
        field1.setEnName("gender");
        field1.setCnName("性别");
        field1.setFieldType("string");
        field1.setEnums(List.of(new FilterEnumInfo("0", "nan", "nan")));
        VisibleFieldResponse field2 = new VisibleFieldResponse();
        field2.setEnName("age_group");
        field2.setCnName("年龄分组");
        field2.setFieldType("string");
        field2.setEnums(List.of(new FilterEnumInfo("0", "nan", "nan")));
        List<VisibleFieldResponse> visibleFields = List.of(field1, field2, field1);

        when(dataTableManageService.validDataTableByTableName(tableName, tenantId)).thenReturn(dataTableInfo);
        when(dataTableManageService.getVisibleFields(tableName, tenantId, false)).thenReturn(visibleFields);
        when(labelCatalogService.checkLabelCatalog(tenantId, "基础标签")).thenReturn(labelCatalog);
        when(redisson.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        // Mock checkLabelCataLog
        // Act
        tenantV3UpgradeHandler.presetUserLabel(tenantId);

        // Assert
        verify(labelCatalogService, times(1)).initTenantCatalog(tenantId);
        verify(labelCatalogService, times(1)).checkLabelCatalog(tenantId, "基础标签");
        verify(dataTableManageService, times(1)).validDataTableByTableName(tableName, tenantId);
        verify(dataTableManageService, times(1)).getVisibleFields(tableName, tenantId, false);
    }

    @Test
    void testPresetUserLabelWithNoPresetFields() {
        // Arrange
        String tenantId = "1";
        String tableName = "mock_user_1";
        LabelCatalog labelCatalog = new LabelCatalog();
        labelCatalog.setId(1L);
        labelCatalog.setCatalogName("基础标签");

        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);

        VisibleFieldResponse field1 = new VisibleFieldResponse();
        field1.setEnName("non_preset_field1");
        VisibleFieldResponse field2 = new VisibleFieldResponse();
        field2.setEnName("non_preset_field2");
        List<VisibleFieldResponse> visibleFields = List.of(field1, field2);

        when(dataTableManageService.validDataTableByTableName(tableName, tenantId)).thenReturn(dataTableInfo);
        when(dataTableManageService.getVisibleFields(tableName, tenantId, false)).thenReturn(visibleFields);
        when(labelCatalogService.checkLabelCatalog(tenantId, "基础标签")).thenReturn(labelCatalog);

        // Act
        tenantV3UpgradeHandler.presetUserLabel(tenantId);

        // Assert
        verify(labelCatalogService, times(1)).initTenantCatalog(tenantId);
        verify(labelCatalogService, times(1)).checkLabelCatalog(tenantId, "基础标签");
        verify(dataTableManageService, times(1)).validDataTableByTableName(tableName, tenantId);
        verify(dataTableManageService, times(1)).getVisibleFields(tableName, tenantId, false);
    }
}