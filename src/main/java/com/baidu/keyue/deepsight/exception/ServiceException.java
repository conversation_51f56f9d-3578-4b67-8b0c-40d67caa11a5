package com.baidu.keyue.deepsight.exception;

import com.baidu.keyue.deepsight.web.WebContextHolder;
import lombok.Getter;

/**
 * 基础错误类
 * */
@Getter
public class ServiceException extends RuntimeException {

    private String requestId;
    private ErrorCode errorCode;
    private String message;

    ServiceException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
        this.message = errorCode.getMessage();
        requestId = WebContextHolder.getRequestId();
    }

    ServiceException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.message = message;
        requestId = WebContextHolder.getRequestId();
    }
}
