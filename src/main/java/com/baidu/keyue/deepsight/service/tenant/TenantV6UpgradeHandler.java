package com.baidu.keyue.deepsight.service.tenant;

import com.baidu.keyue.deepsight.database.doris.DorisTableTemplateConfig;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.TenantInfoMapper;
import com.baidu.keyue.deepsight.service.builtin.BuiltinLabelService;
import com.baidu.keyue.deepsight.service.datamanage.impl.TableRecordCommonService;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName TenantV3UpgradeHandler
 * @Description v1.1.3租户升级
 * <AUTHOR>
 * @Date 2025/6/20 3:18 PM
 */
@Slf4j
@Service
@Order(7)
public class TenantV6UpgradeHandler extends TenantUpgradeHandler {

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private TenantInfoMapper tenantInfoMapper;

    @Resource
    private BuiltinLabelService builtinLabelService;

    @Resource
    private DorisTableTemplateConfig dorisTableTemplateConfig;

    @Resource
    private TableRecordCommonService tableRecordCommonService;

    @Resource
    private DorisService dorisService;

    @Resource
    @Lazy
    private TenantInfoService tenantInfoService;

    /**
     * 外呼对话内容新增字段
     */
    private final List<String> aiobRecordField = List.of("nodeInfo");

    private final Map<String, TableFieldMetaInfo> aiobRecordFieldMap = new HashMap<>();

    @PostConstruct
    public void init() throws IOException {
        setVersion(7);
        initDorisSql("upgrade/doris/v1.2.2.sql");
        dorisTableTemplateConfig.getAiobRecordFieldsInfo().forEach(field -> aiobRecordFieldMap.put(field.getEnField(), field));
    }

    public TenantV6UpgradeHandler(DorisService dorisService) {
        super(dorisService);
    }

    @Override
    protected boolean needUpdateTenant(TenantInfo tenant) {
        TenantInfo tenantInfo = tenantInfoService.queryTenantInfo(tenant.getTenantid());
        Integer version = tenantInfo.getVersion();
        tenant.setUserId(tenantInfo.getUserId());
        return Objects.equals(version, 6);
    }

    @Override
    protected void executeUpgrade(TenantDTO tenant) {
        transactionTemplate.execute(status -> {
            try {
                String tenantId = tenant.getTenantId();
                log.info("tenant v1.2.2 upgrade start.tenantId is {}.", tenantId);
                // 新旧租户-内置标签：
                createBuiltinLabels(tenantId);
                log.info("v1.2.2 Upgrade close createBuiltinLabels over");

                // 老租户升级历史数据
                if (!tenant.getIsInit()) {
                    // 历史表操作
                    oldTableUpgrade(tenantId);
                    log.info("v1.1.2 Upgrade oldTableUpgrade over");
                }

                // 更新责任链租户信息
                TenantInfo info = tenant.getTenantInfo();
                info.setVersion(version);
                // 更新数据库租户版本
                TenantInfo tenantInfo = new TenantInfo();
                tenantInfo.setVersion(version);
                tenantInfo.setId(info.getId());
                tenantInfo.setUpdateTime(new Date());
                tenantInfoMapper.updateByPrimaryKeySelective(tenantInfo);
            } catch (Exception e) {
                log.error("tenant v1.2.2 Upgrade error!, tenantId:{} ", tenant.getTenantId(), e);
                status.setRollbackOnly();
                throw new DeepSightException.InitTenantFailedException(ErrorCode.INTERNAL_ERROR, "租户v1.2.2版本升级失败");
            }
            return null;
        });
    }

    /**
     * 创建内置标签
     *
     * @param tenantId 租户ID
     */
    public void createBuiltinLabels(String tenantId) {
        try {
            log.info("开始为租户 {} 创建内置标签", tenantId);
            builtinLabelService.createBuiltinLabelsForTenant(tenantId);
            log.info("租户 {} 内置标签创建完成", tenantId);
        } catch (Exception e) {
            log.error("租户 {} 内置标签创建失败", tenantId, e);
            // 不抛出异常，避免影响租户升级流程
        }
    }

    /**
     * 老租户表升级
     * 只针对老租户的升级
     * Doris、mysql字段信息修改、添加、删除
     *
     * @param tenantId 租户id
     */
    public void oldTableUpgrade(String tenantId) {
        // 执行除建表外的其他语句：历史Doris表新增字段
        for (String sql : this.sqlList) {
            if (sql.toUpperCase().contains("CREATE TABLE")) {
                continue;
            }
            // 将^&符号作为租户ID替代符号
            String execSql = sql.replaceAll("\\^&", tenantId) + ";";
            dorisService.operationSchema(execSql);
        }
        // 外呼对话内容表增加字段元信息
        String aiobRecordTableName = TenantUtils.generateAiobRecordTableName(tenantId);
        tableRecordCommonService.saveUpgradeFieldMetaInfo(aiobRecordTableName, aiobRecordField, aiobRecordFieldMap);
    }
}
