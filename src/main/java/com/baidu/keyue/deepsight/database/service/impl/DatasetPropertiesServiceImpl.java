package com.baidu.keyue.deepsight.database.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DatasetPropertiesService;
import com.baidu.keyue.deepsight.enums.DataTableStatusEnum;
import com.baidu.keyue.deepsight.enums.DbTypeEnum;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.rules.response.DatasetInfo;
import com.baidu.keyue.deepsight.models.rules.response.DatasetPropertiesResult;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.esotericsoftware.minlog.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @className: DatasetPropertiesServiceImpl
 * @description:
 * @author: wangzhongcheng
 * @date: 2024/12/25 19:45
 */
@Service
public class DatasetPropertiesServiceImpl implements DatasetPropertiesService {

    /**
     * 数据集mapper
     */
    @Autowired
    private DataTableInfoMapper dataTableInfoMapper;

    /**
     * 字段信息mapper
     */
    @Autowired
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @Override
    public List<DatasetInfo> getDatasetProperties() {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());

        Map<Long, DatasetInfo> filterDatasetInfoMap = getFilterDatasetInfoMap(tenantId);
        List<Long> datasetIds = filterDatasetInfoMap.keySet().stream().toList();
        if (CollectionUtils.isEmpty(datasetIds)) {
            return new ArrayList<>();
        }

        // 获取数据集中的事实表字段
        TableFieldMetaInfoCriteria fieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
        TableFieldMetaInfoCriteria.Criteria criteria = fieldMetaInfoCriteria.createCriteria();
        criteria.andIsFilterCriteriaEqualTo(Boolean.TRUE);
        criteria.andIsVisableEqualTo(Boolean.TRUE);
        criteria.andDataTableIdIn(datasetIds);
        List<TableFieldMetaInfo> tableFieldMetaInfos = tableFieldMetaInfoMapper
                .selectByExampleWithBLOBs(fieldMetaInfoCriteria);
        if (CollectionUtils.isEmpty(tableFieldMetaInfos)) {
            return new ArrayList<>();
        }

        // 按照数据集名称分组 key: 数据集表id value: 数据筛选字段信息
        Map<Long, List<DatasetPropertiesResult>> groupByDataset = tableFieldMetaInfos
                .stream()
                .map(DatasetPropertiesResult::convertFrom)
                .collect(Collectors.groupingBy(DatasetPropertiesResult::getDataTableId));

        // 组装数据集信息，并填充字段信息集合
        for (Long datasetId : filterDatasetInfoMap.keySet()) {
            if (CollectionUtils.isEmpty(groupByDataset.get(datasetId))) {
                filterDatasetInfoMap.get(datasetId).setProperties(List.of());
                continue;
            }
            filterDatasetInfoMap.get(datasetId).setProperties(groupByDataset.get(datasetId));
        }
        return filterDatasetInfoMap.values().stream()
                .filter(datasetInfo -> CollectionUtils.isNotEmpty(datasetInfo.getProperties())).toList();
    }

    @Override
    public List<DatasetPropertiesResult> getDatasetProperties(Long dataTableId, Boolean includeBaidu) {
        if (Objects.isNull(dataTableId)) {
            Log.error("dataTableId is null");
            throw new DeepSightException.ParamsErrorException(
                    ErrorCode.BAD_REQUEST, ErrorCode.BAD_REQUEST.getMessage());
        }

        // 获取数据集中的事实表字段
        TableFieldMetaInfoCriteria fieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
        TableFieldMetaInfoCriteria.Criteria criteria = fieldMetaInfoCriteria.createCriteria();
        criteria.andIsVisableEqualTo(true);
        criteria.andIsFilterCriteriaEqualTo(Boolean.TRUE);
        criteria.andDataTableIdEqualTo(dataTableId);
        if (!includeBaidu) {
            // 不包含百度字段
            criteria.andFromBaiduEqualTo(Boolean.FALSE);
        }
        List<TableFieldMetaInfo> tableFieldMetaInfos = tableFieldMetaInfoMapper
                .selectByExampleWithBLOBs(fieldMetaInfoCriteria);

        return tableFieldMetaInfos.stream()
                .map(DatasetPropertiesResult::convertFrom)
                .toList();
    }

    /**
     * 获取过滤数据表信息
     * 数据集信息map key: 数据集id value: 数据集信息
     */
    private Map<Long, DatasetInfo> getFilterDatasetInfoMap(String tenantId) {
        DataTableInfoCriteria dataTableInfoCriteria = new DataTableInfoCriteria();
        DataTableInfoCriteria.Criteria criteria = dataTableInfoCriteria.createCriteria();
        criteria.andTenantidEqualTo(tenantId);
        criteria.andIsVisableEqualTo(Boolean.TRUE);
        criteria.andDbTypeEqualTo(DbTypeEnum.DORIS_TYPE.getDbType());
        criteria.andStatusEqualTo(DataTableStatusEnum.CREATED.getStatus().byteValue());
        criteria.andTableNameNotEqualTo(TenantUtils.generateMockUserTableName(tenantId));
        List<DataTableInfo> dataTableInfos = dataTableInfoMapper.selectByExample(dataTableInfoCriteria);
        return dataTableInfos.stream().collect(Collectors.toMap(DataTableInfo::getId, DatasetInfo::convertFrom));
    }

}
