package com.baidu.keyue.deepsight.service.builtin;

import com.baidu.keyue.deepsight.constants.BuiltinLabelConstants;
import com.baidu.keyue.deepsight.models.label.LabelRule;
import com.baidu.keyue.deepsight.models.label.LabelValueRule;
import com.baidu.keyue.deepsight.models.rules.dto.RuleFilter;
import com.baidu.keyue.deepsight.models.rules.dto.RuleGroup;
import com.baidu.keyue.deepsight.models.rules.dto.RuleNode;
import com.baidu.keyue.deepsight.enums.FilterTypeEnum;
import com.baidu.keyue.deepsight.enums.FuncEnum;
import com.baidu.keyue.deepsight.enums.RuleTypeEnum;
import com.baidu.keyue.deepsight.enums.LogicEnum;
import lombok.experimental.UtilityClass;

import java.util.Arrays;
import java.util.List;

/**
 * 内置标签规则构建器
 * 负责构建6个内置标签的规则配置
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
@UtilityClass
public class BuiltinLabelRuleBuilder {

    /**
     * 构建陌生号码接通意愿规则
     */
    public LabelRule buildConnectWillingnessRule(String userMetricTableName, Long connectRateFieldId) {
        List<LabelValueRule> valueRules = Arrays.asList(
            // 高度防御型：接通率≤20%
            createLabelValueRule(BuiltinLabelConstants.LabelValueEnum.HIGH_DEFENSE, userMetricTableName, connectRateFieldId),
            // 谨慎筛选型：接通率21%-50%
            createLabelValueRule(BuiltinLabelConstants.LabelValueEnum.CAUTIOUS, userMetricTableName, connectRateFieldId),
            // 开放型：接通率>50%
            createLabelValueRule(BuiltinLabelConstants.LabelValueEnum.OPEN, userMetricTableName, connectRateFieldId)
        );

        return createLabelRule(valueRules);
    }

    /**
     * 构建陌拜开拓接受度规则
     */
    public LabelRule buildColdCallAcceptanceRule(String userMetricTableName, Long firstRoundHangupRateFieldId) {
        List<LabelValueRule> valueRules = Arrays.asList(
            // 高接受度：首轮挂断率≤15%
            createLabelValueRule(BuiltinLabelConstants.LabelValueEnum.HIGH_ACCEPTANCE, userMetricTableName, firstRoundHangupRateFieldId),
            // 一般接受度：首轮挂断率16%-50%
            createLabelValueRule(BuiltinLabelConstants.LabelValueEnum.NORMAL_ACCEPTANCE, userMetricTableName, firstRoundHangupRateFieldId),
            // 排斥型：首轮挂断率>50%
            createLabelValueRule(BuiltinLabelConstants.LabelValueEnum.REJECTION, userMetricTableName, firstRoundHangupRateFieldId)
        );

        return createLabelRule(valueRules);
    }

    /**
     * 构建平均对话轮次规则
     */
    public LabelRule buildAvgConversationRoundsRule(String userMetricTableName, Long avgRoundsFieldId) {
        List<LabelValueRule> valueRules = Arrays.asList(
            // 无效对话：0-2轮
            createLabelValueRule(BuiltinLabelConstants.LabelValueEnum.INVALID, userMetricTableName, avgRoundsFieldId),
            // 浅层交互：3-6轮
            createLabelValueRule(BuiltinLabelConstants.LabelValueEnum.SHALLOW, userMetricTableName, avgRoundsFieldId),
            // 深度沟通：7-10轮
            createLabelValueRule(BuiltinLabelConstants.LabelValueEnum.DEEP, userMetricTableName, avgRoundsFieldId),
            // 高价值客户：>10轮
            createLabelValueRule(BuiltinLabelConstants.LabelValueEnum.HIGH_VALUE, userMetricTableName, avgRoundsFieldId)
        );

        return createLabelRule(valueRules);
    }



    /**
     * 创建数据集规则组
     * 根据 dev.md 文档要求，创建嵌套的 RuleGroup 结构
     *
     * @param name           规则组名称
     * @param userTableName  用户表名 (mock_user_tenantId)
     * @param dataTableId    datatable_info 表中的 ID
     * @param function       函数枚举
     * @param params         参数列表
     * @return 规则组
     */
    private static RuleGroup createDatasetRuleGroup(String name, String userTableName, Long dataTableId,
                                                    FuncEnum function, List<String> params) {
        // 创建过滤条件 - fieldId 设置为 datatable_info 表的 ID
        RuleFilter filter = RuleFilter.builder()
                .type(FilterTypeEnum.NUMBER)
                .fieldId(dataTableId)  // 使用 datatable_info 表的 ID
                .function(function)
                .params(params)
                .build();

        // 创建规则节点
        RuleNode ruleNode = RuleNode.builder()
                .type(RuleTypeEnum.DATASET)
                .dataTableId(dataTableId)  // 设置 dataTableId
                .dorisTableName(userTableName)  // 设置实际的表名
                .filters(List.of(filter))
                .build();

        // 创建内层规则组（包含 ruleNodes）
        RuleGroup innerRuleGroup = RuleGroup.builder()
                .name(name)
                .relation(LogicEnum.AND)
                .ruleNodes(List.of(ruleNode))
                .ruleGroups(null)
                .build();

        // 创建外层规则组（包含 ruleGroups）
        RuleGroup outerRuleGroup = RuleGroup.builder()
                .name(null)
                .relation(LogicEnum.AND)
                .ruleGroups(List.of(innerRuleGroup))
                .ruleNodes(null)
                .build();

        return outerRuleGroup;
    }

    /**
     * 创建标签规则
     * @param valueRules 标签值规则列表
     * @return 标签规则
     */
    private static LabelRule createLabelRule(List<LabelValueRule> valueRules) {
        LabelRule labelRule = new LabelRule();
        labelRule.setLabelValueRules(valueRules);
        return labelRule;
    }

    /**
     * 创建标签值规则
     * 根据枚举值自动获取所有相关配置，无需外部传入
     * @param labelValueEnum 标签值枚举
     * @param tableName 数据表名
     * @param fieldId 字段ID
     * @return 标签值规则
     */
    private static LabelValueRule createLabelValueRule(BuiltinLabelConstants.LabelValueEnum labelValueEnum,
                                                      String tableName, Long fieldId) {
        // 根据枚举值获取规则配置
        LabelRuleConfig config = getRuleConfig(labelValueEnum);

        LabelValueRule valueRule = new LabelValueRule();
        valueRule.setLabelValue(labelValueEnum.getValue());
        valueRule.setLabelValueDesc(labelValueEnum.getDescription());
        valueRule.setRuleGroup(createDatasetRuleGroup(
            config.getGroupName(),
            tableName,
            fieldId,
            config.getFunction(),
            config.getParams()
        ));
        return valueRule;
    }

    /**
     * 根据标签值枚举获取对应的规则配置
     * 所有常量映射关系都在这里集中管理
     */
    private static LabelRuleConfig getRuleConfig(BuiltinLabelConstants.LabelValueEnum labelValueEnum) {
        switch (labelValueEnum) {
            // 陌生号码接通意愿
            case HIGH_DEFENSE -> {
                return new LabelRuleConfig(
                        BuiltinLabelConstants.ConnectWillingness.HIGH_DEFENSE_GROUP,
                        FuncEnum.LESS_EQUALS,
                        List.of(BuiltinLabelConstants.ConnectWillingness.THRESHOLD_HIGH_DEFENSE)
                );
            }
            case CAUTIOUS -> {
                return new LabelRuleConfig(
                        BuiltinLabelConstants.ConnectWillingness.CAUTIOUS_GROUP,
                        FuncEnum.BETWEEN,
                        List.of(BuiltinLabelConstants.ConnectWillingness.THRESHOLD_CAUTIOUS_MIN,
                                BuiltinLabelConstants.ConnectWillingness.THRESHOLD_CAUTIOUS_MAX)
                );
            }
            case OPEN -> {
                return new LabelRuleConfig(
                        BuiltinLabelConstants.ConnectWillingness.OPEN_GROUP,
                        FuncEnum.GREATER_THAN,
                        List.of(BuiltinLabelConstants.ConnectWillingness.THRESHOLD_OPEN)
                );
            }

            // 陌拜开拓接受度
            case HIGH_ACCEPTANCE -> {
                return new LabelRuleConfig(
                        BuiltinLabelConstants.ColdCallAcceptance.HIGH_ACCEPTANCE_GROUP,
                        FuncEnum.LESS_EQUALS,
                        List.of(BuiltinLabelConstants.ColdCallAcceptance.THRESHOLD_HIGH_ACCEPTANCE)
                );
            }
            case NORMAL_ACCEPTANCE -> {
                return new LabelRuleConfig(
                        BuiltinLabelConstants.ColdCallAcceptance.NORMAL_ACCEPTANCE_GROUP,
                        FuncEnum.BETWEEN,
                        List.of(BuiltinLabelConstants.ColdCallAcceptance.THRESHOLD_NORMAL_MIN,
                                BuiltinLabelConstants.ColdCallAcceptance.THRESHOLD_NORMAL_MAX)
                );
            }
            case REJECTION -> {
                return new LabelRuleConfig(
                        BuiltinLabelConstants.ColdCallAcceptance.REJECTION_GROUP,
                        FuncEnum.GREATER_THAN,
                        List.of(BuiltinLabelConstants.ColdCallAcceptance.THRESHOLD_REJECTION)
                );
            }

            // 平均对话轮次
            case INVALID -> {
                return new LabelRuleConfig(
                        BuiltinLabelConstants.AvgConversationRounds.INVALID_GROUP,
                        FuncEnum.BETWEEN,
                        List.of(BuiltinLabelConstants.AvgConversationRounds.THRESHOLD_INVALID_MIN,
                                BuiltinLabelConstants.AvgConversationRounds.THRESHOLD_INVALID_MAX)
                );
            }
            case SHALLOW -> {
                return new LabelRuleConfig(
                        BuiltinLabelConstants.AvgConversationRounds.SHALLOW_GROUP,
                        FuncEnum.BETWEEN,
                        List.of(BuiltinLabelConstants.AvgConversationRounds.THRESHOLD_SHALLOW_MIN,
                                BuiltinLabelConstants.AvgConversationRounds.THRESHOLD_SHALLOW_MAX)
                );
            }
            case DEEP -> {
                return new LabelRuleConfig(
                        BuiltinLabelConstants.AvgConversationRounds.DEEP_GROUP,
                        FuncEnum.BETWEEN,
                        List.of(BuiltinLabelConstants.AvgConversationRounds.THRESHOLD_DEEP_MIN,
                                BuiltinLabelConstants.AvgConversationRounds.THRESHOLD_DEEP_MAX)
                );
            }
            case HIGH_VALUE -> {
                return new LabelRuleConfig(
                        BuiltinLabelConstants.AvgConversationRounds.HIGH_VALUE_GROUP,
                        FuncEnum.GREATER_THAN,
                        List.of(BuiltinLabelConstants.AvgConversationRounds.THRESHOLD_HIGH_VALUE)
                );
            }
            default -> throw new IllegalArgumentException("不支持的标签值枚举: " + labelValueEnum);
        }
    }

    /**
     * 标签规则配置
     * 用于封装规则组名称、函数和参数
     */
    @lombok.Data
    @lombok.AllArgsConstructor
    private static class LabelRuleConfig {
        private String groupName;
        private FuncEnum function;
        private List<String> params;
    }
}