package com.baidu.keyue.deepsight.controller;

import java.util.HashMap;
import java.util.List;

import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.sop.SOPAnalyseProgressRequest;
import com.baidu.keyue.deepsight.models.sop.SOPAnalyseProgressResponse;
import com.baidu.keyue.deepsight.models.sop.SOPNodeConfirmRequest;
import com.baidu.keyue.deepsight.models.sop.SOPNodePredictRequest;
import com.baidu.keyue.deepsight.models.sop.SOPNodePredictResponse;
import com.baidu.keyue.deepsight.models.sop.SopBaseRequest;
import com.baidu.keyue.deepsight.models.sop.SopFlexibleWholeRequest;
import com.baidu.keyue.deepsight.models.sop.SopFlexibleWholeResponse;
import com.baidu.keyue.deepsight.models.sop.SopNodeDetailRequest;
import com.baidu.keyue.deepsight.models.sop.SopNodeDetailResponse;
import com.baidu.keyue.deepsight.models.sop.SopSankeyMetaResponse;
import com.baidu.keyue.deepsight.models.sop.SopSankeyWholeRequest;
import com.baidu.keyue.deepsight.models.sop.SopSankeyWholeResponse;
import com.baidu.keyue.deepsight.models.sop.SopUserConfigUpdateRequest;
import com.baidu.keyue.deepsight.models.sop.SopUserDetailRequest;
import com.baidu.keyue.deepsight.models.sop.SopWholeRobotVersionRequest;
import com.baidu.keyue.deepsight.models.sop.SopWholeRobotVersionResponse;
import com.baidu.keyue.deepsight.models.sop.SopTaskCreateRequest;
import com.baidu.keyue.deepsight.models.sop.SopTaskQueryRequest;
import com.baidu.keyue.deepsight.models.sop.SopTaskResponse;
import com.baidu.keyue.deepsight.models.sop.SopTaskUpdateRequest;
import com.baidu.keyue.deepsight.service.sop.AiobSOPService;
import com.baidu.keyue.deepsight.service.sop.impl.AiobSopStatisticService;
import com.baidu.keyue.deepsight.service.sop.SopTaskCfgService;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 外呼SOP相关接口
 * @Description 外呼 SOP 统计
 * <AUTHOR>
 * @Date 2025/5/08 16:51
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/deepsight/v1/sop")
public class AiobSOPController {

    private final AiobSopStatisticService aiobSopStatisticService;
    private final AiobSOPService aiobSOPService;
    private final SopTaskCfgService sopTaskCfgService;

    /**
     * 【灵活画布】整体统计数据
     * @param request SopFlexibleWholeRequest
     * @return BaseResponse<SopFlexibleWholeResponse>
     */
    @PostMapping("/r/flexible/whole")
    public BaseResponse<SopFlexibleWholeResponse> flexibleWholeData(@RequestBody @Valid SopFlexibleWholeRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(aiobSopStatisticService.flexibleWholeData(tenantId, request));
    }

    /**
     * 【快捷场景】桑基图整体统计数据
     * @param request SopSankeyWholeRequest
     * @return BaseResponse<SopSankeyWholeResponse>
     */
    @PostMapping("/r/sankey/whole")
    public BaseResponse<SopSankeyWholeResponse> sankeyWholeData(@RequestBody @Valid SopSankeyWholeRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(aiobSopStatisticService.sankeyWholeData(tenantId, request));
    }

    /**
     * 【快捷场景】桑基图步骤和节点信息
     * @param request SopSankeyWholeRequest
     * @return BaseResponse<SopSankeyMetaResponse>
     */
    @PostMapping("/r/sankey/sopMeta")
    public BaseResponse<SopSankeyMetaResponse> sankeySopMeta(@RequestBody @Valid SopSankeyWholeRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(aiobSOPService.listSOPMeta(tenantId, request.getTaskId(), request.getRobotVer()));
    }

    /**
     * 【快捷场景、灵活画布】节点详情统计 (包含上下游节点流量分布、每日流量分布)
     * @param request SopNodeDetailRequest
     * @return BaseResponse<SopNodeDetailResponse>
     */
    @PostMapping("/r/nodeDetail")
    public BaseResponse<SopNodeDetailResponse> nodeDetailStatistics(@RequestBody @Valid SopNodeDetailRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(aiobSopStatisticService.nodeDetailStatistics(tenantId, request));
    }

    /**
     * 【快捷场景、灵活画布】分析进度
     * @param request SopNodeDetailRequest
     * @return BaseResponse<SopNodeDetailResponse>
     */
    @PostMapping("/r/analyseProgress")
    public BaseResponse<SOPAnalyseProgressResponse> analyseProgress(@RequestBody @Valid SOPAnalyseProgressRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(aiobSopStatisticService.analyseProgress(tenantId, request));
    }

    /**
     * 获取机器人版本列表
     */
    @PostMapping("/r/robotVersionList")
    public BaseResponse<List<SopWholeRobotVersionResponse>> wholeRobotVersion(@RequestBody @Valid SopWholeRobotVersionRequest request) {
        return BaseResponse.of(aiobSOPService.listRobotVersion(request));
    }

    /**
     * 节点预测
     */
    @PostMapping("/r/nodePredict")
    public BaseResponse<SOPNodePredictResponse> predictNode(@RequestBody @Valid SOPNodePredictRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(aiobSOPService.predictNode(tenantId, request));
    }

    /**
     * 确认节点
     */
    @PostMapping("/r/nodeConfirm")
    public BaseResponse<Void> editNode(@RequestBody @Valid SOPNodeConfirmRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        aiobSOPService.confirmNode(tenantId, request.getTaskId(), request.getRule(), request.getMarkdown(), request.getRobotVer());
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 【快捷场景、灵活画布】更新分析设置
     * @param request SopUserConfigUpdateRequest
     * @return BaseResponse<Void>
     */
    @PostMapping("/w/userConfig/update")
    public BaseResponse<Void> updateUserConfig(@RequestBody @Valid SopUserConfigUpdateRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        aiobSOPService.updateUserConfig(tenantId, request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 【快捷场景、灵活画布】获取分析设置
     * @param request SopBaseRequest
     * @return BaseResponse<SopUserConfigUpdateRequest>
     */
    @PostMapping("/r/userConfig/get")
    public BaseResponse<SopUserConfigUpdateRequest> getUserConfig(@RequestBody @Valid SopBaseRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(aiobSOPService.getUserConfig(tenantId, request));
    }

    /**
     * 用户明细
     * @param request SopNodeDetailRequest
     * @return BasePageResponse<Map < String, String>>
     */
    @PostMapping("/r/userDetail")
    public BasePageResponse<HashMap<String, Object>> nodeUserDetail(@RequestBody @Valid SopUserDetailRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        if (request.getIntent()) {
            request.setCurrNodeId(aiobSopStatisticService.intentTagDecrypt(request.getCurrNodeId()));
        }
        List<String> idTransferRes = aiobSopStatisticService.taskIdTransfer(tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer());
        request.setRobotId(idTransferRes.get(0));
        request.setRobotVer(idTransferRes.get(1));
        return BasePageResponse.of(aiobSOPService.getUserDetailV2(tenantId, request));
    }

    // ================= 离线分析任务相关接口 =================

    /**
     * 创建离线分析任务
     * @param request SopTaskCreateRequest
     * @return BaseResponse<Long>
     */
    @PostMapping("/w/offlineTask/create")
    public BaseResponse<Long> createOfflineTask(@RequestBody @Valid SopTaskCreateRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        String creator = WebContextHolder.getUserAuthInfo().getUserName();
        Long taskId = sopTaskCfgService.createOfflineTask(tenantId, request, creator);
        return BaseResponse.of(taskId);
    }

    /**
     * 分页查询离线分析任务
     * @param request SopTaskQueryRequest
     * @return BasePageResponse<SopTaskResponse>
     */
    @PostMapping("/r/offlineTask/query")
    public BasePageResponse.Page<SopTaskResponse> queryOfflineTasks(@RequestBody @Valid SopTaskQueryRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return sopTaskCfgService.queryOfflineTasks(tenantId, request);

    }

    /**
     * 根据ID查询离线分析任务详情
     * @param id 分析任务ID
     * @return BaseResponse<SopTaskResponse>
     */
    @PostMapping("/r/offlineTask/detail")
    public BaseResponse<SopTaskResponse> getOfflineTaskDetail(@RequestBody Long id) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        SopTaskResponse response = sopTaskCfgService.getOfflineTaskById(tenantId, id);
        return BaseResponse.of(response);
    }

    /**
     * 编辑离线分析任务
     * @param request SopTaskUpdateRequest
     * @return BaseResponse<Void>
     */
    @PostMapping("/w/offlineTask/update")
    public BaseResponse<Void> updateOfflineTask(@RequestBody @Valid SopTaskUpdateRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        String modifier = WebContextHolder.getUserAuthInfo().getUserName();
        sopTaskCfgService.updateOfflineTask(tenantId, request, modifier);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 更新离线分析任务状态
     * @param request 包含taskId和status的请求
     * @return BaseResponse<Void>
     */
    @PostMapping("/w/offlineTask/updateStatus")
    public BaseResponse<Void> updateOfflineTaskStatus(@RequestBody HashMap<String, Object> request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        String modifier = WebContextHolder.getUserAuthInfo().getUserName();
        
        Long taskId = Long.valueOf(request.get("taskId").toString());
        Byte status = Byte.valueOf(request.get("status").toString());
        
        sopTaskCfgService.updateTaskStatus(tenantId, taskId, status, modifier);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 删除离线分析任务（支持取消运行中的任务）
     * @param taskId 任务ID
     * @return BaseResponse<Void>
     */
    @PostMapping("/w/offlineTask/delete")
    public BaseResponse<Void> deleteOfflineTask(@RequestBody Long taskId) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        sopTaskCfgService.deleteOfflineTask(tenantId, taskId);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 取消正在运行的离线分析任务
     * @param taskId 任务ID
     * @return BaseResponse<Boolean>
     */
    @PostMapping("/w/offlineTask/cancel")
    public BaseResponse<Boolean> cancelRunningTask(@RequestBody Long taskId) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        String operator = WebContextHolder.getUserAuthInfo().getUserName();
        boolean cancelled = sopTaskCfgService.cancelRunningTask(tenantId, taskId, operator);
        return BaseResponse.of(cancelled);
    }

    /**
     * 获取租户下指定状态的任务列表
     * @param status 任务状态，可为null
     * @return BaseResponse<List<SopTaskResponse>>
     */
    @PostMapping("/r/offlineTask/listByStatus")
    public BaseResponse<List<SopTaskResponse>> getOfflineTasksByStatus(@RequestBody(required = false) Byte status) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        List<SopTaskResponse> taskList = sopTaskCfgService.getTasksByStatus(tenantId, status);
        return BaseResponse.of(taskList);
    }

    /**
     * 获取任务执行状态监控信息
     * @return BaseResponse<Map<String, Object>>
     */
    @PostMapping("/r/offlineTask/monitor")
    public BaseResponse<HashMap<String, Object>> getTaskMonitorInfo() {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        
        HashMap<String, Object> monitorInfo = new HashMap<>();
        
        // 统计各种状态的任务数量
        int runningCount = sopTaskCfgService.getTasksByStatus(tenantId, (byte) 1).size();
        int failedCount = sopTaskCfgService.getTasksByStatus(tenantId, (byte) 2).size();
        int successCount = sopTaskCfgService.getTasksByStatus(tenantId, (byte) 3).size();
        int cancelledCount = sopTaskCfgService.getTasksByStatus(tenantId, (byte) 4).size();
        
        monitorInfo.put("runningTaskCount", runningCount);
        monitorInfo.put("failedTaskCount", failedCount);
        monitorInfo.put("successTaskCount", successCount);
        monitorInfo.put("cancelledTaskCount", cancelledCount);
        monitorInfo.put("totalTaskCount", runningCount + failedCount + successCount + cancelledCount);
        
        // 添加任务执行管理器的监控信息（需要在Service层添加相应方法）
        // 这里暂时省略，可以根据需要添加更详细的监控信息
        
        return BaseResponse.of(monitorInfo);
    }
}
