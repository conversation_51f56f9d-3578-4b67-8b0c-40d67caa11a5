package com.baidu.keyue.deepsight.service.sop.impl;

import com.alibaba.fastjson.JSON;

import com.baidu.keyue.deepsight.enums.AiobRobotTypeEnum;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.SopUserConfigAssistMetricEnum;
import com.baidu.keyue.deepsight.enums.SopUserConfigMainMetricEnum;
import com.baidu.keyue.deepsight.enums.SopUserConfigWarningThresholdEnum;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.sop.SopTaskCfgStatusEnum;
import com.baidu.keyue.deepsight.models.sop.SopTaskCreateRequest;
import com.baidu.keyue.deepsight.models.sop.SopTaskQueryRequest;
import com.baidu.keyue.deepsight.models.sop.SopTaskResponse;
import com.baidu.keyue.deepsight.models.sop.SopTaskUpdateRequest;
import com.baidu.keyue.deepsight.mysqldb.entity.SopTaskCfg;
import com.baidu.keyue.deepsight.mysqldb.entity.SopTaskCfgCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.SopUserConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.SopUserConfigCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.SopTaskCfgMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.SopUserConfigMapper;
import com.baidu.keyue.deepsight.service.sop.SopTaskCfgService;
import com.baidu.keyue.deepsight.service.sop.AiobSOPService;
import com.baidu.keyue.deepsight.models.sop.SopUserConfigUpdateRequest;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SOP离线分析任务配置服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SopTaskCfgServiceImpl implements SopTaskCfgService {

    private final SopTaskCfgMapper sopTaskCfgMapper;
    private final AiobSOPService aiobSOPService;
    private final SopUserConfigMapper sopUserConfigMapper;

    @Override
    public Long createOfflineTask(String tenantId, SopTaskCreateRequest request, String creator) {

        // 检查是否已存在相同配置的任务
        if (existsSameTask(-1L, -1L, tenantId, request.getTaskId(), request.getRobotId(), request.getRobotScene(),
                request.getRobotVersion(), request.getIsAutoAnswer(), request.getUserConfig())) {
            throw new DeepSightException.SopTaskCfgException(ErrorCode.BAD_REQUEST, "已存在相同配置的离线分析任务");
        }

        SopTaskCfg sopTaskCfg = new SopTaskCfg();
        sopTaskCfg.setTenantId(tenantId);
        sopTaskCfg.setTaskId(request.getTaskId());
        sopTaskCfg.setRobotId(request.getRobotId());
        sopTaskCfg.setRobotScene(request.getRobotScene());
        sopTaskCfg.setTaskName(request.getTaskName());
        sopTaskCfg.setRobotName(request.getRobotName());

        // 处理机器人版本列表，转为JSON字符串
        if (!CollectionUtils.isEmpty(request.getRobotVersion())) {
            sopTaskCfg.setRobotVersion(JSON.toJSONString(request.getRobotVersion()));
        } else {
            sopTaskCfg.setRobotVersion("[]");
        }

        sopTaskCfg.setIsAutoAnswer(request.getIsAutoAnswer());
        sopTaskCfg.setCreator(creator);
        sopTaskCfg.setModifier(creator);

        // 处理标签列表，转为JSON字符串
        if (!CollectionUtils.isEmpty(request.getTags())) {
            sopTaskCfg.setTags(JSON.toJSONString(request.getTags()));
        } else {
            sopTaskCfg.setTags("[]");
        }

        sopTaskCfg.setStatus(SopTaskCfgStatusEnum.RUNNING.getCode()); // 默认状态为执行中
        sopTaskCfg.setCreateTime(new Date());
        sopTaskCfg.setUpdateTime(new Date());


        // 初始化用户配置
        SopUserConfig userConfig = aiobSOPService.createUserConfig(tenantId, request.getUserConfig());
        if (ObjectUtils.isEmpty(userConfig)) {
            sopTaskCfg.setUserConfigId(userConfig.getId());
        }

        int result = sopTaskCfgMapper.insertSelective(sopTaskCfg);
        if (result <= 0) {
            throw new DeepSightException.SopTaskCfgException(ErrorCode.BAD_REQUEST, "创建离线分析任务失败");
        }
        return sopTaskCfg.getId();
    }

    @Override
    public BasePageResponse.Page<SopTaskResponse> queryOfflineTasks(String tenantId, SopTaskQueryRequest request) {
        log.debug("分页查询SOP离线分析任务, tenantId: {}, request: {}", tenantId, JSON.toJSONString(request));


        SopTaskCfgCriteria criteria = new SopTaskCfgCriteria();
        SopTaskCfgCriteria.Criteria criteriaCondition = criteria.createCriteria();
        criteriaCondition.andTenantIdEqualTo(tenantId);

        if (StringUtils.hasText(request.getTaskId())) {
            criteriaCondition.andTaskIdEqualTo(request.getTaskId());
        }

        if (StringUtils.hasText(request.getRobotId())) {
            criteriaCondition.andRobotIdEqualTo(request.getRobotId());
        }


        if (request.getStatus() != null) {
            criteriaCondition.andStatusEqualTo(request.getStatus());
        }

        if (StringUtils.hasText(request.getKeyword())) {
            criteriaCondition.andTaskNameContainsByLocate(request.getKeyword());
        }

        criteria.setOrderByClause("create_time desc");
        long count = sopTaskCfgMapper.countByExample(criteria);
        if (count == 0) {
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), 0L, Lists.newArrayList());
        }

        // 设置分页参数
        PageHelper.startPage(request.getPageNo(), request.getPageSize());
        List<SopTaskCfg> taskList = sopTaskCfgMapper.selectByExample(criteria);

        // 转换为响应对象
        List<SopTaskResponse> responseList = taskList.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());


        return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), count, responseList);
    }

    @Override
    public SopTaskResponse getOfflineTaskById(String tenantId, Long taskId) {

        List<SopTaskCfg> sopTaskCfg = getSopTaskCfgList(tenantId, taskId);
        return convertToResponse(sopTaskCfg.get(0));
    }

    private List<SopTaskCfg> getSopTaskCfgList(String tenantId, Long taskId) {
        SopTaskCfgCriteria criteria = new SopTaskCfgCriteria();
        criteria.createCriteria().andTenantIdEqualTo(tenantId).andIdEqualTo(taskId);
        List<SopTaskCfg> sopTaskCfg = sopTaskCfgMapper.selectByExample(criteria);
        if (CollectionUtils.isEmpty(sopTaskCfg)) {
            throw new DeepSightException.SopTaskCfgException(ErrorCode.BAD_REQUEST, "分析任务不存在");
        }
        return sopTaskCfg;
    }

    @Override
    public void updateTaskStatus(String tenantId, Long taskId, Byte status, String modifier) {
        log.info("更新SOP离线分析任务状态, tenantId: {}, taskId: {}, status: {}, modifier: {}",
                tenantId, taskId, status, modifier);

        SopTaskCfg sopTaskCfg = getTaskByIdAndTenant(tenantId, taskId);

        SopTaskCfg updateRecord = new SopTaskCfg();
        updateRecord.setId(taskId);
        updateRecord.setStatus(status);
        updateRecord.setModifier(modifier);
        updateRecord.setUpdateTime(new Date());

        int result = sopTaskCfgMapper.updateByPrimaryKeySelective(updateRecord);
        if (result <= 0) {
            throw new DeepSightException.SopTaskCfgException(ErrorCode.INTERNAL_ERROR, "更新任务状态失败");
        }

        log.info("更新SOP离线分析任务状态成功");
    }

    @Override
    public void updateTaskMessage(String tenantId, Long taskId, String taskMsg) {
        SopTaskCfg sopTaskCfg = getTaskByIdAndTenant(tenantId, taskId);

        SopTaskCfg updateRecord = new SopTaskCfg();
        updateRecord.setId(taskId);
        updateRecord.setTaskMsg(taskMsg);
        updateRecord.setUpdateTime(new Date());
        updateRecord.setId(sopTaskCfg.getId());

        int result = sopTaskCfgMapper.updateByPrimaryKeySelective(updateRecord);
        if (result <= 0) {
            throw new DeepSightException.SopTaskCfgException(ErrorCode.INTERNAL_ERROR, "更新任务执行信息失败");
        }

    }

    @Override
    public void updateOfflineTask(String tenantId, SopTaskUpdateRequest request, String modifier) {


        List<SopTaskCfg> sopTaskCfgs = getSopTaskCfgList(tenantId, request.getId());
        SopTaskCfg sopTaskCfg = sopTaskCfgs.stream()
                .findFirst()
                .orElseThrow(() -> new DeepSightException.SopTaskCfgException(ErrorCode.BAD_REQUEST, "分析任务不存在"));

        if (existsSameTask(request.getId(), request.getUserConfigId(), tenantId, request.getTaskId(),
                request.getRobotId(), request.getRobotScene(), request.getRobotVersion(),
                request.getIsAutoAnswer(), request.getUserConfig())) {
            throw new DeepSightException.SopTaskCfgException(ErrorCode.BAD_REQUEST, "已存在相同配置的离线分析任务");

        }
        // 检查任务状态，只有成功或执行失败的任务可以编辑
        if (SopTaskCfgStatusEnum.RUNNING.getCode().equals(sopTaskCfg.getStatus())) {
            throw new DeepSightException.SopTaskCfgException(ErrorCode.BAD_REQUEST, "正在执行的任务不能编辑");
        }

        SopTaskCfg updateRecord = new SopTaskCfg();
        updateRecord.setId(request.getId());

        // 更新机器人版本列表
        if (request.getRobotVersion() != null) {
            updateRecord.setRobotVersion(JSON.toJSONString(request.getRobotVersion()));
        }

        // 更新是否自动应答
        if (request.getIsAutoAnswer() != null) {
            updateRecord.setIsAutoAnswer(request.getIsAutoAnswer());
        }

        // 更新标签列表
        if (request.getTags() != null) {
            updateRecord.setTags(JSON.toJSONString(request.getTags()));
        }

        updateRecord.setModifier(modifier);
        updateRecord.setUpdateTime(new Date());

        sopTaskCfgMapper.updateByPrimaryKeySelective(updateRecord);
        // 同步更新用户配置
        if (request.getUserConfig() != null) {
            String userId = WebContextHolder.getUserId();
            SopUserConfigMainMetricEnum mainMetric =
                    SopUserConfigMainMetricEnum.codeOf(request.getUserConfig().getMainMetric());
            SopUserConfigAssistMetricEnum assistMetric =
                    SopUserConfigAssistMetricEnum.codeOf(request.getUserConfig().getAssistMetric());
            SopUserConfigWarningThresholdEnum threshold =
                    SopUserConfigWarningThresholdEnum.codeOf(request.getUserConfig().getWarningThreshold());

            SopUserConfig userConfig = new SopUserConfig();
            userConfig.setCoreMetric(mainMetric.getCode().byteValue());
            userConfig.setAssistMetric(assistMetric.getCode().byteValue());
            userConfig.setWarningThreshold(threshold.getCode());
            userConfig.setModifier(userId);
            userConfig.setUpdateTime(new Date());
            userConfig.setId(updateRecord.getUserConfigId());
            userConfig.setTenantId(tenantId);
            sopUserConfigMapper.updateByPrimaryKeySelective(userConfig);
        }


    }

    @Override
    public void deleteOfflineTask(String tenantId, Long taskId) {
        SopTaskCfg taskByIdAndTenant = getTaskByIdAndTenant(tenantId, taskId);
        int result = sopTaskCfgMapper.deleteByPrimaryKey(taskByIdAndTenant.getId());
        if (result <= 0) {
            throw new DeepSightException.SopTaskCfgException(ErrorCode.INTERNAL_ERROR, "删除任务失败");
        }
    }

    @Override
    public boolean cancelRunningTask(String tenantId, Long taskId, String operator) {
        return false;
    }

    @Override
    public boolean existsSameTask(Long id, Long userConfigId, String tenantId, String taskId, String robotId,
                                  Byte robotScene,
                                  List<String> robotVersion
            , Byte isAutoAnswer,
                                  SopUserConfigUpdateRequest request) {
        SopTaskCfgCriteria taskCfgCriteria = new SopTaskCfgCriteria();
        SopTaskCfgCriteria.Criteria cfgCriteriaCriteria = taskCfgCriteria.createCriteria();
        cfgCriteriaCriteria
                .andTenantIdEqualTo(tenantId).andTaskIdEqualTo(taskId)
                .andRobotIdEqualTo(robotId).andRobotSceneEqualTo(robotScene);

        List<SopTaskCfg> sopTaskCfgs = sopTaskCfgMapper.selectByExample(taskCfgCriteria);
        if (CollectionUtils.isEmpty(sopTaskCfgs)) {
            return false;
        }
        boolean anyMatch = sopTaskCfgs.stream().anyMatch(taskCfg -> taskCfg.getIsAutoAnswer().equals(isAutoAnswer)
                && !taskCfg.getId().equals(id) && taskCfg.getRobotVersion().equals(JSON.toJSONString(robotVersion)));
        if (!anyMatch) {
            return false;
        }

        SopUserConfigCriteria example = new SopUserConfigCriteria();
        SopUserConfigCriteria.Criteria criteria = example.createCriteria();
        if (userConfigId != null && userConfigId > 0L) {
            criteria.andIdNotEqualTo(userConfigId);
        }

        criteria
                .andTenantIdEqualTo(tenantId)
                .andTaskIdEqualTo(taskId)
                .andAssistMetricEqualTo(SopUserConfigAssistMetricEnum.codeOf(request.getAssistMetric()).getCode().byteValue())
                .andCoreMetricEqualTo(SopUserConfigMainMetricEnum.codeOf(request.getMainMetric()).getCode().byteValue())
                .andWarningThresholdEqualTo(SopUserConfigWarningThresholdEnum.codeOf(request.getWarningThreshold()).getCode())
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean())
        ;
        long userConfigCount = sopUserConfigMapper.countByExample(example);
        return userConfigCount > 0;
    }

    @Override
    public List<SopTaskResponse> getTasksByStatus(String tenantId, Byte status) {

        if (tenantId == null) {
            // 查询所有租户的任务
            SopTaskCfgCriteria criteria = new SopTaskCfgCriteria();
            SopTaskCfgCriteria.Criteria criteriaCondition = criteria.createCriteria();

            if (status != null) {
                criteriaCondition.andStatusEqualTo(status);
            }

            criteria.setOrderByClause("create_time desc");

            List<SopTaskCfg> taskList = sopTaskCfgMapper.selectByExample(criteria);

            return taskList.stream()
                    .map(this::convertToResponse)
                    .collect(Collectors.toList());
        } else {
            // 查询指定租户的任务
            List<SopTaskCfg> taskList = sopTaskCfgMapper.selectByTenantAndStatus(tenantId, status);

            return taskList.stream()
                    .map(this::convertToResponse)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 转换实体对象为响应对象
     */
    private SopTaskResponse convertToResponse(SopTaskCfg sopTaskCfg) {
        SopTaskResponse response = new SopTaskResponse();
        response.setId(sopTaskCfg.getId());
        response.setTaskId(sopTaskCfg.getTaskId());
        response.setRobotId(sopTaskCfg.getRobotId());
        response.setRobotScene(sopTaskCfg.getRobotScene());
        response.setRobotSceneDesc(getRobotSceneDesc(sopTaskCfg.getRobotScene()));

        // 解析机器人版本列表
        if (StringUtils.hasText(sopTaskCfg.getRobotVersion())) {
            try {
                response.setRobotVersion(JSON.parseArray(sopTaskCfg.getRobotVersion(), String.class));
            } catch (Exception e) {
                log.warn("解析机器人版本列表失败: {}", sopTaskCfg.getRobotVersion(), e);
                response.setRobotVersion(new ArrayList<>());
            }
        } else {
            response.setRobotVersion(new ArrayList<>());
        }

        response.setIsAutoAnswer(sopTaskCfg.getIsAutoAnswer());
        response.setCreator(sopTaskCfg.getCreator());
        response.setCreateTime(sopTaskCfg.getCreateTime());
        response.setUpdateTime(sopTaskCfg.getUpdateTime());

        // 解析标签列表
        if (StringUtils.hasText(sopTaskCfg.getTags())) {
            try {
                response.setTags(JSON.parseArray(sopTaskCfg.getTags(), String.class));
            } catch (Exception e) {
                log.warn("解析标签列表失败: {}", sopTaskCfg.getTags(), e);
                response.setTags(new ArrayList<>());
            }
        } else {
            response.setTags(new ArrayList<>());
        }

        response.setStatus(sopTaskCfg.getStatus());
        response.setStatusDesc(getStatusDesc(sopTaskCfg.getStatus()));
        response.setTaskMsg(sopTaskCfg.getTaskMsg());
        response.setTaskName(sopTaskCfg.getTaskName());
        response.setRobotName(sopTaskCfg.getRobotName());

        // 查询用户配置信息
        SopUserConfigCriteria configCriteria = new SopUserConfigCriteria();
        configCriteria.createCriteria().andIdEqualTo(sopTaskCfg.getUserConfigId()).andTenantIdEqualTo(sopTaskCfg.getTenantId()).
                andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<SopUserConfig> sopUserConfigs = sopUserConfigMapper.selectByExample(configCriteria);
        SopUserConfig userConfig = sopUserConfigs.stream()
                .findFirst().stream().findFirst().orElse(null);

        if (!ObjectUtils.isEmpty(userConfig)) {
            SopUserConfigUpdateRequest sopUserConfigUpdateRequest = new SopUserConfigUpdateRequest();
            sopUserConfigUpdateRequest.setMainMetric(Integer.valueOf(userConfig.getCoreMetric()));
            sopUserConfigUpdateRequest.setAssistMetric(Integer.valueOf(userConfig.getAssistMetric()));
            sopUserConfigUpdateRequest.setWarningThreshold(userConfig.getWarningThreshold());
            response.setUserConfig(sopUserConfigUpdateRequest);
        } else {
            response.setUserConfig(null);
        }

        return response;
    }

    /**
     * 获取机器人场景描述
     */
    private String getRobotSceneDesc(Byte robotScene) {
        AiobRobotTypeEnum robotTypeEnum = AiobRobotTypeEnum.fromCode(robotScene);
        return robotTypeEnum != null ? robotTypeEnum.getDesc() : "未知";
    }

    /**
     * 获取状态描述
     */
    private String getStatusDesc(Byte status) {
        SopTaskCfgStatusEnum statusEnum = SopTaskCfgStatusEnum.fromCode(status);
        return statusEnum != null ? statusEnum.getDesc() : "未知";
    }

    /**
     * 根据任务ID和租户ID查询任务，如果不存在则抛出异常
     */
    private SopTaskCfg getTaskByIdAndTenant(String tenantId, Long taskId) {
        SopTaskCfg sopTaskCfg = sopTaskCfgMapper.selectByPrimaryKey(taskId);
        if (sopTaskCfg == null || !tenantId.equals(sopTaskCfg.getTenantId())) {
            throw new DeepSightException.SopTaskCfgException(ErrorCode.NOT_FOUND, "任务不存在");
        }
        return sopTaskCfg;
    }
}