package com.baidu.keyue.deepsight.models.bsc.basic;

import com.baidu.keyue.deepsight.exception.ServiceException;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @className: BaseCalculateContext
 * @description:
 * @author: wangzhongcheng
 * @date: 2025/1/20 19:24
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BaseCalculateContext {

    /**
     * bsc 信息
     * jobId - bsc 工作任务id
     * instanceId - bsc job对应的工作实例id
     * bscTaskRequest - bsc 自定义param参数
     */
    private String jobId;
    private String instanceId;

    /**
     * 平台任务信息：标签、客群
     * execId - 平台任务执行id
     * err - 平台任务执行异常信息
     */
    private Long execId;
    @JsonIgnore
    private ServiceException err;

    /**
     * doris info doris的相关信息
     * primaryKey - 宽表主键字段、临时表主键字段
     * db - 宽表所属 doris 的库
     * table - flink 作业临时表
     * finalTable - 最终结果宽表
     * fieldName - 宽表的新增字段名、临时表的结果字段名
     */
    private String primaryKey;
    private String db;
    private String table;
    private String dupTable;
    private String finalTable;
    private String fieldName;

    /**
     * 生成bsc job名称
     */
    public String generateBscJobName() {
        return StringUtils.EMPTY;
    }
}
