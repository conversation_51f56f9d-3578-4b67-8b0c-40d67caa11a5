package com.baidu.keyue.deepsight.models.sop;

import lombok.Getter;

@Getter
public enum SopTaskCfgStatusEnum {

    RUNNING((byte) 1, "执行中"),

    FAIL((byte) 2, "计算失败"),

    SUCCESS((byte) 3, "成功"),

    CANCELLED((byte) 4, "已取消");

    private Byte code;

    private String desc;
    SopTaskCfgStatusEnum(Byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SopTaskCfgStatusEnum fromCode(Byte code) {
        if (code == null) {
            return null;
        }
        for (SopTaskCfgStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }


}
