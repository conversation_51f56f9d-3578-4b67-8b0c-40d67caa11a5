package com.baidu.keyue.deepsight.service.label.impl;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.database.doris.DorisConfiguration;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.database.service.impl.BaseCalculateService;
import com.baidu.keyue.deepsight.enums.LabelValueSaveModEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TaskTypeEnum;
import com.baidu.keyue.deepsight.enums.UpdateModEnum;
import com.baidu.keyue.deepsight.models.bsc.basic.BscTableIdentify;
import com.baidu.keyue.deepsight.models.bsc.label.BscLabelTaskDataField;
import com.baidu.keyue.deepsight.models.bsc.label.BscLabelTaskLabelCalculate;
import com.baidu.keyue.deepsight.models.bsc.label.BscLabelTaskRequest;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.exception.ServiceException;
import com.baidu.keyue.deepsight.models.label.GetLabelDetailRequest;
import com.baidu.keyue.deepsight.models.label.LabelDistribute;
import com.baidu.keyue.deepsight.models.label.LabelDistributeDetail;
import com.baidu.keyue.deepsight.models.label.LabelRule;
import com.baidu.keyue.deepsight.models.label.LabelValueRule;
import com.baidu.keyue.deepsight.models.label.cal.LabelCalculateContext;
import com.baidu.keyue.deepsight.models.rules.dto.DqlParseResult;
import com.baidu.keyue.deepsight.mysqldb.entity.Label;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendLabelMapper;
import com.baidu.keyue.deepsight.service.label.LabelCalculateService;
import com.baidu.keyue.deepsight.service.label.LabelFieldService;
import com.baidu.keyue.deepsight.service.label.LabelService;
import com.baidu.keyue.deepsight.service.rules.RuleParseService;
import com.baidu.keyue.deepsight.service.tasks.TaskInfoService;
import com.baidu.keyue.deepsight.service.tasks.TaskSchedulerService;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.google.common.collect.Lists;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LabelCalculateServiceImpl
        extends BaseCalculateService<LabelCalculateContext, LabelWithBLOBs> implements LabelCalculateService {

    @Autowired
    private DorisConfiguration dorisConfiguration;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private ExtendLabelMapper labelMapper;

    @Autowired
    private LabelFieldService labelFieldService;

    @Autowired
    private TaskSchedulerService taskSchedulerService;

    @Autowired
    private TaskInfoService taskInfoService;

    @Autowired
    private RuleParseService ruleParseService;

    @Autowired
    private LabelService labelService;

    private final Base64.Encoder encoder = Base64.getEncoder();

    public String generateLabelExecLockKey(String... keys) {
        return StringUtils.join(keys, "-");
    }

    public void execByManual(GetLabelDetailRequest request) {
        String userId = WebContextHolder.getUserId();
        String tenantId = WebContextHolder.getTenantId();
        String lockKey = generateLabelExecLockKey("LabelCalculateService-execByManual", tenantId, request.getLabelId().toString());
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("execByManual get lock failed.");
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST, "标签计算中");
        }
        try {
            // 获取标签详情 & 标签任务详情
            LabelWithBLOBs label = labelService.getLabelByTenantAndLabelId(tenantId, request.getLabelId());
            // 手动执行
            execByManual(label, userId);
        } catch (ServiceException e) {
            log.error("LabelExecByManual error. ", e);
            throw new DeepSightException.LabelExecCalculateFailedException(e.getMessage());
        } catch (Exception e) {
            log.error("LabelExecByManual error. ", e);
            throw new DeepSightException.LabelExecCalculateFailedException();
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    protected LabelCalculateContext initCalculateExecInstance(LabelWithBLOBs label, String userId) {
        LabelCalculateContext calculateExecInstance = new LabelCalculateContext();
        if (Objects.nonNull(label.getRecalculate()) && label.getRecalculate()) {
            label.setLabelValueUpdateMod(UpdateModEnum.REPLACE.getCode());
            calculateExecInstance.setLabelRecalculate(true);
        }
        calculateExecInstance.setLabel(label);
        // 创建执行记录
        calculateExecInstance.setExecId(newSchedulerRecord(label.getTask(), userId));
        // 设置主键，默认为 oneId
        calculateExecInstance.setPrimaryKey(Constants.TABLE_USER_ONE_ID);
        // 设置最终宽表
        calculateExecInstance.setDb(dorisConfiguration.getDb());
        calculateExecInstance.setFinalTable(TenantUtils.generateMockUserTableName(label.getTenantId()));
        // 设置标签计算的临时表，临时表名使用 execId 计算
        calculateExecInstance.setTable(calculateExecInstance.generateTemporaryTableName());
        calculateExecInstance.setDupTable(calculateExecInstance.generateTemporaryDupTableName());
        generateFieldName(calculateExecInstance, label.getField());
        // 初始化 bsc 任务结构体
        calculateExecInstance.setBscLabelTaskRequest(new BscLabelTaskRequest());
        calculateExecInstance.getBscLabelTaskRequest().setExecId(calculateExecInstance.getExecId());
        calculateExecInstance.getBscLabelTaskRequest().setUpdateModEnum(label.getLabelValueUpdateMod());
        calculateExecInstance.getBscLabelTaskRequest().setSaveModEnum(label.getLabelValueSaveMod());
        return calculateExecInstance;
    }

    private long newSchedulerRecord(long taskId, String userId) {
        return taskSchedulerService.newTaskScheduler(taskId, userId);
    }

    @Transactional
    public void onProcessingStatus(LabelCalculateContext calculateExecInstance) {
        // 更新调度记录
        taskSchedulerService.updateSchedulerStatus(calculateExecInstance.getExecId(), TaskExecStatusEnum.RUNNING);
        // 更新标签计算状态
        labelMapper.updateLabelCalTaskStatus(
                calculateExecInstance.getLabel().getId(), TaskExecStatusEnum.RUNNING.getCode(),
                calculateExecInstance.getLabel().getDistribution());
    }

    @Transactional
    public void onProcessing(LabelCalculateContext calculateExecInstance) {
        // 更新调度记录
        taskSchedulerService.updateScheduler(calculateExecInstance, TaskExecStatusEnum.RUNNING);
        // 更新标签计算状态
        labelMapper.updateLabelCalTaskResult(
                calculateExecInstance.getLabel().getId(), TaskExecStatusEnum.RUNNING.getCode(),
                calculateExecInstance.getLabel().getDistribution(), new Date(),
                calculateExecInstance.getLabelRecalculate());
    }

    @Transactional
    public void onSuccess(LabelCalculateContext calculateExecInstance) {
        // 更新调度记录
        taskSchedulerService.updateScheduler(calculateExecInstance, TaskExecStatusEnum.SUCCESS);
        // 更新标签计算状态
        String distribution = JsonUtils.toJsonWithOutException(calculateExecInstance.getLabelDistribute());
        labelMapper.updateLabelCalTaskResult(
                calculateExecInstance.getLabel().getId(), TaskExecStatusEnum.SUCCESS.getCode(), distribution, new Date(),
                false);
    }

    @Transactional
    public void onCancel(LabelCalculateContext calculateExecInstance) {
        // 更新调度记录
        taskSchedulerService.updateScheduler(calculateExecInstance, TaskExecStatusEnum.CANCELED);
        // 更新标签计算状态
        labelMapper.updateLabelCalTaskResult(
                calculateExecInstance.getLabel().getId(), TaskExecStatusEnum.CANCELED.getCode(),
                calculateExecInstance.getLabel().getDistribution(), new Date(),
                false);
    }

    private void generateFieldName(LabelCalculateContext execContext, Long fieldId) {
        String fieldName = execContext.generateFieldName(fieldId);
        if (StringUtils.isBlank(fieldName)) {
            execContext.setErr(new DeepSightException.LabelOperatorFailedException(ErrorCode.BAD_REQUEST, "标签字段为空"));
        }
        execContext.setFieldName(fieldName);
    }

    private void buildLabelValueSql(LabelCalculateContext execContext, LabelRule labelRule) {
        AtomicInteger alias = new AtomicInteger(0);
        Map<String, Integer> valuePriorityMap = new HashMap<>();
        for (int i = 0; i < labelRule.getLabelValueRules().size(); i++) {
            LabelValueRule rule = labelRule.getLabelValueRules().get(i);
            valuePriorityMap.put(rule.getLabelValue(), i);
            DqlParseResult dqlParseResult;
            try {
                dqlParseResult = ruleParseService.parseRuleGroup(rule.getRuleGroup(), alias);
            } catch (Exception e) {
                execContext.setErr(new DeepSightException.LabelOperatorFailedException(
                        ErrorCode.BAD_REQUEST, String.format("标签规则(%s)解析失败", rule.getLabelValue())));
                return;
            }
            dqlParseResult.setSelect(dqlParseResult.getSelect() + String.format(" ,'%s', %d", rule.getLabelValue(), i));
            String sql = String.format("INSERT INTO `%s` ", execContext.getDupTable()) + dqlParseResult.parseDorisSql();
            try {
                dorisService.execSql(sql);
            } catch (Exception e) {
                log.error("buildLabelValueSql exec sql error: {}, ", sql, e);
            }
        }
        execContext.getBscLabelTaskRequest().setValuePriorityMap(valuePriorityMap);
        // Source table schema
        execContext.getBscLabelTaskRequest().setSourceTable(Lists.newArrayList(
                new BscTableIdentify(execContext.getDb(), execContext.getDupTable(), execContext.getDupTable(), Lists.newArrayList(
                        new BscLabelTaskDataField("id", "string"),
                        new BscLabelTaskDataField("label", "string"),
                        new BscLabelTaskDataField("priority", "int")
                ))
        ));
        // 保存 sql 规则
        execContext.getBscLabelTaskRequest().setCalculates(
                Lists.newArrayList(
                        new BscLabelTaskLabelCalculate(
                                String.format("select id,label from %s", execContext.getDupTable())
                        )
                )
        );
        //  Sink table schema
        execContext.getBscLabelTaskRequest().setSinkTable(Lists.newArrayList(
                new BscTableIdentify(execContext.getDb(), execContext.getTable(), "", Lists.newArrayList(
                        new BscLabelTaskDataField("id", "string"),
                        new BscLabelTaskDataField("label", "array")
                ))
        ));
    }

    protected void taskCalculate(LabelCalculateContext execContext) {
        LabelWithBLOBs label = execContext.getLabel();
        // 1、标签规则校验
        LabelRule labelRule = JsonUtils.toObjectWithoutException(label.getLabelRule(), LabelRule.class);
        if (Objects.isNull(labelRule)) {
            execContext.setErr(new DeepSightException.LabelOperatorFailedException(ErrorCode.BAD_REQUEST, "标签规则为空"));
            return;
        }
        // 2.Doris前置资源准备
        try {
            dorisService.createLabelProcessTemporaryTable(execContext.getDb(), execContext.getTable(), execContext.getDupTable());
        } catch (Exception e) {
            execContext.setErr(new DeepSightException.DorisExecException());
            return;
        }
        // 3. 标签值规则 SQL 解析
        buildLabelValueSql(execContext, labelRule);
        if (Objects.nonNull(execContext.getErr())) {
            return;
        }
        onFinishedHandler(execContext);
        clearTmpTables(execContext);
    }

    @Override
    protected List<LabelWithBLOBs> getWaitExecTaskObjList(Set<Long> taskIdList) {
        return labelService.getWaitExecLabel(taskIdList);
    }

    @Override
    public List<Pair<LabelWithBLOBs, TaskInfo>> pullWaitExecLabelTask() {
        return this.pullWaitExecTask(TaskTypeEnum.LABEL_DWS_TASK);
    }

    @Override
    protected void onFailure(LabelCalculateContext execContext) {
        // 更新调度记录
        taskSchedulerService.updateScheduler(execContext, TaskExecStatusEnum.FAILED);
        // 更新标签计算状态
        labelMapper.updateLabelCalTaskResult(
                execContext.getLabel().getId(), TaskExecStatusEnum.FAILED.getCode(),
                execContext.getLabel().getDistribution(), new Date(),
                execContext.getLabelRecalculate());
    }

    @Override
    protected void onFinishedHandler(LabelCalculateContext calculateContext) {
        LabelWithBLOBs label = calculateContext.getLabel();
        BscLabelTaskRequest bscLabelTaskRequest = calculateContext.getBscLabelTaskRequest();
        LabelValueSaveModEnum saveModEnum = LabelValueSaveModEnum.getByCode(label.getLabelValueSaveMod());
        // 数据：dup -> 临时表 -> 宽表
        labelValuesMigrate(calculateContext);
        if (Objects.nonNull(calculateContext.getErr())) {
            onFailure(calculateContext);
            return;
        }
        // count(user宽表总记录数)
        long userCount = countAllLines(calculateContext);
        if (Objects.nonNull(calculateContext.getErr())) {
            onFailure(calculateContext);
            return;
        }
        // 查询标签总记录数
        long labelLineCount = countLabelAllLines(calculateContext);
        if (Objects.nonNull(calculateContext.getErr())) {
            onFailure(calculateContext);
            return;
        }
        // 统计宽表里的标签值分布
        List<LabelDistributeDetail> distributeDetails = calDistribute(calculateContext, saveModEnum);
        if (Objects.nonNull(calculateContext.getErr())) {
            onFailure(calculateContext);
            return;
        }
        LabelDistribute labelDistribute = new LabelDistribute();
        // 标签值人数：打上某个标签值的用户数。
        labelDistribute.setTotal(labelLineCount);
        // 覆盖率：打上某个标签的人数占全部人数的比例，如A标签100人，用户总数1000人，则覆盖率为10%；
        if (userCount > 0) {
            labelDistribute.setCoverageRate(String.format("%.2f%%", (double) labelLineCount / userCount * 100));
        } else {
            labelDistribute.setCoverageRate("0.0%");
        }
        // 标签值占比：打上某个标签值的用户数占该标签的用户数的比例，如A标签100人，取值为a1的有20人，则a1的占比为20%
        labelDistribute.setLabels(Lists.newArrayList());
        // distribution && 补齐没有记录的标签值
        Map<String, Boolean> tempLabelMap = new HashMap<>();
        distributeDetails.forEach(distributeDetail -> {
            tempLabelMap.put(distributeDetail.getLabelName(), true);
            if (labelLineCount > 0) {
                distributeDetail.setPercent(String.format("%.2f%%", (double) distributeDetail.getCount() / labelLineCount * 100));
            }
        });
        calculateContext.getBscLabelTaskRequest().getValuePriorityMap().forEach((labelValue, priority) -> {
            if (!tempLabelMap.containsKey(labelValue)) {
                distributeDetails.add(new LabelDistributeDetail(labelValue, 0L, "0.0%"));
            }
        });
        labelDistribute.setLabels(distributeDetails);
        calculateContext.setLabelDistribute(labelDistribute);
        onSuccess(calculateContext);
    }

    @Override
    protected LabelCalculateContext getCalculateContextByJsonStr(String contextJsonStr) {
        return JsonUtils.toObjectWithoutException(contextJsonStr, LabelCalculateContext.class);
    }

    private Long countAllLines(LabelCalculateContext execContext) {
        try {
            return dorisService.getCount(String.format("select count(*) from %s.%s", execContext.getDb(), execContext.getFinalTable()));
        } catch (Exception e) {
            log.error("LabelCalculateService.countAllLines error : ", e);
            execContext.setErr(new DeepSightException.LabelCountFailedException());
            return 0L;
        }
    }

    private Long countLabelAllLines(LabelCalculateContext execContext) {
        try {
            return dorisService.getCount(
                    String.format("select count(*) from %s.%s where array_size(%s) > 0",
                            execContext.getDb(),
                            execContext.getFinalTable(),
                            execContext.getFieldName())
            );
        } catch (Exception e) {
            log.error("LabelCalculateService.countAllLines error : ", e);
            execContext.setErr(new DeepSightException.LabelCountFailedException());
            return 0L;
        }
    }

    private List<LabelDistributeDetail> calDistribute(LabelCalculateContext execContext, LabelValueSaveModEnum saveModEnum) {
        String fieldName = execContext.getFieldName();
        String table = String.format("%s.%s", execContext.getDb(), execContext.getFinalTable());
        String distributionSql;
        // 多值：统计最多十个标签值的结果
        // 单值：只统计计算后标签值列表里的第一个标签值
        if (LabelValueSaveModEnum.MULTI.equals(saveModEnum)) {
            distributionSql = String.format("""
                            SELECT label, COUNT(*) AS count\s
                            FROM (
                                SELECT element_at(%s, 1) AS label FROM %s\s
                                UNION ALL
                                SELECT element_at(%s, 2) AS label FROM %s\s
                                UNION ALL
                                SELECT element_at(%s, 3) AS label FROM %s\s
                                UNION ALL
                                SELECT element_at(%s, 4) AS label FROM %s\s
                                UNION ALL
                                SELECT element_at(%s, 5) AS label FROM %s\s
                                UNION ALL
                                SELECT element_at(%s, 6) AS label FROM %s\s
                                UNION ALL
                                SELECT element_at(%s, 7) AS label FROM %s\s
                                UNION ALL
                                SELECT element_at(%s, 8) AS label FROM %s\s
                                UNION ALL
                                SELECT element_at(%s, 9) AS label FROM %s\s
                                UNION ALL
                                SELECT element_at(%s, 10) AS label FROM %s\s
                            ) AS flattened
                            WHERE label IS NOT NULL
                            GROUP BY label
                            ORDER BY count DESC""",
                    fieldName, table,
                    fieldName, table,
                    fieldName, table,
                    fieldName, table,
                    fieldName, table,
                    fieldName, table,
                    fieldName, table,
                    fieldName, table,
                    fieldName, table,
                    fieldName, table
            );
        } else {
            distributionSql = String.format("""
                            SELECT label, COUNT(*) AS count\s
                            FROM (
                                SELECT element_at(%s, 1) AS label FROM %s\s
                            ) AS flattened
                            WHERE label IS NOT NULL
                            GROUP BY label
                            ORDER BY count DESC""",
                    fieldName, table
            );
        }
        List<Map<String, Object>> list = Lists.newArrayList();
        try {
            list = dorisService.selectList(distributionSql);
        } catch (Exception e) {
            log.error("LabelCalculateService.calDistribute error : ", e);
            execContext.setErr(new DeepSightException.LabelExecDistributeFailedException());
        }
        List<LabelDistributeDetail> distributeDetails = Lists.newArrayList();
        if (Objects.nonNull(list) && !list.isEmpty()) {
            distributeDetails = list.stream().map(row -> {
                String l = (String) row.get("label");
                Long c = (Long) row.get("count");
                if (Objects.isNull(c)) {
                    c = 0L;
                }
                return new LabelDistributeDetail(l, c, "0.0%");
            }).collect(Collectors.toList());
        }
        return distributeDetails;
    }

    private void labelValuesMigrate(LabelCalculateContext execContext) {
        LabelWithBLOBs label = execContext.getLabel();
        UpdateModEnum updateModEnum = UpdateModEnum.getByCode(label.getLabelValueUpdateMod());
        LabelValueSaveModEnum saveModEnum = LabelValueSaveModEnum.getByCode(label.getLabelValueSaveMod());
        String dupMigrateSql;
        // 单值
        if (LabelValueSaveModEnum.SINGLE.equals(saveModEnum)) {
            dupMigrateSql = String.format("""
                    insert into %s\s
                    SELECT\s
                        id,\s
                        split_by_string(GROUP_CONCAT(label order by priority), ",")[1:1] AS label_array
                    FROM %s where id != '' GROUP BY id""", execContext.getTable(), execContext.getDupTable());
        } else {
            // 多值
            dupMigrateSql = String.format("""
                    insert into %s\s
                    SELECT\s
                        id,\s
                        split_by_string(GROUP_CONCAT(label order by priority), ",") AS label_array
                    FROM %s where id != '' GROUP BY id""", execContext.getTable(), execContext.getDupTable());
        }
        try {
            dorisService.execSql(dupMigrateSql);
        } catch (Exception e) {
            execContext.setErr(new DeepSightException.DorisExecException("标签值数据迁移"));
            log.error("LabelCalculateService.labelValuesMigrate dupMigrateSql from dup to tmp failed, ", e);
            return;
        }
        String from = String.format("%s.%s", execContext.getDb(), execContext.getTable());
        String to = String.format("%s.%s", execContext.getDb(), execContext.getFinalTable());
        String fieldName = execContext.getFieldName();
        String primaryKey = execContext.getPrimaryKey();
        // 每次更新重新计算，先做旧值的删除
        if (UpdateModEnum.REPLACE.equals(updateModEnum)) {
            // old data clear
            String clearSql = String.format("update %s as t set t.%s = '[]'", to, fieldName);
            try {
                dorisService.execSql(clearSql);
            } catch (Exception e) {
                execContext.setErr(new DeepSightException.DorisExecException("标签值数据清理执行失败"));
                log.error("LabelCalculateService.labelValuesMigrate clear old data failed, ", e);
                return;
            }
        }
        /*
         * 合并历史数据：
         * 	  命中新值，则覆盖旧值
         * 	  未命中新值，保持旧值
         * */
        String sqlTemplate = "UPDATE %s as t SET t.%s = s.label FROM %s s WHERE t.%s = s.id and s.id != ''";
        String sql = String.format(sqlTemplate, to, fieldName, from, primaryKey);
        try {
            dorisService.execSql(sql);
        } catch (Exception e) {
            execContext.setErr(new DeepSightException.DorisExecException("标签值数据迁移执行失败"));
            log.error("LabelCalculateService.labelValuesMigrate do data migrate failed, ", e);
            return;
        }
    }

    @Override
    public void cancelExecByManual(Label label) {
        // 获取标签详情 & 标签任务详情
        TaskInfo task = taskInfoService.getTaskDetailWithId(label.getTask());
        List<TaskSchedulerWithBLOBs> taskSchedulerRecords = taskSchedulerService.queryRunningScheduler(Lists.newArrayList(task.getId()));
        if (CollectionUtils.isEmpty(taskSchedulerRecords)) {
            log.info("cancelExecByManual got empty records");
            return;
        }
        TaskSchedulerWithBLOBs schedulerRecord = taskSchedulerRecords.get(0);
        LabelCalculateContext execContext = JsonUtils.toObjectWithoutException(schedulerRecord.getBody(), LabelCalculateContext.class);
        if (Objects.isNull(execContext)) {
            log.info("cancelExecByManual body parser LabelCalculateContext failed");
            return;
        }
        onCancel(execContext);
        String instanceId = execContext.getInstanceId();
        if (StringUtils.isBlank(instanceId)) {
            log.info("cancelExecByManual got empty bsc-instanceId");
            return;
        }
    }

    @Override
    public List<Label> queryDeletedLabel(Integer seconds) {
        return labelService.queryDeletedLabel(seconds);
    }

    @Override
    public void invalidLabelDorisFieldClear(Label label) {
        if (Objects.isNull(label.getField()) || StringUtils.isBlank(label.getTenantId())) {
            log.warn("label field is null, labelId: {}, tenantId: {}", label.getId(), label.getTenantId());
            return;
        }
        labelFieldService.deleteDorisLabelProcessField(label.getTenantId(), label.getField());
    }

    @Override
    protected String getBscJobCode(LabelCalculateContext execContext) {
        return null;
    }

    @Override
    protected String getBscResourceId() {
        return null;
    }

    private void clearTmpTables(LabelCalculateContext execContext) {
        // doris temporary table delete
        String temporaryTableName = execContext.getTable();
        String dupTemporaryTableName = execContext.getDupTable();
        if (StringUtils.isNotBlank(temporaryTableName)
                && StringUtils.contains(temporaryTableName, "temporary_t")) {
            dorisService.deleteLabelProcessTemporaryTable(execContext.getDb(), temporaryTableName);
        }
        if (StringUtils.isNotBlank(dupTemporaryTableName)
                && StringUtils.contains(dupTemporaryTableName, "temporary_t")) {
            dorisService.deleteLabelProcessTemporaryTable(execContext.getDb(), dupTemporaryTableName);
        }
    }
}
