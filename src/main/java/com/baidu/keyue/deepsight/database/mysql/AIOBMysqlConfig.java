package com.baidu.keyue.deepsight.database.mysql;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @className AIOBMysqlConfig
 * @description 外呼mysql配置
 * @date 2025/1/12 16:37
 */
@Configuration
@MapperScan(basePackages  = "com.baidu.keyue.deepsight.mysqldb.aiob.mapper" , sqlSessionFactoryRef = "aiobMysqlSqlSessionFactory")
public class AIOBMysqlConfig {

    private static final String MAPPER_LOCATION = "classpath*:mapper/mysqldb/*.xml";

    @Bean(name="aiobMysqlDataSource")
    public DataSource mysqlDataSource(AiobMysqlConfiguration mysqlConfiguration) {
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName(mysqlConfiguration.getDriverClassName());
        dataSource.setJdbcUrl(mysqlConfiguration.getUrl());
        dataSource.setUsername(mysqlConfiguration.getUsername());
        dataSource.setPassword(mysqlConfiguration.getPassword());
        return dataSource;
    }

    @Bean("aiobMysqlSqlSessionFactory")
    public SqlSessionFactory mysqlSqlSessionFactory(@Qualifier("aiobMysqlDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        // mapper的xml形式文件位置必须要配置，不然将报错：no statement （这种错误也可能是mapper的xml中，namespace与项目的路径不一致导致）
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources(MAPPER_LOCATION));
        return bean.getObject();
    }

    @Bean("aiobMysqlSqlSessionTemplate")
    public SqlSessionTemplate mysqlSqlSessionTemplate(@Qualifier("aiobMysqlSqlSessionFactory") SqlSessionFactory sqlSessionFactory){
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}
