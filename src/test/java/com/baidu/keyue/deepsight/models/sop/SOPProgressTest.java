package com.baidu.keyue.deepsight.models.sop;

import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMeta;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mockStatic;

@ExtendWith(MockitoExtension.class)
class SOPProgressTest {

    @Test
    void toMarkDown() {
        SOPProgress sopProgress = new SOPProgress();
        sopProgress.setList(Lists.newArrayList(
                SOPProgress.SOPStepWithNodes.builder()
                        .step("开场白")
                        .nodes(Lists.newArrayList("打招呼"))
                        .build(),
                SOPProgress.SOPStepWithNodes.builder()
                        .step("判断是否接受访谈")
                        .nodes(Lists.newArrayList("愿意接受访谈", "不愿意接受访谈"))
                        .build()
        ));
        String expected = """
                # 开场白
                ## 打招呼
                # 判断是否接受访谈
                ## 愿意接受访谈
                ## 不愿意接受访谈
                """;
        String actual = sopProgress.toMarkDown();
        assertEquals(expected, actual);
    }

    @Test
    void convertFromMarkdown() {
        String markdown = """
                # 开场白
                ## 打招呼
                # 判断是否接受访谈
                ## 愿意接受访谈
                ## 不愿意接受访谈
                """;
        SOPProgress actual = SOPProgress.convertFromMarkdown(markdown);
        SOPProgress expected = new SOPProgress();
        expected.setList(Lists.newArrayList(
                SOPProgress.SOPStepWithNodes.builder()
                        .step("开场白")
                        .nodes(Lists.newArrayList("打招呼"))
                        .build(),
                SOPProgress.SOPStepWithNodes.builder()
                        .step("判断是否接受访谈")
                        .nodes(Lists.newArrayList("愿意接受访谈", "不愿意接受访谈"))
                        .build()
        ));
        assertEquals(expected.toMarkDown(), expected.toMarkDown());
    }

    @Test
    void toMetas() {
        try (MockedStatic<WebContextHolder> mockedWebContextHolder = mockStatic(WebContextHolder.class)) {
            mockedWebContextHolder.when(WebContextHolder::getTenantId).thenReturn("tenantId");
            mockedWebContextHolder.when(WebContextHolder::getUserId).thenReturn("userId");

            SOPProgress sopProgress = new SOPProgress();
            sopProgress.setList(Lists.newArrayList(
                    SOPProgress.SOPStepWithNodes.builder()
                            .step("开场白")
                            .nodes(Lists.newArrayList("打招呼"))
                            .build(),
                    SOPProgress.SOPStepWithNodes.builder()
                            .step("判断是否接受访谈")
                            .nodes(Lists.newArrayList("愿意接受访谈", "不愿意接受访谈"))
                            .build()
            ));
            List<AiobSopMeta> metas = sopProgress.toMetas("taskId", "tenantId", "");
            assertEquals(6, metas.size());
        }


    }

    @Test
    void validMarkdown() {
        String md1 = """
                """;
        assertThrows(DeepSightException.MarkDownH1EmptyException.class, () -> SOPProgress.validMarkdown(md1));

        String md2 = """
                你好
                """;
        assertThrows(DeepSightException.MarkDownH1EmptyException.class, () -> SOPProgress.validMarkdown(md2));

        String md3 = """
                # 开场白
                # 判断意愿
                ## 用户同意回访
                ## 用户拒绝回访
                # 询问满意度
                ## 感谢并问满意度
                ## 满意度高分结束
                ## 满意度低转改进
                # 咨询改进点
                ## 咨询不满原因
                ## 引导用户多说
                ## 用户说完感谢
                ## 用户不耐烦结束
                # 结束对话
                ## 礼貌结束对话
                """;
        assertThrows(DeepSightException.MarkDownH2EmptyException.class, () -> SOPProgress.validMarkdown(md3));

        String md4 = """
                ## 介绍身份目的
                # 判断意愿
                ## 用户同意回访
                ## 用户拒绝回访
                # 询问满意度
                ## 感谢并问满意度
                ## 满意度高分结束
                ## 满意度低转改进
                # 咨询改进点
                ## 咨询不满原因
                ## 引导用户多说
                ## 用户说完感谢
                ## 用户不耐烦结束
                # 结束对话
                ## 礼貌结束对话
                """;
        assertThrows(DeepSightException.MarkDownH1EmptyException.class, () -> SOPProgress.validMarkdown(md4));

        String md5 = """
                # 开场白
                ## 介绍身份目的
                # 判断意愿
                ## 用户同意回访
                ## 用户拒绝回访
                # 询问满意度
                ## 感谢并问满意度
                ## 满意度高分结束
                ## 满意度低转改进
                # 咨询改进点
                ## 咨询不满原因
                ## 引导用户多说
                ## 用户说完感谢
                ## 用户不耐烦结束
                # 结束对话
                ## 礼貌结束对话
                """;
        assertDoesNotThrow(() -> SOPProgress.validMarkdown(md5));

        String md6 = """
                # 开场白
                ## 开场白
                # 判断意愿
                ## 判断意愿
                ## 用户拒绝回访
                # 询问满意度
                ## 感谢并问满意度
                ## 满意度高分结束
                ## 满意度低转改进
                # 咨询改进点
                ## 咨询不满原因
                ## 引导用户多说
                ## 用户说完感谢
                ## 用户不耐烦结束
                # 结束对话
                ## 礼貌结束对话
                """;
        assertThrows(DeepSightException.MarkDownSameH1AndH2Exception.class, () -> SOPProgress.validMarkdown(md6));

        String md7 = """
                # 开场白
                ## 介绍身份目的
                # 判断意愿
                ## 用户同意回访
                ## 用户同意回访
                # 询问满意度
                ## 感谢并问满意度
                ## 满意度高分结束
                ## 满意度低转改进
                # 咨询改进点
                ## 咨询不满原因
                ## 引导用户多说
                ## 用户说完感谢
                ## 用户不耐烦结束
                # 结束对话
                ## 礼貌结束对话
                """;
        assertThrows(DeepSightException.MarkDownSameH2Exception.class, () -> SOPProgress.validMarkdown(md7));

        String md8 = """
                # 第一步
                开场白
                """;
        assertThrows(DeepSightException.MarkDownH2EmptyException.class, () -> SOPProgress.validMarkdown(md8));


        String md9 = """
                #开场白
                  ##打招呼并介绍活动
                # 客户回应
                  ## 客户明确参加
                  ## 客户明确拒绝
                  ## 客户考虑中
                # 挽留成功后
                  ## 回复成功结束话术
                # 挽留失败后
                  ## 回复失败结束话术
                # 客户质疑身份
                  ## 回应身份并追问参加
                # 客户提问可答
                  ## 回答并追问参加
                # 客户提问不可答
                  ## 建议宣讲会了解并追问参加
                # 语音机器人接听
                  ## 介绍活动并结束
                # 客户回复无意义
                  ## 请求确认是否参加
                # 客户不礼貌用语
                  ## 回复抱歉并结束
                # 客户明确参加后
                  ## 回复成功结束话术
                # 客户明确拒绝后
                  ## 回复失败结束话术
                # 客户考虑中后
                  ## 尝试挽留客户
                # 尝试挽留客户后
                  ## 挽留成功
                  ## 挽留失败
                """;
        assertDoesNotThrow(() -> SOPProgress.validMarkdown(md9));
    }
}