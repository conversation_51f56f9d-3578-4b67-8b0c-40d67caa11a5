package com.baidu.keyue.deepsight.schedule.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.AiobTaskStatusEnum;
import com.baidu.keyue.deepsight.models.dial.AiobTaskListRequest;
import com.baidu.keyue.deepsight.models.dial.AiobTaskListResponse;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.service.agg.AiobSessionMetricAggService;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.kybase.commons.utils.HttpUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;


@Slf4j
@Service
public class AiobTaskInfoSyncAsyncTask {


    @Value("${aiob.url}")
    private String aiobHost;

    @Value("${aiob.taskList}")
    private String taskListUri;

    @Value("${aiob.queryTaskInfoMax:300}")
    private long queryTaskInfoMax;

    @Autowired
    private DorisService dorisService;

    @Autowired
    private AiobSessionMetricAggService aiobSessionMetricAggService;

    @Async("aiobTaskInfoSyncTaskExecutor")
    public CompletableFuture<Void> processTenantInfo(TenantInfo tenantInfo) {
        try {
            this.syncTaskInfoReal(tenantInfo);
        } catch (Exception e) {
            log.error("aiobTaskInfoSync exec task failed, tenantId: {}", tenantInfo.getTenantid(), e);
        }
        log.info("aiobTaskInfoSync exec task finished, tenantId: {}", tenantInfo.getTenantid());
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 同步租户外呼任务信息
     *
     * @param tenantInfo 租户
     */
    private void syncTaskInfoReal(TenantInfo tenantInfo) {
        String tenantId = tenantInfo.getTenantid();
        String tableName = TenantUtils.generateAiobSessionTableName(tenantId);
        String countSql = ORMUtils.generateCountSessionTaskIds(tableName);
        long count = dorisService.getCount(countSql);
        if (count <= 0) {
            log.info("{} task status update no data skip", tenantId);
            return;
        }
        int offset = 0;
        long pageSize = queryTaskInfoMax;
        long pageCount = count % queryTaskInfoMax == 0 ? count / queryTaskInfoMax : count / queryTaskInfoMax + 1;
        for (int i = 0; i < pageCount; i++) {
            offset = i * (int) pageSize;
            String queryIdsSql = ORMUtils.generateQuerySessionTaskIds(tableName, offset, (int) pageSize);
            List<Map<String, Object>> taskIds = dorisService.selectList(queryIdsSql);
            if (CollUtil.isEmpty(taskIds)) {
                continue;
            }
            String url = aiobHost + taskListUri;
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put(Constants.TENANT_ID_HEADER_KEY, tenantId);
            headerMap.put(Constants.USER_ID_HEADER_KEY, tenantInfo.getUserId());
            headerMap.put(Constants.ACCESS_TOKEN, Constants.ACCESS_TOKEN_VALUE);
            AiobTaskListRequest listRequest = new AiobTaskListRequest(taskIds);
            String postJson = HttpUtil.postJson(url, JSONUtil.toJsonStr(listRequest), headerMap);
            AiobTaskListResponse res = JsonUtils.readType(postJson, new TypeReference<>() {
            });
            if (res == null) {
                log.error("task status update res解析错误:{}", postJson);
                return;
            }
            AiobTaskListResponse.TaskDetail data = res.getData();
            if (data == null || CollUtil.isEmpty(data.getList())) {
                log.error("task status update任务详情为空,tenantId:{}, res:{}", tenantId, postJson);
                return;
            }
            for (AiobTaskListResponse.TaskList task : data.getList()) {
                Long taskId = task.getTaskId();
                String taskName = task.getTaskName();
                AiobTaskStatusEnum statusEnum = AiobTaskStatusEnum.createByValue(task.getStatus());
                String updateSql = ORMUtils.generateSessionTaskInfoUpdate(tableName, taskId, statusEnum, taskName);
                log.debug("update session task status:{}", updateSql);
                dorisService.execSql(updateSql);
            }
        }
        log.info("{} task status update over", tenantInfo.getTenantid());
    }

}
