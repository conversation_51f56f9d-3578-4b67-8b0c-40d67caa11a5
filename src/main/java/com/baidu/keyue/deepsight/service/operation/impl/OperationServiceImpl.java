package com.baidu.keyue.deepsight.service.operation.impl;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.enums.OperationModeEnum;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.operation.response.OperationModeResponse;
import com.baidu.keyue.deepsight.service.operation.OperationService;
import com.baidu.kybase.sdk.beans.admin.vo.TenantAppPackageInfo;
import com.baidu.kybase.sdk.beans.admin.vo.TenantAppPackageInfoReq;
import com.baidu.kybase.sdk.beans.admin.vo.TenantAppPackageInfoResp;
import com.baidu.kybase.sdk.user.service.OpAdminApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *  租户运营模式服务实现类
 */
@Slf4j
@Service
public class OperationServiceImpl implements OperationService {
    @Autowired
    private OpAdminApi opAdminApi;

    @Override
    public OperationModeResponse getTenantOperationMode(String tenantId) {
        OperationModeResponse response = new OperationModeResponse();
        // 默认为自运营
        response.setType(OperationModeEnum.OPERATION_BY_SELF.getCode().intValue());
        // 调用基座API，获取租户的运营模式
        TenantAppPackageInfoReq req = new TenantAppPackageInfoReq();
        req.setTenantId(Long.valueOf(tenantId));
        try {
            TenantAppPackageInfoResp packageInfo = opAdminApi.getAdminTenantAppPakcageInfo(req);
            for (TenantAppPackageInfo app : packageInfo.getApps()) {
                if (Constants.DONG_CHA_APPID.equals(app.getAppId()) && app.getPackageType() != null) {
                    // 当appId=7时，对应的packageType就是当前租户的运营模式
                    response.setType(app.getPackageType());
                    break;
                }
            }
            log.info("getTenantOperationMode success, tenantId={}, operationMode={}",
                    tenantId,
                    OperationModeEnum.getByCode(response.getType()).getDesc());
            return response;
        } catch (Exception e) {
            log.error("getTenantOperationMode error, tenantId={}", tenantId, e);
            throw new DeepSightException.OperationModeException(ErrorCode.INTERNAL_ERROR, "获取运营模式失败");
        }
    }

    @Override
    public OperationModeEnum detectTenantOperationMode(String tenantId) {
        OperationModeResponse operationModeResponse = getTenantOperationMode(tenantId);
        if (2 == operationModeResponse.getType()) {
            return OperationModeEnum.OPERATION_BY_BAIDU_OP;
        } else {
            return OperationModeEnum.OPERATION_BY_SELF;
        }
    }
}
