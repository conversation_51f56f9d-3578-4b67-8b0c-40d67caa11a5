<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="add_builtin_field_to_label_table" author="system">
        <comment>为label表添加is_builtin字段，用于标识内置标签</comment>
        <sql>
            ALTER TABLE `label` ADD COLUMN `is_builtin` TINYINT(1) DEFAULT 0 
            COMMENT '是否为内置标签: 0-用户标签, 1-内置标签';
        </sql>
        <rollback>
            <sql>
                ALTER TABLE `label` DROP COLUMN `is_builtin`;
            </sql>
        </rollback>
    </changeSet>

</databaseChangeLog>
