package com.baidu.keyue.deepsight.models.profile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 外呼行为指标VO类
 *
 * <AUTHOR>
 * @date 2025/8/12 17:34
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiobMetricVO {

    /**
     * 接通率
     */
    private Float connectRate;

    /**
     * 首轮挂断率
     */
    private Float firstRoundHangupRate;

    /**
     * 平均对话轮次
     */
    private Float avgRounds;

    /**
     * 平均通话时长(s)
     */
    private Float avgDuration;

    /**
     * 最近一次呼叫距离今日天数
     */
    private Float daysSinceLastCall;

}
