CREATE TABLE `aiob_conversation_record_^&` (
  `EvtSequenceNumber` varchar(255) NOT NULL COMMENT '对话唯一id',
  `sessionId` varchar(255) NULL COMMENT '通话id',
  `roleType` varchar(255) NULL COMMENT '对话角色voice-客户侧 speech-坐席侧',
  `robotId` varchar(128) NULL COMMENT '机器人id',
  `taskId` varchar(128) NULL COMMENT '任务id',
  `content` text NULL COMMENT '文本内容:对话语音对应的文字内容',
  `contextText` text NULL COMMENT '回复文本:机器人侧回复文本',
  `createTime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `silent` boolean NULL COMMENT '静默时间',
  `timeLen` int NULL COMMENT '总时长:秒',
  `intent` varchar(255) NULL COMMENT '意图',
  `collectInfo` json NULL COMMENT '信息收集内容',
  `deepsight_datetime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '写入时间',
  `deepsight_update_datetime` datetime NULL COMMENT '更新时间',
  `oneId` varchar(255) NULL DEFAULT "" COMMENT '唯一ID',
  `start` varchar(255) NULL DEFAULT "" COMMENT '对话相对时间',
  `queryId` varchar(255) NULL DEFAULT "" COMMENT 'debug查询ID'
) ENGINE=OLAP
UNIQUE KEY(`EvtSequenceNumber`)
COMMENT '外呼对话内容表'
DISTRIBUTED BY HASH(`EvtSequenceNumber`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"enable_mow_light_delete" = "false"
);;


CREATE TABLE `aiob_conversation_record_debug_^&` (
   `queryId` varchar(80) NULL COMMENT 'debug info id',
   `sessionId` varchar(80) NULL COMMENT '通话 id',
   `topicId` varchar(255) NULL COMMENT '主题id',
   `nodeId` varchar(255) NULL COMMENT '节点id',
   `robot_id` varchar(255) NULL COMMENT '机器人 id',
   `robot_ver` varchar(255) NULL COMMENT '机器人版本',
   `chunkId` varchar(255) NULL COMMENT '节点顺序',
   `agent_id` varchar(255) NULL COMMENT 'agent_id',
   `version_id` varchar(255) NULL COMMENT 'version_id',
   `intent` array<varchar(128)> NULL DEFAULT "[]" COMMENT '意图'
) ENGINE=OLAP
DUPLICATE KEY(`queryId`)
COMMENT '外呼 sop 边统计明细表'
DISTRIBUTED BY HASH(`queryId`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"enable_mow_light_delete" = "false"
);;



CREATE TABLE `aiob_conversation_session_agg_^&` (
   `oneId` varchar(255) NULL COMMENT 'oneIdID',
   `call_date` datetime NULL COMMENT '通话日期（比如 2025-04-07）',
   `time_bucket` varchar(10) NULL COMMENT '时段（比如12-15)',
   `total_calls` bigint SUM NULL COMMENT '拨打总次数',
   `total_connected_calls` bigint SUM NULL COMMENT '接通总次数',
   `total_first_round_hangup` bigint SUM NULL COMMENT '首轮挂断次数（轮次为1）',
   `total_rounds` bigint SUM NULL COMMENT '对话总轮数',
   `total_duration_time` bigint SUM NULL COMMENT '通话总时长（单位秒）'
) ENGINE=OLAP
AGGREGATE KEY(`oneId`, `call_date`, `time_bucket`)
DISTRIBUTED BY HASH(`oneId`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"enable_mow_light_delete" = "false"
);;



CREATE TABLE `aiob_conversation_session_service_^&` (
   `sessionId` varchar(255) NOT NULL COMMENT 'saas对于一通电话的唯一表示',
   `memberId` varchar(255) NULL COMMENT '号码组ID',
   `taskName` varchar(255) NULL COMMENT '任务名称',
   `taskId` varchar(255) NULL COMMENT '错误详细信息',
   `taskType` varchar(10) NULL COMMENT '负责人id',
   `taskTypeDesc` varchar(255) NULL COMMENT '处理时间',
   `soundRecord` tinyint NULL COMMENT '1 允许试听，0 不允许试听',
   `sessionType` varchar(10) NULL COMMENT '会话类型：0-未知（洋钱罐） 1-呼入 2-呼出',
   `robotId` varchar(255) NULL COMMENT '机器人id',
   `robotName` varchar(255) NULL COMMENT '机器人名称',
   `cmdStatus` varchar(10) NULL COMMENT '状态',
   `mobile` varchar(255) NULL COMMENT '被叫号码 (已加密)',
   `mobileProvince` varchar(50) NULL COMMENT '被叫号码 省',
   `mobileCity` varchar(50) NULL COMMENT '被叫号码 市',
   `didOwner` varchar(128) NULL COMMENT '号码所属：0-平台，1-客户自有',
   `action` json NULL COMMENT '动作',
   `isRinging` boolean NULL COMMENT '是否响铃',
   `isAnswer` boolean NULL COMMENT '是否回复',
   `hangupReason` varchar(50) NULL COMMENT '挂断原因',
   `intent` varchar(50) NULL COMMENT '意图',
   `callTimes` int NULL COMMENT '号码维度呼叫次序',
   `callType` int NULL COMMENT '呼叫类型：0首次呼叫，1重试呼叫，2预约呼叫, 3实时呼叫',
   `completeType` int NULL COMMENT '电话结果 3, 未完成 2, 已完成',
   `durationTimeLen` int NULL COMMENT '拨打持续时长',
   `endType` tinyint NULL COMMENT '电话结果 0-已完成 1-待呼叫 2-未完成',
   `endTypeReason` varchar(255) NULL COMMENT '触发电话结果原因',
   `executiveStrategy` json NULL COMMENT '策略：完成节点',
   `fileId` largeint NULL COMMENT '导入文件的唯一标识',
   `isComplete` boolean NULL COMMENT '是否是完成节点',
   `isRobotHangup` boolean NULL COMMENT '是否机器人挂机',
   `smsStatus` tinyint NULL COMMENT '短信状态 0-成功 2-失败',
   `smsVar` json NULL COMMENT '短信变量信息',
   `transResult` tinyint NULL COMMENT '转人工状态 0：未发起 1：成功 -1：失败',
   `sysType` tinyint NULL COMMENT '任务类型',
   `startTime` datetime NULL COMMENT '开始时间',
   `endTime` datetime NULL COMMENT '挂断时间',
   `talkingStartTime` datetime NULL COMMENT '接通时间',
   `talkingTimeLen` int NULL COMMENT '通话时长',
   `talkingTurn` int NULL COMMENT '对话轮次',
   `audioCallPath` varchar(255) NULL COMMENT '录音地址',
   `ringStartTime` datetime NULL COMMENT '振铃时间',
   `ringingTimeLen` int NULL COMMENT '振铃时长',
   `createTime` datetime NULL COMMENT '创建时间',
   `updateTime` datetime NULL COMMENT '更新时间',
   `createTimePoint` bigint NULL COMMENT '创建时间后续用来做拉数据处理',
   `robotScene` tinyint NULL COMMENT '机器人场景',
   `robotSceneName` varchar(255) NULL COMMENT '是否机器人场景',
   `fromSource` varchar(50) NULL COMMENT '来源',
   `tagExtractInfo` json NULL COMMENT '机器人标签抽取信息',
   `conversationContent` text NULL COMMENT '对话内容',
   `recordCount` int NULL DEFAULT "0" COMMENT '通话轮数',
   `mobileMD5` varchar(255) NULL DEFAULT "" COMMENT '电话号码MD5',
   `tenantId` varchar(255) NULL DEFAULT "" COMMENT '租户id',
   `tenantName` varchar(255) NULL DEFAULT "" COMMENT '租户名称',
   `deepsight_datetime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '写入时间',
   `deepsight_update_datetime` datetime NULL COMMENT '更新时间',
   `BAIDUID` varchar(255) NULL DEFAULT "" COMMENT '百度域浏览器cookie',
   `IMEI` varchar(255) NULL DEFAULT "" COMMENT '国际移动设备身份码',
   `CUID` varchar(255) NULL DEFAULT "" COMMENT '百度定义移动设备唯一标示',
   `MAC` varchar(255) NULL DEFAULT "" COMMENT 'Mac 地址',
   `oneId` varchar(255) NULL DEFAULT "" COMMENT '唯一ID',
   `sipCode` varchar(255) NULL COMMENT '接通状态',
   `botVersionId` varchar(255) NULL DEFAULT "" COMMENT '机器人版本',
   `customTagList` array<varchar(128)> NULL DEFAULT "[]" COMMENT '意向',
   `callerNum` varchar(255) NULL COMMENT '主叫号码',
   `dicCategory` varchar(255) NULL COMMENT '字典类别-未接通类别',
   `dicName` varchar(255) NULL COMMENT '字典名称-未接通名称',
   `is_auto_answer` tinyint NULL COMMENT '是否为小秘书：1是，0否',
   `lineStatus` varchar(255) NULL DEFAULT "ENABLED" COMMENT '号线状态：ALL(全部状态)/ENABLED(启用中)/BANNED(已禁止)/ARREARS(已欠费)/DISABLED(已停用)',
   `taskStatus` tinyint NULL COMMENT '外呼任务:1-待启动/2-执行中/3-已暂停/4-已完成/5-已终止',
   `callerCity` varchar(255) NULL COMMENT '主叫号码城市',
   INDEX indexConversationContent (`conversationContent`) USING INVERTED PROPERTIES("parser" = "chinese", "lower_case" = "true", "support_phrase" = "true") COMMENT 'conversationContent倒排索引'
) ENGINE=OLAP
UNIQUE KEY(`sessionId`)
COMMENT 'service table'
DISTRIBUTED BY HASH(`sessionId`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"enable_mow_light_delete" = "false"
);;



CREATE TABLE `aiob_sop_edge_metric_^&` (
     `cal_date` datetime NULL COMMENT '计算时间',
     `task_id` varchar(255) NULL COMMENT '任务 id',
     `robot_id` varchar(255) NULL COMMENT '机器人 id',
     `robot_ver` varchar(255) NULL COMMENT '机器人版本',
     `topic_id` varchar(255) NULL COMMENT '主题id',
     `from_node` varchar(255) NULL COMMENT '边起始节点 id',
     `end_node` varchar(255) NULL COMMENT '边到达节点 id',
     `oneId` varchar(255) NULL COMMENT 'oneId',
     `sessionId` varchar(255) NULL DEFAULT "" COMMENT '通话id'
) ENGINE=OLAP
DUPLICATE KEY(`cal_date`, `task_id`)
COMMENT '外呼 sop 边统计明细表'
DISTRIBUTED BY HASH(`task_id`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"enable_mow_light_delete" = "false"
);;



CREATE TABLE `aiob_sop_node_metric_^&` (
  `cal_date` datetime NULL COMMENT '计算时间',
  `task_id` varchar(255) NULL DEFAULT "" COMMENT '任务 id',
  `robot_id` varchar(255) NULL DEFAULT "" COMMENT '机器人 id',
  `robot_ver` varchar(255) NULL DEFAULT "" COMMENT '机器人版本',
  `topic_id` varchar(255) NULL DEFAULT "" COMMENT '主题id',
  `node_id` varchar(255) NULL DEFAULT "" COMMENT '当前节点 id',
  `oneId` varchar(255) NULL DEFAULT "" COMMENT 'oneId',
  `hangup` int NULL DEFAULT "0" COMMENT '异常挂断电话',
  `intent` array<varchar(128)> NULL DEFAULT "[]" COMMENT '意图节点',
  `sessionId` varchar(255) NULL DEFAULT "" COMMENT '通话id'
) ENGINE=OLAP
DUPLICATE KEY(`cal_date`, `task_id`)
COMMENT '外呼 sop 节点统计明细表'
DISTRIBUTED BY HASH(`task_id`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"enable_mow_light_delete" = "false"
);;



CREATE TABLE `icsoc_customer_^&` (
    `id` varchar(255) NOT NULL COMMENT '主键id',
    `customer_name` varchar(255) NOT NULL COMMENT '客户名称',
    `mobile` array<bigint> NOT NULL COMMENT '电话',
    `source` varchar(255) NOT NULL COMMENT '来源',
    `mail` array<varchar(128)> NULL DEFAULT "[]" COMMENT '邮箱',
    `age` int NULL COMMENT '年龄'
) ENGINE=OLAP
UNIQUE KEY(`id`)
DISTRIBUTED BY HASH(`id`) BUCKETS 2
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"enable_mow_light_delete" = "false"
);;



CREATE TABLE `id_mapping_^&` (
    `oneId` varchar(64) NOT NULL COMMENT '唯一id',
    `user_id` array<varchar(255)> NULL DEFAULT "[]",
    `mobile` array<varchar(255)> NULL DEFAULT "[]",
    `BAIDUID` array<varchar(255)> NULL DEFAULT "[]",
    `UNIONID` array<varchar(255)> NULL DEFAULT "[]",
    `DEVICEID` array<varchar(255)> NULL DEFAULT "[]",
    `IMEI` array<varchar(255)> NULL DEFAULT "[]",
    `CUID` array<varchar(255)> NULL DEFAULT "[]",
    `MAC` array<varchar(255)> NULL DEFAULT "[]",
    `IDFA` array<varchar(255)> NULL DEFAULT "[]",
    `OAID` array<varchar(255)> NULL DEFAULT "[]",
    `anonymous_id` array<varchar(255)> NULL DEFAULT "[]",
    `taskId` array<varchar(255)> NULL DEFAULT "[]"
) ENGINE=OLAP
UNIQUE KEY(`oneId`)
COMMENT 'id mapping 表'
DISTRIBUTED BY HASH(`oneId`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"enable_mow_light_delete" = "false"
);;




CREATE TABLE `keyue_conversation_record_^&` (
   `queryId` varchar(255) NOT NULL COMMENT '对话id：同一个sessionid内，每次对话都有一个对应的queryid',
   `user_id` varchar(128) NULL COMMENT '用户id',
   `sessionId` varchar(255) NULL COMMENT '回话id：首轮会返回sessionId,后续对话需使用返回的sessionId',
   `queryTime` datetime NULL COMMENT '用户请求的时间',
   `queryText` text NULL COMMENT '请求的问题',
   `answerText` text NULL COMMENT '机器人回答内容',
   `userName` varchar(128) NULL COMMENT '用户名称',
   `variables` json NULL COMMENT 'Map格式，key 是变量名称，value是变量值。对话中使用的变量，如果传了会覆盖当前中对话中已有的变量值',
   `tenantId` varchar(128) NULL COMMENT '租户id',
   `endTime` datetime NULL COMMENT '回答结束时间',
   `intent` json NULL COMMENT '意图',
   `agentId` varchar(128) NULL COMMENT '机器人id',
   `deepsight_datetime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `deepsight_update_datetime` datetime NULL COMMENT '更新时间',
   `BAIDUID` varchar(255) NULL DEFAULT "" COMMENT '百度域浏览器cookie',
   `UNIONID` varchar(255) NULL DEFAULT "" COMMENT '微信公众号用户UnionID',
   `IMEI` varchar(255) NULL DEFAULT "" COMMENT '国际移动设备身份码',
   `CUID` varchar(255) NULL DEFAULT "" COMMENT '百度定义移动设备唯一标示',
   `MAC` varchar(255) NULL DEFAULT "" COMMENT 'Mac地址',
   `anonymous_id` varchar(255) NULL DEFAULT "" COMMENT '匿名访客ID',
   `oneId` varchar(255) NULL DEFAULT "" COMMENT '唯一ID'
) ENGINE=OLAP
UNIQUE KEY(`queryId`)
COMMENT '客服对话内容表'
DISTRIBUTED BY HASH(`queryId`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"enable_mow_light_delete" = "false"
);;




CREATE TABLE `memory_extract_info_^&` (
    `id` bigint NOT NULL DEFAULT "0" COMMENT '唯一 id',
    `user_id` varchar(255) NULL DEFAULT "" COMMENT 'oneid',
    `external_id` varchar(255) NULL DEFAULT "" COMMENT '外部 ID',
    `extract_date` datetime NOT NULL COMMENT '抽取时间',
    `memory_id` bigint NOT NULL COMMENT '记忆提取配置ID',
    `dataset_id` bigint NOT NULL COMMENT '数据集 ID',
    `memory_content` varchar(255) NOT NULL DEFAULT "" COMMENT '记忆内容',
    `memory_type` varchar(128) NOT NULL DEFAULT "" COMMENT '记忆类型',
    `deepsight_datetime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '写入时间',
    `deepsight_update_datetime` datetime NULL COMMENT '更新时间',
    `oneId` varchar(255) NULL DEFAULT "" COMMENT '唯一ID'
) ENGINE=OLAP
UNIQUE KEY(`id`, `user_id`)
COMMENT '记忆提取结果表'
DISTRIBUTED BY HASH(`user_id`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"enable_mow_light_delete" = "false"
);;




CREATE TABLE `mock_user_^&` (
   `user_id` varchar(255) NOT NULL COMMENT '用户id',
   `user_name` varchar(255) NULL COMMENT '用户昵称',
   `age` int NULL COMMENT '用户年龄',
   `age_group` varchar(255) NULL COMMENT '用户年龄段',
   `city` varchar(255) NULL COMMENT '用户城市',
   `gender` varchar(128) NULL COMMENT '用户性别',
   `register_time` datetime NULL COMMENT '注册时间',
   `update_time` datetime NULL COMMENT '更新时间',
   `tags` varchar(255) NULL COMMENT '用户标签',
   `area` varchar(255) NULL COMMENT '商圈',
   `country` varchar(255) NULL COMMENT '用户国家',
   `DEVICEID` varchar(255) NULL COMMENT '设备标识',
   `device_model` varchar(255) NULL COMMENT '设备型号',
   `district` varchar(255) NULL COMMENT '用户所在区县',
   `membership_level` varchar(255) NULL COMMENT '会员等级',
   `os` varchar(255) NULL COMMENT '操作系统',
   `province` varchar(255) NULL COMMENT '用户省份',
   `user_type` varchar(255) NULL COMMENT '用户类型',
   `income_level` varchar(255) NULL COMMENT '收入水平',
   `education_level` varchar(255) NULL COMMENT '教育水平',
   `life_stage` varchar(255) NULL COMMENT '人生阶段',
   `industry` varchar(255) NULL COMMENT '所在行业',
   `occupation` varchar(255) NULL COMMENT '职业类型',
   `city_level` varchar(255) NULL COMMENT '常驻城市线级',
   `mobile` varchar(255) NULL COMMENT '手机',
   `email_address` varchar(255) NULL COMMENT '邮箱',
   `wechat_id` varchar(255) NULL COMMENT '微信',
   `predict_gender` varchar(8) NULL DEFAULT "" COMMENT '预测性别',
   `predict_age_group` varchar(32) NULL DEFAULT "" COMMENT '预测年龄',
   `predict_life_stage` varchar(32) NULL DEFAULT "" COMMENT '预测人生阶段',
   `predict_marriage_status` varchar(8) NULL DEFAULT "" COMMENT '预测婚姻状况',
   `predict_industry` varchar(32) NULL DEFAULT "" COMMENT '预测所在行业',
   `predict_education_level` varchar(32) NULL DEFAULT "" COMMENT '预测教育水平',
   `predict_occupation` varchar(32) NULL DEFAULT "" COMMENT '预测职业类别',
   `predict_consume_level` varchar(8) NULL DEFAULT "" COMMENT '预测消费水平',
   `predict_consume_intent` varchar(8) NULL DEFAULT "" COMMENT '预测消费意愿',
   `predict_geographic_location` varchar(128) NULL DEFAULT "" COMMENT '预测地理位置',
   `predict_interests` varchar(256) NULL DEFAULT "" COMMENT '预测兴趣关注',
   `merge_gender` varchar(8) NULL DEFAULT "" COMMENT '合并性别',
   `merge_age_group` varchar(32) NULL DEFAULT "" COMMENT '合并年龄',
   `merge_life_stage` varchar(32) NULL DEFAULT "" COMMENT '合并人生阶段',
   `merge_marriage_status` varchar(8) NULL DEFAULT "" COMMENT '合并婚姻状况',
   `merge_industry` varchar(32) NULL DEFAULT "" COMMENT '合并所在行业',
   `merge_education_level` varchar(32) NULL DEFAULT "" COMMENT '合并教育水平',
   `merge_occupation` varchar(32) NULL DEFAULT "" COMMENT '合并职业类别',
   `merge_consume_level` varchar(8) NULL DEFAULT "" COMMENT '合并消费水平',
   `merge_consume_intent` varchar(8) NULL DEFAULT "" COMMENT '合并消费意愿',
   `merge_geographic_location` varchar(128) NULL DEFAULT "" COMMENT '合并地理位置',
   `merge_interests` varchar(256) NULL DEFAULT "" COMMENT '合并兴趣关注',
   `bd_gender` varchar(8) NULL DEFAULT "" COMMENT '百度性别',
   `bd_age_group` varchar(32) NULL DEFAULT "" COMMENT '百度年龄',
   `bd_life_stage` varchar(32) NULL DEFAULT "" COMMENT '百度人生阶段',
   `bd_marriage_status` varchar(8) NULL DEFAULT "" COMMENT '百度婚姻状况',
   `bd_industry` varchar(32) NULL DEFAULT "" COMMENT '百度所在行业',
   `bd_education_level` varchar(32) NULL DEFAULT "" COMMENT '百度教育水平',
   `bd_occupation` varchar(32) NULL DEFAULT "" COMMENT '百度职业类别',
   `bd_consume_level` varchar(8) NULL DEFAULT "" COMMENT '百度消费水平',
   `bd_consume_intent` varchar(128) NULL DEFAULT "" COMMENT '百度消费意愿',
   `bd_geographic_location` varchar(128) NULL DEFAULT "" COMMENT '百度地理位置',
   `bd_interests` varchar(2048) NULL DEFAULT "" COMMENT '百度兴趣关注',
   `deepsight_datetime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '写入时间',
   `deepsight_update_datetime` datetime NULL COMMENT '更新时间',
   `mobile_list` array<varchar(128)> NULL DEFAULT "[]" COMMENT '手机号，多值',
   `email_list` array<varchar(128)> NULL DEFAULT "[]" COMMENT '邮箱，多值',
   `source` varchar(128) NULL COMMENT '来源',
   `idm_baiduid` varchar(255) NULL DEFAULT "" COMMENT 'idm_baiduid',
   `idm_userid` array<varchar(255)> NULL DEFAULT "[]" COMMENT 'idm_userid',
   `idm_cuid` array<varchar(255)> NULL DEFAULT "[]" COMMENT 'idm_cuid',
   `idm_imei` array<varchar(255)> NULL DEFAULT "[]" COMMENT 'idm_imei',
   `idm_mac` array<varchar(255)> NULL DEFAULT "[]" COMMENT 'idm_mac',
   `idm_idfa` array<varchar(255)> NULL DEFAULT "[]" COMMENT 'idm_idfa',
   `idm_oaid` array<varchar(255)> NULL DEFAULT "[]" COMMENT 'idm_oaid',
   `IMEI` varchar(255) NULL DEFAULT "" COMMENT '国际移动设备身份码',
   `BAIDUID` varchar(255) NULL DEFAULT "" COMMENT '百度域浏览器cookie',
   `CUID` varchar(255) NULL DEFAULT "" COMMENT '百度定义移动设备唯一标示',
   `USERID` varchar(255) NULL DEFAULT "" COMMENT '百度注册id',
   `MAC` varchar(255) NULL DEFAULT "" COMMENT 'Mac 地址',
   `UNIONID` varchar(255) NULL DEFAULT "" COMMENT '微信公众号用户UnionID',
   `IDFA` varchar(255) NULL DEFAULT "" COMMENT 'apple提供给广告主的设备唯一ID',
   `OAID` varchar(255) NULL DEFAULT "" COMMENT 'OAID的设备标识',
   `anonymous_id` varchar(255) NULL DEFAULT "" COMMENT '匿名访客ID',
   `oneId` varchar(255) NULL DEFAULT "" COMMENT '唯一ID',
   `process_customer_311` varchar(150) NULL DEFAULT "1",
   `process_label_639` array<varchar(150)> NULL DEFAULT "[]",
   `process_label_640` array<varchar(150)> NULL DEFAULT "[]",
   `process_label_641` array<varchar(150)> NULL DEFAULT "[]",
   `process_label_642` array<varchar(150)> NULL DEFAULT "[]",
   `process_label_643` array<varchar(150)> NULL DEFAULT "[]"
) ENGINE=OLAP
UNIQUE KEY(`user_id`)
COMMENT 'default user table'
DISTRIBUTED BY HASH(`user_id`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"enable_mow_light_delete" = "false"
);;



CREATE TABLE `user_metric_^&` (
    `oneId` varchar(255) NOT NULL COMMENT '用户ID，平台维度唯一标识，不能为空',
    `connect_rate` double NOT NULL COMMENT '接通率，取值范围0-1，不能为空',
    `time_bucket_statistics` json NOT NULL COMMENT '各时间段接通情况，JSON格式，示例：{\"0-3\":{\"count\":12,\"percent\":0.3512}}，不能为空',
    `first_round_hangup_rate` double NOT NULL COMMENT '首轮挂断率，取值范围0-1，不能为空',
    `avg_rounds` double NOT NULL COMMENT '平均对话轮次，不能为空',
    `avg_duration` double NOT NULL COMMENT '平均通话时长，单位：秒，不能为空'
) ENGINE=OLAP
UNIQUE KEY(`oneId`)
DISTRIBUTED BY HASH(`oneId`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"enable_mow_light_delete" = "false"
);;





CREATE TABLE `user_profile_^&` (
      `oneId` varchar(64) NULL DEFAULT "全局唯一id",
      `deepsight_datetime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '写入时间',
      `deepsight_update_datetime` datetime NULL COMMENT '更新时间',
      `user_id` varchar(255) NULL COMMENT '用户id',
      `user_name` varchar(255) NULL COMMENT '用户昵称',
      `age` int NULL COMMENT '用户年龄',
      `age_group` varchar(255) NULL COMMENT '用户年龄段',
      `city` varchar(255) NULL COMMENT '用户城市',
      `gender` varchar(128) NULL COMMENT '用户性别',
      `register_time` datetime NULL COMMENT '注册时间',
      `update_time` datetime NULL COMMENT '更新时间',
      `tags` varchar(255) NULL COMMENT '用户标签',
      `area` varchar(255) NULL COMMENT '商圈',
      `country` varchar(255) NULL COMMENT '用户国家',
      `device_id` varchar(255) NULL COMMENT '设备标识',
      `device_model` varchar(255) NULL COMMENT '设备型号',
      `district` varchar(255) NULL COMMENT '用户所在区县',
      `membership_level` varchar(255) NULL COMMENT '会员等级',
      `os` varchar(255) NULL COMMENT '操作系统',
      `province` varchar(255) NULL COMMENT '用户省份',
      `user_type` varchar(255) NULL COMMENT '用户类型',
      `income_level` varchar(255) NULL COMMENT '收入水平',
      `education_level` varchar(255) NULL COMMENT '教育水平',
      `life_stage` varchar(255) NULL COMMENT '人生阶段',
      `industry` varchar(255) NULL COMMENT '所在行业',
      `occupation` varchar(255) NULL COMMENT '职业类型',
      `city_level` varchar(255) NULL COMMENT '常驻城市线级',
      `mobile` varchar(255) NULL COMMENT '手机',
      `email_address` varchar(255) NULL COMMENT '邮箱',
      `wechat_id` varchar(255) NULL COMMENT '微信',
      `mobile_list` array<varchar(128)> NULL DEFAULT "[]" COMMENT '手机号，多值',
      `email_list` array<varchar(128)> NULL DEFAULT "[]" COMMENT '邮箱，多值',
      `source` varchar(128) NULL COMMENT '来源',
      `predict_gender` varchar(16) NULL DEFAULT "" COMMENT '预测性别',
      `predict_age_group` varchar(64) NULL DEFAULT "" COMMENT '预测年龄',
      `predict_life_stage` varchar(64) NULL DEFAULT "" COMMENT '预测人生阶段',
      `predict_marriage_status` varchar(16) NULL DEFAULT "" COMMENT '预测婚姻状况',
      `predict_industry` varchar(64) NULL DEFAULT "" COMMENT '预测所在行业',
      `predict_education_level` varchar(64) NULL DEFAULT "" COMMENT '预测教育水平',
      `predict_occupation` varchar(64) NULL DEFAULT "" COMMENT '预测职业类别',
      `predict_consume_level` varchar(16) NULL DEFAULT "" COMMENT '预测消费水平',
      `predict_consume_intent` varchar(512) NULL DEFAULT "" COMMENT '预测消费意愿',
      `predict_geographic_location` varchar(512) NULL DEFAULT "" COMMENT '预测地理位置',
      `predict_interests` varchar(2048) NULL DEFAULT "" COMMENT '预测兴趣关注',
      `merge_gender` varchar(16) NULL DEFAULT "" COMMENT '合并性别',
      `merge_age_group` varchar(64) NULL DEFAULT "" COMMENT '合并年龄',
      `merge_life_stage` varchar(64) NULL DEFAULT "" COMMENT '合并人生阶段',
      `merge_marriage_status` varchar(16) NULL DEFAULT "" COMMENT '合并婚姻状况',
      `merge_industry` varchar(64) NULL DEFAULT "" COMMENT '合并所在行业',
      `merge_education_level` varchar(64) NULL DEFAULT "" COMMENT '合并教育水平',
      `merge_occupation` varchar(64) NULL DEFAULT "" COMMENT '合并职业类别',
      `merge_consume_level` varchar(16) NULL DEFAULT "" COMMENT '合并消费水平',
      `merge_consume_intent` varchar(512) NULL DEFAULT "" COMMENT '合并消费意愿',
      `merge_geographic_location` varchar(512) NULL DEFAULT "" COMMENT '合并地理位置',
      `merge_interests` varchar(2048) NULL DEFAULT "" COMMENT '合并兴趣关注',
      `bd_gender` varchar(16) NULL DEFAULT "" COMMENT '百度性别',
      `bd_age_group` varchar(32) NULL DEFAULT "" COMMENT '百度年龄',
      `bd_life_stage` varchar(32) NULL DEFAULT "" COMMENT '百度人生阶段',
      `bd_marriage_status` varchar(8) NULL DEFAULT "" COMMENT '百度婚姻状况',
      `bd_industry` varchar(32) NULL DEFAULT "" COMMENT '百度所在行业',
      `bd_education_level` varchar(32) NULL DEFAULT "" COMMENT '百度教育水平',
      `bd_occupation` varchar(32) NULL DEFAULT "" COMMENT '百度职业类别',
      `bd_consume_level` varchar(8) NULL DEFAULT "" COMMENT '百度消费水平',
      `bd_consume_intent` varchar(512) NULL DEFAULT "" COMMENT '百度消费意愿',
      `bd_geographic_location` varchar(512) NULL DEFAULT "" COMMENT '百度地理位置',
      `bd_interests` varchar(2048) NULL DEFAULT "" COMMENT '百度兴趣关注',
      `DEVICEID` varchar(255) NULL DEFAULT "" COMMENT '设备IDvarchar',
      `IMEI` varchar(255) NULL DEFAULT "" COMMENT '国际移动设备身份码',
      `BAIDUID` varchar(255) NULL DEFAULT "" COMMENT '百度域浏览器cookie',
      `CUID` varchar(255) NULL DEFAULT "" COMMENT '百度定义移动设备唯一标示',
      `USERID` varchar(255) NULL DEFAULT "" COMMENT '百度注册id',
      `MAC` varchar(255) NULL DEFAULT "" COMMENT 'Mac 地址',
      `UNIONID` varchar(255) NULL DEFAULT "" COMMENT '微信公众号用户UnionID',
      `IDFA` varchar(255) NULL DEFAULT "" COMMENT 'apple提供给广告主的设备唯一ID',
      `OAID` varchar(255) NULL DEFAULT "" COMMENT 'OAID的设备标识',
      `anonymous_id` varchar(255) NULL DEFAULT "" COMMENT '匿名访客ID',
      `process_customer_225` varchar(150) NULL DEFAULT "1",
      `process_label_529` array<varchar(150)> NULL DEFAULT "[]",
      `process_label_530` array<varchar(150)> NULL DEFAULT "[]",
      `process_label_531` array<varchar(150)> NULL DEFAULT "[]",
      `process_label_532` array<varchar(150)> NULL DEFAULT "[]",
      `process_label_533` array<varchar(150)> NULL DEFAULT "[]"
) ENGINE=OLAP
UNIQUE KEY(`oneId`)
COMMENT '用户档案表'
DISTRIBUTED BY HASH(`oneId`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"enable_mow_light_delete" = "false"
);;


