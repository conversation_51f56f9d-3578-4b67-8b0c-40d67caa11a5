package com.baidu.keyue.deepsight.constants;

/**
 * SQL常量管理类
 * 统一管理项目中的SQL查询语句模板，避免硬编码
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
public final class SqlConstants {

    private SqlConstants() {
        // 工具类，禁止实例化
    }

    /**
     * 数值常量
     */
    public static final class NumericConstants {

        /**
         * 自动应答标识：非自动应答
         */
        public static final int NOT_AUTO_ANSWER = 0;

        /**
         * 风险评估最大对话记录数
         */
        public static final int MAX_CONVERSATION_RECORDS = 5;

        /**
         * LLM服务模拟延迟（毫秒）
         */
        public static final int LLM_MOCK_DELAY_MS = 100;

        private NumericConstants() {
            // 禁止实例化
        }
    }

    /**
     * 动态目录相关SQL常量
     */
    public static final class DynamicCatalog {
        
        /**
         * 查询机器人名称列表SQL模板
         * 参数：表名
         */
        public static final String QUERY_ROBOT_NAMES = 
            "SELECT DISTINCT robotName FROM %s " +
            "WHERE robotName IS NOT NULL AND robotName != '' AND TRIM(robotName) != ''";

        /**
         * 查询tagExtractInfo和robotName映射SQL模板
         * 参数：表名
         */
        public static final String QUERY_TAG_EXTRACT_WITH_ROBOT_NAME = 
            "SELECT DISTINCT robotName, CAST(tagExtractInfo AS STRING) as tagExtractInfo FROM %s " +
            "WHERE tagExtractInfo IS NOT NULL AND CAST(tagExtractInfo AS STRING) != '' AND TRIM(CAST(tagExtractInfo AS STRING)) != '' " +
            "AND robotName IS NOT NULL AND robotName != ''";

        private DynamicCatalog() {
            // 禁止实例化
        }
    }

    /**
     * 标签字段验证相关SQL常量
     */
    public static final class LabelFieldValidation {
        
        /**
         * 数组字段数据验证SQL模板
         * 参数：表名, 字段名, 字段名, WHERE子句
         */
        public static final String VALIDATE_ARRAY_FIELD = 
            "SELECT COUNT(*) as count FROM %s WHERE %s IS NOT NULL AND array_size(%s) > 0%s";

        /**
         * 日期时间字段数据验证SQL模板
         * 参数：表名, 字段名, WHERE子句
         */
        public static final String VALIDATE_DATETIME_FIELD = 
            "SELECT COUNT(*) as count FROM %s WHERE %s IS NOT NULL%s";

        /**
         * 普通字符串字段数据验证SQL模板
         * 参数：表名, 字段名, 字段名, WHERE子句
         */
        public static final String VALIDATE_STRING_FIELD = 
            "SELECT COUNT(*) as count FROM %s WHERE %s IS NOT NULL AND %s != ''%s";

        private LabelFieldValidation() {
            // 禁止实例化
        }
    }

    /**
     * 字段类型判断常量
     */
    public static final class FieldType {
        
        /**
         * 数组字段标识符
         */
        public static final String[] ARRAY_FIELD_INDICATORS = {
            "customTagList", "tagExtractInfo", "list", "array"
        };

        /**
         * 日期时间字段标识符
         */
        public static final String[] DATETIME_FIELD_INDICATORS = {
            "startTime", "endTime", "time", "date"
        };

        private FieldType() {
            // 禁止实例化
        }
    }

    /**
     * SQL转义相关常量
     */
    public static final class SqlEscape {
        
        /**
         * 单引号转义
         */
        public static final String SINGLE_QUOTE_ESCAPE = "''";
        
        /**
         * 单引号
         */
        public static final String SINGLE_QUOTE = "'";

        private SqlEscape() {
            // 禁止实例化
        }
    }

    /**
     * 通用查询条件常量
     */
    public static final class CommonConditions {

        /**
         * robotName条件模板
         * 参数：robotName值
         */
        public static final String ROBOT_NAME_CONDITION = " AND robotName = '%s'";

        /**
         * 空字符串
         */
        public static final String EMPTY_STRING = "";

        private CommonConditions() {
            // 禁止实例化
        }
    }

    /**
     * 内置标签查询相关SQL常量
     */
    public static final class BuiltinLabelQuery {

        /**
         * 查询意向标签数据SQL模板（只查最新一条记录）
         * 参数：表名, robotName
         */
        public static final String QUERY_INTENTION_LABEL_LATEST =
            "SELECT oneId, customTagList FROM (" +
            "  SELECT oneId, customTagList, " +
            "         ROW_NUMBER() OVER (PARTITION BY oneId ORDER BY startTime DESC) as rn " +
            "  FROM %s " +
            "  WHERE robotName = '%s' AND customTagList IS NOT NULL AND array_size(customTagList) > 0" +
            ") ranked " +
            "WHERE rn = 1";

        /**
         * 查询LLM标签数据SQL模板（只查最新一条记录）
         * 参数：表名, robotName, labelKey
         */
        public static final String QUERY_LLM_LABEL_LATEST =
            "SELECT oneId, CAST(tagExtractInfo AS STRING) as tagExtractInfo FROM (" +
            "  SELECT oneId, tagExtractInfo, " +
            "         ROW_NUMBER() OVER (PARTITION BY oneId ORDER BY startTime DESC) as rn " +
            "  FROM %s " +
            "  WHERE robotName = '%s' AND tagExtractInfo IS NOT NULL AND CAST(tagExtractInfo AS STRING) != '' " +
            "  AND CAST(tagExtractInfo AS STRING) LIKE '%%\"key\":\"%s\"%%'" +
            ") ranked " +
            "WHERE rn = 1";

        /**
         * 查询风险评估时间数据SQL模板
         * 参数：表名
         */
        public static final String QUERY_RISK_TIME_DATA =
            "SELECT oneId, MAX(startTime) as lastCallDate FROM %s WHERE is_auto_answer = " + NumericConstants.NOT_AUTO_ANSWER + " GROUP BY oneId";

        /**
         * 查询风险评估内容数据SQL模板（每个用户最多5条记录）
         * 参数：表名
         */
        public static final String QUERY_RISK_CONTENT_DATA =
            "SELECT oneId, conversationContent, startTime FROM (" +
            "  SELECT oneId, conversationContent, startTime, " +
            "         ROW_NUMBER() OVER (PARTITION BY oneId ORDER BY startTime DESC) as rn " +
            "  FROM %s " +
            "  WHERE is_auto_answer = " + NumericConstants.NOT_AUTO_ANSWER + " AND conversationContent IS NOT NULL AND conversationContent != ''" +
            ") ranked " +
            "WHERE rn <= " + NumericConstants.MAX_CONVERSATION_RECORDS + " " +
            "ORDER BY oneId, startTime DESC";

        private BuiltinLabelQuery() {
            // 禁止实例化
        }
    }

    /**
     * 数据库字段名常量
     */
    public static final class FieldNames {

        /**
         * 机器人名称字段
         */
        public static final String ROBOT_NAME = "robotName";

        /**
         * 标签提取信息字段
         */
        public static final String TAG_EXTRACT_INFO = "tagExtractInfo";

        /**
         * oneId字段
         */
        public static final String ONE_ID = "oneId";

        /**
         * 自定义标签列表字段
         */
        public static final String CUSTOM_TAG_LIST = "customTagList";

        /**
         * 对话内容字段
         */
        public static final String CONVERSATION_CONTENT = "conversationContent";


        /**
         * 最后通话日期字段
         */
        public static final String LAST_CALL_DATE = "lastCallDate";

        /**
         * JSON中的key字段
         */
        public static final String JSON_KEY = "key";

        /**
         * JSON中的value字段
         */
        public static final String JSON_VALUE = "value";

        /**
         * 查询结果计数字段
         */
        public static final String COUNT = "count";

        private FieldNames() {
            // 禁止实例化
        }
    }

    /**
     * 内置标签操作SQL模板常量
     */
    public static final class BuiltinLabelSql {

        /**
         * 更新用户宽表SQL模板
         * 参数：表名, 字段名, 字段值, oneId
         */
        public static final String UPDATE_USER_LABEL =
            "UPDATE %s SET %s = %s WHERE oneId = '%s'";

        private BuiltinLabelSql() {
            // 禁止实例化
        }
    }

    /**
     * 字符串常量
     */
    public static final class StringConstants {

        /**
         * 意向标签后缀
         */
        public static final String INTENTION_LABEL_SUFFIX = "_意向标签";

        /**
         * 标签字段名前缀
         */
        public static final String LABEL_FIELD_PREFIX = "process_label_";

        /**
         * 空数组字符串
         */
        public static final String EMPTY_ARRAY = "[]";

        /**
         * NULL字符串
         */
        public static final String NULL_STRING = "NULL";

        /**
         * 空格分隔符
         */
        public static final String SPACE_SEPARATOR = " ";

        /**
         * 逗号分隔符
         */
        public static final String COMMA_SEPARATOR = ", ";

        /**
         * 单引号转义字符
         */
        public static final String SINGLE_QUOTE_ESCAPE = "\\'";

        private StringConstants() {
            // 禁止实例化
        }
    }
}
