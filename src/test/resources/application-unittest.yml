server:
  port: 8080
  servlet:
    context-path: /deepsight/v1
mybatis:
  mapper-locations: classpath:mapper/mysqldb/*.xml
spring:
  liquibase:
    enabled: true
    change-log: classpath:/db.changelog/master.xml
    user: root
    password: znkf@2024
    url: *******************************************************************************************************************
    drop-first: false
  application:
    name: deep-sight-platform
  jpa:
    show-sql: true
    hibernate:
      show-sql: true
  datasource:
    aiob:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *********************************************************************************************************
      username: root
      password: 123456a?
    mysql:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *******************************************************************************************************************
      username: root
      password: znkf@2024
    doris:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *******************************************************************************************************************
      db: "deep_sight_dev"
      username: admin
      password: znkf_2024
  data:
    elasticsearch:
      client:
        reactive:
          hosts: *************
          port: 8203
          username: elastic
          password: bwfCP6CEQqKQ7F37ZbXH

  kafka:
    enable: true
    bootstrap-servers: **************:9095,*************:9095,*************:9095
    producer:
      acks: 1
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      enable-auto-commit: false
      auto-offset-reset: latest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      max-poll-records: 300
      auto-commit-interval: 100
    properties:
      security.protocol: SASL_SSL
      ssl:
        protocol: TLSv1.2
        truststore:
          location: client.truststore.jks
          password: bms@kafka
        endpoint:
          identification.algorithm:
          protocols: TLSv1.2,TLSv1.1,TLSv1
      sasl:
        mechanism: PLAIN
        jaas.config: org.apache.kafka.common.security.plain.PlainLoginModule required username="deepsight_root" password="znkf@2024";
keyueBase:
  accessKey: dongcha123456
  secretKey: dongcha123456
  # 用户中心配置
  user:
    host: http://*************:8030
    auth:
      # 是否开启LoginFilter进行会话、权限校验
      enable: false
      # 校验url路径模式（多个逗号分隔）
      urlPatterns: '/*'
      # 忽略校验的路径前缀（多个逗号分隔，可为空）
      ignorePathPrefixes: '/deepsight/v1/actuator/health,/deepsight/v1/table/content/sync'
      # 尝试获取用户信息，但获取失败不阻止后续处理请求（多个逗号分隔，可为空）
      tryGetUserPaths: ''
  # 运营管理平台配置
  admin:
    host: http://*************:8030

kafka:
  topics:
    dataSync: "deep_sight_append_dataset_data_sandbox"
redis:
  host: "*************"
  port: "8032"
  password: "znkf_2024"
  prefix: "deepsight_dev"

bsc:
  # 本地调试："http://bjdd-admin-00.bjdd:8220"
  # bcc 环境访问地址："http://bsc-api.bj.baidubce.com"
  endpoint: "http://bjdd-admin-00.bjdd:8220"
  vpcId: "vpc-f3ndwf8e0077"
  subnetId: "sbn-s72h4xsia6sr"
  logicalZone: "zoneF"
  securityGroupId: "g-p40e1bfwhrar"
  cidr: "**********/24"
  jobPrefix: "deepsight_dev"

label-calculate:
  db: "deep_sight_dev"
  table: "mock_user"
  primaryKey: "mobile"
  labelType: "array"
  jar: "demo-1.0.47-SNAPSHOT.jar"
  resourceId: "44ca1b640b84cc130b0aac51d591e64c"

customer-calculate:
  db: "deep_sight_dev"
  table: "mock_user"
  primaryKey: "mobile"
  customerType: "varchar"
  jar: "demo-1.0.47-SNAPSHOT-jar-with-dependencies.jar"
  resourceId: "2a2812fdab7c3a7e27df0e71bffbfa5d"

switch:
  execScheduled: false
  aiobAllDataSync: true
  labelCalSchedulerWithBSC: false
  customerCalSchedulerWithBSC: false
  loginAuthSkip: true
# 发版版本号，以1.0 beat版本为version-1递增
app-version: 7
