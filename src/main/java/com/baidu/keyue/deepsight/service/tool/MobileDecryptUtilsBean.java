package com.baidu.keyue.deepsight.service.tool;

import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.datamanage.impl.TableRecordCommonService;
import com.baidu.keyue.deepsight.utils.MobileProcessUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 手机号码解密工具类
 *
 * <AUTHOR>
 * @date 2025/8/13 19:58
 */
@Component
@Slf4j
public class MobileDecryptUtilsBean {

    @Autowired
    private DataTableManageService dataTableManageService;

    @Autowired
    private TableRecordCommonService tableRecordCommonService;

    /**
     * 解密 目标tableName 的手机号码秘文
     *
     * @param encryptedMobile 手机号秘文
     * @param tableName 手机号秘文所在表名
     * @return 明文手机号码
     */
    public String decryptMobileByTableName(String encryptedMobile, String tableName) {
        // 从 datatable_info 表获取主键id
        DataTableInfo tableDetail = dataTableManageService.getTableDetailWithTableName(tableName);
        if (tableDetail == null) {
            log.error("failed to decryptMobileByTableName");
            return null;
        }

        // 基于主键id从 field_encry_config 表查 加密字段 和 密钥
        Map<String, String> fieldEncryptInfo = tableRecordCommonService.getEncryptFields(tableDetail.getId());

        // 使用 加密字段 和 密钥 解密
        return MobileProcessUtils.decryptMobile(encryptedMobile, fieldEncryptInfo);
    }

}
