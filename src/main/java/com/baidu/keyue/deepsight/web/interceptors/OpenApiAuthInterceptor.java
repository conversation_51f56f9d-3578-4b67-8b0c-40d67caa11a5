package com.baidu.keyue.deepsight.web.interceptors;

import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.service.datamanage.impl.AccessTokenService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;


/**
 * <AUTHOR>
 * @className OpenApiAuthInterceptor
 * @description 对外接口鉴权
 * @date 2025/2/18 15:16
 */
@Component
@Slf4j
public class OpenApiAuthInterceptor implements HandlerInterceptor {

    private static final String AUTHORIZATION = "Authorization";
    public static final String TOKEN_VERSION_V1 = "dp-api-auth-v1/";

    @Autowired
    private AccessTokenService tokenService;


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        try {
            String authorization = request.getHeader(AUTHORIZATION);
            if (StringUtils.isEmpty(authorization)) {
                throw new DeepSightException.AuthTokenException(ErrorCode.FORBIDDEN_REQUEST, "无权限");
            }
            if (authorization.startsWith(TOKEN_VERSION_V1)) {
                if (!tokenService.validateAuthorization(request, authorization)) {
                    throw new DeepSightException.AuthTokenException(ErrorCode.FORBIDDEN_REQUEST, "无权限");
                }
            }

        } catch (DeepSightException.AuthTokenException e) {
            throw new DeepSightException.AuthTokenException(ErrorCode.FORBIDDEN_REQUEST, e.getMessage());
        } catch (Exception e) {
            log.error("authorization check msg:", e);
            return false;
        }

        return true;
    }


}
