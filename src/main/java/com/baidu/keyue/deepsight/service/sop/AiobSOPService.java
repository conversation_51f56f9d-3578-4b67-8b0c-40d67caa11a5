package com.baidu.keyue.deepsight.service.sop;

import java.util.HashMap;
import java.util.List;

import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.sop.SOPNodePredictRequest;
import com.baidu.keyue.deepsight.models.sop.SOPNodePredictResponse;
import com.baidu.keyue.deepsight.models.sop.SOPQuickNodeListResponse;
import com.baidu.keyue.deepsight.models.sop.SopBaseRequest;
import com.baidu.keyue.deepsight.models.sop.SopSankeyMetaResponse;
import com.baidu.keyue.deepsight.models.sop.SopUserConfigUpdateRequest;
import com.baidu.keyue.deepsight.models.sop.SopUserDetailRequest;
import com.baidu.keyue.deepsight.models.sop.SopWholeRobotVersionRequest;
import com.baidu.keyue.deepsight.models.sop.SopWholeRobotVersionResponse;
import com.baidu.keyue.deepsight.models.sop.aiob.AiobDiagramVersionRecordResp;
import com.baidu.keyue.deepsight.models.sop.aiob.AiobDiagramVersionRecordViewResp;
import com.baidu.keyue.deepsight.mysqldb.entity.SopUserConfig;

/**
 * *@Author: dongjiacheng01
 * *@Description: 外呼sop服务接口
 * *@Date: 10:29 2025/5/20
 */
public interface AiobSOPService {

    /**
     * 快捷场景 - 查询对话rule及所有对话节点
     */
    SOPQuickNodeListResponse listQuickSOPNodes(String tenantId, String taskId, String robotVer);

    /**
     * 获取任务的全部步骤 + 节点信息
     * @param tenantId 租户id
     * @param taskId 任务id
     * @return 步骤列表
     */
    SopSankeyMetaResponse listSOPMeta(String tenantId, String taskId, String robotVer);

    /**
     * 快捷场景 - 节点预测
     */
    SOPNodePredictResponse predictNode(String tenantId, SOPNodePredictRequest request);

    /**
     * 机器人版本列表获取
     */
    List<SopWholeRobotVersionResponse> listRobotVersion(SopWholeRobotVersionRequest request);

    /**
     * 灵活画布 - 获取画布流程
     */
    AiobDiagramVersionRecordViewResp getDiagramRecords(String agentId, String versionId);

    /**
     * 灵活画布 - 获取画布流程
     */
    AiobDiagramVersionRecordResp getDiagramVersions(String agentId, String versionId);

    /**
     * 快捷场景 - 节点确认
     */
    void confirmNode(String tenantId, String taskId, String rule, String markdown, String robotVer);

    /**
     * 更新分析设置
     * @param tenantId 租户id
     * @param request 请求
     */
    void updateUserConfig(String tenantId, SopUserConfigUpdateRequest request);


    /**
     * 获取sop用户配置 如果不存在则自动创建
     * @param tenantId 租户id
     * @param taskId 任务id
     * @param userId 用户id
     * @return sop用户配置
     */
    SopUserConfig getOrCreateSopUserConfig(String tenantId, String taskId, String userId);

    /**
     * 获取分析设置
     * @param tenantId 租户id
     * @param request 请求
     * @return 设置详情
     */
    SopUserConfigUpdateRequest getUserConfig(String tenantId, SopBaseRequest request);

    /**
     * 创建用户配置
     * @param tenantId
     * @param request
     * @return
     */
    SopUserConfig createUserConfig(String tenantId, SopUserConfigUpdateRequest request);

    /**
     * 用户明细
     */
    BasePageResponse.Page<HashMap<String, Object>> getUserDetail(String tenantId, SopUserDetailRequest request);

    BasePageResponse.Page<HashMap<String, Object>> getUserDetailV2(String tenantId, SopUserDetailRequest request);
}
