package com.baidu.keyue.deepsight.service.diffusion.impl;

import com.baidu.keyue.deepsight.config.BosConfig;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.database.service.DorisUtils;
import com.baidu.keyue.deepsight.enums.DiffusionFilterEnum;
import com.baidu.keyue.deepsight.enums.JudgeCriteriaEnum;
import com.baidu.keyue.deepsight.enums.OperationModeEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.models.diffusion.DiffusionCalculateContext;
import com.baidu.keyue.deepsight.models.diffusion.DiffusionGroupContext;
import com.baidu.keyue.deepsight.models.diffusion.DiffusionRequestItem;
import com.baidu.keyue.deepsight.models.diffusion.DiffusionResponseItem;
import com.baidu.keyue.deepsight.models.doris.TableDescribeDto;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.exception.ServiceException;
import com.baidu.keyue.deepsight.models.meg.MEGRequestPrepare;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTask;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTaskCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.mapper.CustomerDiffusionTaskMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendTaskSchedulerMapper;
import com.baidu.keyue.deepsight.service.customer.CustomerGroupService;
import com.baidu.keyue.deepsight.service.diffusion.GroupDiffusionService;
import com.baidu.keyue.deepsight.service.operation.OperationService;
import com.baidu.keyue.deepsight.service.tasks.TaskSchedulerService;
import com.baidu.keyue.deepsight.service.tool.BosUtils;
import com.baidu.keyue.deepsight.service.user.BaiduUserDataService;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class GroupDiffusionCalculateServiceImplTest {


    @Mock
    private OperationService operationService;

    @Mock
    private BosConfig.Bucket bucket;

    @Mock
    private BaiduUserDataService baiduUserDataService;

    @Mock
    private DorisService dorisService;

    @Mock
    private CustomerGroupService customerGroupService;

    @InjectMocks
    private GroupDiffusionCalculateServiceImpl service;

    @Mock
    private CustomerDiffusionTaskMapper diffusionTaskMapper;

    @Mock
    private TaskSchedulerService taskSchedulerService;

    @Mock
    private ExtendTaskSchedulerMapper schedulerMapper;

    @Mock
    private GroupDiffusionService groupDiffusionService;

    @Mock
    private BosUtils bosUtils;
    @Mock
    private BosConfig bosConfig;

    @Test
    void downFileExitsShouldReturnFalseForEmptyList() {
        List<String> objectKeys = List.of();

        boolean result = service.downFileExits(objectKeys);
        Assertions.assertFalse(result);
    }

    @Test
    void downFileExitsShouldReturnFalseForNullList() {
        boolean result = service.downFileExits(null);
        Assertions.assertFalse(result);
    }

    @Test
    void downFileExitsShouldReturnTrueWhenDoneFileExists() {
        List<String> objectKeys = List.of(
                "path/to/file1",
                "path/to/donefile",
                "path/to/file2"
        );

        boolean result = service.downFileExits(objectKeys);
        assertTrue(result);
    }

    @Test
    void downFileExitsShouldReturnFalseWhenDoneFileNotExists() {
        List<String> objectKeys = List.of(
                "path/to/file1",
                "path/to/otherfile",
                "path/to/file2"
        );

        boolean result = service.downFileExits(objectKeys);
        Assertions.assertFalse(result);
    }

    @Test
    void downFileExitsShouldReturnTrueWhenDoneFileAtLastPosition() {
        List<String> objectKeys = List.of(
                "path/to/file1",
                "path/to/file2",
                "path/to/donefile"
        );

        boolean result = service.downFileExits(objectKeys);
        assertTrue(result);
    }

    @Test
    void downFileExitsShouldReturnTrueWhenDoneFileAtFirstPosition() {
        List<String> objectKeys = List.of(
                "path/to/donefile",
                "path/to/file1",
                "path/to/file2"
        );

        boolean result = service.downFileExits(objectKeys);
        assertTrue(result);
    }

    @Test
    void failFileExitsShouldReturnTrueWhenContainsFailFile() {
        List<String> objectKeys = List.of(
                "path/to/failed",
                "path/to/otherfile"
        );

        boolean result = service.failFileExits(objectKeys);
        assertTrue(result);
    }

    @Test
    void failFileExitsShouldReturnFalseWhenNoFailFile() {
        List<String> objectKeys = List.of(
                "path/to/result",
                "path/to/donefile"
        );

        boolean result = service.failFileExits(objectKeys);
        Assertions.assertFalse(result);
    }

    @Test
    void failFileExitsShouldHandleMultipleLevelPaths() {
        List<String> objectKeys = List.of(
                "deep/path/to/failed",
                "another/path/to/other"
        );

        boolean result = service.failFileExits(objectKeys);
        assertTrue(result);
    }

    @Test
    void failFileExitsShouldReturnFalseForEmptyList() {
        List<String> objectKeys = List.of();

        boolean result = service.failFileExits(objectKeys);
        Assertions.assertFalse(result);
    }

    @Test
    void taskCheckShouldSetErrorWhenSeedGroupIdIsNull() {
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setPredictGroup("1,2,3");
        context.setDiffusionTask(task);

        service.taskCheck(context);

        assertNotNull(context.getErr());
        assertTrue(context.getErr() instanceof DeepSightException.GroupDiffusionGroupCheckFailedException);
    }

    @Test
    void testPullRunningTask_NoTasks() {
        when(diffusionTaskMapper.selectByExample(any(CustomerDiffusionTaskCriteria.class)))
                .thenReturn(Collections.emptyList());

        List<TaskSchedulerWithBLOBs> result = service.pullRunningTask();

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(diffusionTaskMapper, times(1)).selectByExample(any(CustomerDiffusionTaskCriteria.class));
        verify(taskSchedulerService, times(0)).queryRunningScheduler(anyList());
    }

    @Test
    void testPullRunningTask_WithTasks() {
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setTaskId(1L);
        task.setCalStatus(TaskExecStatusEnum.RUNNING.getCode());
        List<CustomerDiffusionTask> taskList = List.of(task);

        TaskSchedulerWithBLOBs schedulerTask = new TaskSchedulerWithBLOBs();
        schedulerTask.setTaskId(1L);
        List<TaskSchedulerWithBLOBs> schedulerTaskList = List.of(schedulerTask);

        when(diffusionTaskMapper.selectByExample(any(CustomerDiffusionTaskCriteria.class)))
                .thenReturn(taskList);
        when(taskSchedulerService.queryRunningScheduler(anyList()))
                .thenReturn(schedulerTaskList);

        List<TaskSchedulerWithBLOBs> result = service.pullRunningTask();

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1L, result.get(0).getTaskId());
        verify(diffusionTaskMapper, times(1)).selectByExample(any(CustomerDiffusionTaskCriteria.class));
        verify(taskSchedulerService, times(1)).queryRunningScheduler(anyList());
    }

    @Test
    void taskCheckShouldSetErrorWhenPredictGroupIdsIsEmpty() {
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setSeedGroup(1L);
        task.setPredictGroup("");
        context.setDiffusionTask(task);

        service.taskCheck(context);

        assertNotNull(context.getErr());
        assertTrue(context.getErr() instanceof DeepSightException.GroupDiffusionGroupCheckFailedException);
    }

    @Test
    void taskCheckShouldSetErrorWhenSeedGroupsIsEmpty() {
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setSeedGroup(1L);
        task.setPredictGroup("2,3");
        context.setDiffusionTask(task);
        context.setTenantId("tenant");

        when(customerGroupService.retrieveCustomerGroupWithIds(anyList(), anyString()))
                .thenReturn(Collections.emptyList());

        service.taskCheck(context);

        assertNotNull(context.getErr());
        assertTrue(context.getErr() instanceof DeepSightException.GroupDiffusionGroupCheckFailedException);
    }

    @Test
    void taskCheckShouldSetErrorWhenPredictGroupsIsEmpty() {
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setSeedGroup(1L);
        task.setPredictGroup("2,3");
        context.setDiffusionTask(task);
        context.setTenantId("tenant");

        CustomerGroup seedGroup = new CustomerGroup();
        seedGroup.setId(1L);
        when(customerGroupService.retrieveCustomerGroupWithIds(anyList(), anyString()))
                .thenReturn(List.of(seedGroup));
        when(customerGroupService.retrieveCustomerGroupWithIds(anyList(), anyString()))
                .thenReturn(Collections.emptyList());

        service.taskCheck(context);

        assertNotNull(context.getErr());
        assertTrue(context.getErr() instanceof DeepSightException.GroupDiffusionGroupCheckFailedException);
    }


    @Test
    void taskCheckShouldSetGroupContextsWhenAllChecksPass() {
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setSeedGroup(1L);
        task.setPredictGroup("2,3");
        context.setDiffusionTask(task);
        context.setTenantId("tenant");

        CustomerGroup seedGroup = new CustomerGroup();
        seedGroup.setId(1L);
        CustomerGroup predictGroup1 = new CustomerGroup();
        predictGroup1.setId(2L);
        CustomerGroup predictGroup2 = new CustomerGroup();
        predictGroup2.setId(3L);

        when(customerGroupService.retrieveCustomerGroupWithIds(anyList(), anyString()))
                .thenReturn(List.of(seedGroup));
        when(customerGroupService.retrieveCustomerGroupWithIds(anyList(), anyString()))
                .thenReturn(List.of(predictGroup1, predictGroup2));

        service.taskCheck(context);

        Assertions.assertNull(context.getErr());
        assertNotNull(context.getSeedGroup());
        assertEquals(1, context.getSeedGroup().getGroupIds().size());
        assertEquals(2L, context.getSeedGroup().getGroupIds().get(0));
        assertNotNull(context.getPredictGroup());
        assertEquals(2, context.getPredictGroup().getGroupIds().size());
        assertTrue(context.getPredictGroup().getGroupIds().containsAll(List.of(2L, 3L)));
    }

    @Test
    void migrateDiffusionDataToCustomerGroupShouldExecuteSqlSuccessfully() {
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<DorisUtils> dorisUtilsMock = mockStatic(DorisUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            DiffusionCalculateContext context = new DiffusionCalculateContext();
            context.setExecId(1L);
            context.setTable("source_table");

            String tenantId = "tenant-1";
            long customerGroupId = 123L;
            String mockUserTableName = "mock_user_table";
            String groupFieldName = "group_field";
            String expectedSql = "expected_sql";

            tenantUtilsMock.when(() -> TenantUtils.generateMockUserTableName(tenantId))
                    .thenReturn(mockUserTableName);
            dorisUtilsMock.when(() -> DorisUtils.generateCustomerGroupFieldName(customerGroupId))
                    .thenReturn(groupFieldName);
            ormUtilsMock.when(() -> ORMUtils.generateMigrateDiffusionDataToCustomerGroup(mockUserTableName, context.getTable(), groupFieldName))
                    .thenReturn(expectedSql);

            service.migrateDiffusionDataToCustomerGroup(context, tenantId, customerGroupId);

            verify(dorisService).execSql(expectedSql);
        }
    }

    @Test
    void migrateDiffusionDataToCustomerGroupShouldThrowExceptionWhenDorisServiceFails() {
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<DorisUtils> dorisUtilsMock = mockStatic(DorisUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            DiffusionCalculateContext context = new DiffusionCalculateContext();
            context.setExecId(1L);
            context.setTable("source_table");

            String tenantId = "tenant-1";
            long customerGroupId = 123L;
            String mockUserTableName = "mock_user_table";
            String groupFieldName = "group_field";
            String expectedSql = "expected_sql";

            tenantUtilsMock.when(() -> TenantUtils.generateMockUserTableName(tenantId))
                    .thenReturn(mockUserTableName);
            dorisUtilsMock.when(() -> DorisUtils.generateCustomerGroupFieldName(customerGroupId))
                    .thenReturn(groupFieldName);
            ormUtilsMock.when(() -> ORMUtils.generateMigrateDiffusionDataToCustomerGroup(mockUserTableName, context.getTable(), groupFieldName))
                    .thenReturn(expectedSql);

            doThrow(new RuntimeException("Doris error")).when(dorisService).execSql(expectedSql);

            ServiceException exception = assertThrows(ServiceException.class,
                    () -> service.migrateDiffusionDataToCustomerGroup(context, tenantId, customerGroupId));
            assertEquals("操作失败，客群打包失败", exception.getMessage());
        }
    }

    @Test
    void migrateDiffusionDataToCustomerGroupShouldThrowExceptionWhenFromTableIsBlank() {
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        context.setExecId(1L);
        context.setTable("");

        ServiceException exception = assertThrows(ServiceException.class,
                () -> service.migrateDiffusionDataToCustomerGroup(context, "tenant-1", 123L));
        assertEquals("操作失败，客群打包失败", exception.getMessage());
    }

    @Test
    void clearCustomerGroupDataShouldThrowExceptionWhenDorisFails() {
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<DorisUtils> dorisUtilsMock = mockStatic(DorisUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            // Arrange
            DiffusionCalculateContext context = mock(DiffusionCalculateContext.class);
            String tenantId = "testTenant";
            Long customerGroupId = 123L;
            String mockTableName = "mock_table";
            String groupFieldName = "group_field";
            String expectedSql = "CLEAR SQL";
            Exception expectedException = new RuntimeException("Doris error");

            when(context.getExecId()).thenReturn(1L);
            tenantUtilsMock.when(() -> TenantUtils.generateMockUserTableName(tenantId))
                    .thenReturn(mockTableName);
            dorisUtilsMock.when(() -> DorisUtils.generateCustomerGroupFieldName(customerGroupId))
                    .thenReturn(groupFieldName);
            ormUtilsMock.when(() -> ORMUtils.generateClearCustomerGroupDataSql(mockTableName, groupFieldName))
                    .thenReturn(expectedSql);
            doThrow(expectedException).when(dorisService).execSql(expectedSql);

            // Act & Assert
            ServiceException exception = assertThrows(ServiceException.class,
                    () -> service.clearCustomerGroupData(context, tenantId, customerGroupId));
            assertEquals("操作失败，客群初始化失败", exception.getMessage());
        }
    }

    @Test
    void clearCustomerGroupDataShouldExecuteSqlSuccessfully() {
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<DorisUtils> dorisUtilsMock = mockStatic(DorisUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            // Arrange
            DiffusionCalculateContext context = mock(DiffusionCalculateContext.class);
            String tenantId = "testTenant";
            long customerGroupId = 123L;
            long execId = 1L;
            String mockTableName = "mock_table";
            String groupFieldName = "group_field";
            String expectedSql = "CLEAR SQL";

            tenantUtilsMock.when(() -> TenantUtils.generateMockUserTableName(tenantId))
                    .thenReturn(mockTableName);
            dorisUtilsMock.when(() -> DorisUtils.generateCustomerGroupFieldName(customerGroupId))
                    .thenReturn(groupFieldName);
            ormUtilsMock.when(() -> ORMUtils.generateClearCustomerGroupDataSql(mockTableName, groupFieldName))
                    .thenReturn(expectedSql);

            // Act & Assert
            assertDoesNotThrow(() -> service.clearCustomerGroupData(context, tenantId, customerGroupId));
            verify(dorisService).execSql(expectedSql);
        }
    }

    @Test
    public void testJsonContext() {
        String s = "{\"execId\":2038207,\"table\":\"process_diffusion_temporary_t_118\",\"diffusionTask\":{\"id\":118,\"taskName\":\"预测任务01\",\"seedGroup\":10057,\"predictGroup\":\"10019\",\"filterRule\":0,\"featureSelect\":1,\"threshold\":1.0,\"judgeCriteria\":0,\"similarity\":70.0,\"ranking\":0,\"triggerMod\":1,\"triggerFrequency\":0,\"triggerFrequencyValue\":\"{\\\"hour\\\":1}\",\"calStatus\":3,\"lastCalDate\":null,\"taskId\":164,\"tenantId\":\"36957887057482752\",\"groupPackage\":false,\"del\":false,\"creator\":\"5599182807777282\",\"modifier\":\"5599182807777282\",\"createTime\":1743504892000,\"updateTime\":1743504892000,\"customerGroupId\":0},\"tenantId\":\"36957887057482752\",\"threshold\":1.0,\"seedGroup\":{\"groupIds\":[10057],\"querySql\":\"SELECT * FROM (\\n SELECT oneId,\\n ( CASE WHEN `merge_gender` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_age_group` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_life_stage` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_marriage_status` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_industry` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_education_level` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_occupation` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_consume_level` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_consume_intent` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_geographic_location` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_interests` != '' THEN 1 ELSE 0 END + CASE WHEN array_size(`mobile_list`) > 0 THEN 1 ELSE 0 END + CASE WHEN array_size(`email_list`) > 0 THEN 1 ELSE 0 END + CASE WHEN `IMEI` != '' THEN 1 ELSE 0 END + CASE WHEN `BAIDUID` != '' THEN 1 ELSE 0 END + CASE WHEN `CUID` != '' THEN 1 ELSE 0 END + CASE WHEN `USERID` != '' THEN 1 ELSE 0 END + CASE WHEN `MAC` != '' THEN 1 ELSE 0 END + CASE WHEN `UNIONID` != '' THEN 1 ELSE 0 END + CASE WHEN `IDFA` != '' THEN 1 ELSE 0 END + CASE WHEN `OAID` != '' THEN 1 ELSE 0 END + CASE WHEN `anonymous_id` != '' THEN 1 ELSE 0 END ) AS non_null_count\\n FROM mock_user_36957887057482752\\n WHERE `process_customer_10057` = '1'\\n order by deepsight_datetime\\n) AS subquery\\nWHERE subquery.non_null_count >= 22\",\"countSql\":\"SELECT COUNT(*) FROM (\\n SELECT oneId,\\n ( CASE WHEN `merge_gender` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_age_group` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_life_stage` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_marriage_status` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_industry` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_education_level` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_occupation` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_consume_level` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_consume_intent` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_geographic_location` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_interests` != '' THEN 1 ELSE 0 END + CASE WHEN array_size(`mobile_list`) > 0 THEN 1 ELSE 0 END + CASE WHEN array_size(`email_list`) > 0 THEN 1 ELSE 0 END + CASE WHEN `IMEI` != '' THEN 1 ELSE 0 END + CASE WHEN `BAIDUID` != '' THEN 1 ELSE 0 END + CASE WHEN `CUID` != '' THEN 1 ELSE 0 END + CASE WHEN `USERID` != '' THEN 1 ELSE 0 END + CASE WHEN `MAC` != '' THEN 1 ELSE 0 END + CASE WHEN `UNIONID` != '' THEN 1 ELSE 0 END + CASE WHEN `IDFA` != '' THEN 1 ELSE 0 END + CASE WHEN `OAID` != '' THEN 1 ELSE 0 END + CASE WHEN `anonymous_id` != '' THEN 1 ELSE 0 END ) AS non_null_count\\n FROM mock_user_36957887057482752\\n WHERE `process_customer_10057` = '1'\\n order by deepsight_datetime\\n) AS subquery\\nWHERE subquery.non_null_count >= 22\",\"count\":0,\"tempFilename\":\"seed-2038207\",\"objectKey\":\"customer_diffusion/dev-2038207/input/seed\"},\"predictGroup\":{\"groupIds\":[10019],\"querySql\":\"SELECT * FROM (\\n SELECT oneId,\\n ( CASE WHEN `merge_gender` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_age_group` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_life_stage` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_marriage_status` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_industry` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_education_level` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_occupation` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_consume_level` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_consume_intent` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_geographic_location` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_interests` != '' THEN 1 ELSE 0 END + CASE WHEN array_size(`mobile_list`) > 0 THEN 1 ELSE 0 END + CASE WHEN array_size(`email_list`) > 0 THEN 1 ELSE 0 END + CASE WHEN `IMEI` != '' THEN 1 ELSE 0 END + CASE WHEN `BAIDUID` != '' THEN 1 ELSE 0 END + CASE WHEN `CUID` != '' THEN 1 ELSE 0 END + CASE WHEN `USERID` != '' THEN 1 ELSE 0 END + CASE WHEN `MAC` != '' THEN 1 ELSE 0 END + CASE WHEN `UNIONID` != '' THEN 1 ELSE 0 END + CASE WHEN `IDFA` != '' THEN 1 ELSE 0 END + CASE WHEN `OAID` != '' THEN 1 ELSE 0 END + CASE WHEN `anonymous_id` != '' THEN 1 ELSE 0 END ) AS non_null_count\\n FROM mock_user_36957887057482752\\n WHERE `process_customer_10019` = '1'\\n order by deepsight_datetime\\n) AS subquery\\nWHERE subquery.non_null_count >= 22\",\"countSql\":\"SELECT COUNT(*) FROM (\\n SELECT oneId,\\n ( CASE WHEN `merge_gender` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_age_group` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_life_stage` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_marriage_status` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_industry` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_education_level` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_occupation` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_consume_level` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_consume_intent` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_geographic_location` != '' THEN 1 ELSE 0 END + CASE WHEN `merge_interests` != '' THEN 1 ELSE 0 END + CASE WHEN array_size(`mobile_list`) > 0 THEN 1 ELSE 0 END + CASE WHEN array_size(`email_list`) > 0 THEN 1 ELSE 0 END + CASE WHEN `IMEI` != '' THEN 1 ELSE 0 END + CASE WHEN `BAIDUID` != '' THEN 1 ELSE 0 END + CASE WHEN `CUID` != '' THEN 1 ELSE 0 END + CASE WHEN `USERID` != '' THEN 1 ELSE 0 END + CASE WHEN `MAC` != '' THEN 1 ELSE 0 END + CASE WHEN `UNIONID` != '' THEN 1 ELSE 0 END + CASE WHEN `IDFA` != '' THEN 1 ELSE 0 END + CASE WHEN `OAID` != '' THEN 1 ELSE 0 END + CASE WHEN `anonymous_id` != '' THEN 1 ELSE 0 END ) AS non_null_count\\n FROM mock_user_36957887057482752\\n WHERE `process_customer_10019` = '1'\\n order by deepsight_datetime\\n) AS subquery\\nWHERE subquery.non_null_count >= 22\",\"count\":0,\"tempFilename\":\"predict-2038207\",\"objectKey\":\"customer_diffusion/dev-2038207/input/predict\"},\"outputDirPrefix\":\"customer_diffusion/dev-2038207/output/\"}";
        DiffusionCalculateContext context = JsonUtils.toObjectWithoutException(s, DiffusionCalculateContext.class);
        assertNotNull(context);
        assertEquals("process_diffusion_temporary_t_118", context.getTable());
    }

    @Test
    void callBaiduMegInShouldReturnItemWhenSuccess() {
        MEGRequestPrepare request = new MEGRequestPrepare();
        DiffusionRequestItem expectedItem = new DiffusionRequestItem();

        when(baiduUserDataService.getUserAttribute(request)).thenReturn(expectedItem);

        Mono<DiffusionRequestItem> result = service.callBaiduMegIn(request);

        StepVerifier.create(result)
                .expectNext(expectedItem)
                .verifyComplete();
    }

    @Test
    void callBaiduMegInShouldReturnEmptyWhenNullResponse() {
        MEGRequestPrepare request = new MEGRequestPrepare();

        when(baiduUserDataService.getUserAttribute(request)).thenReturn(null);

        Mono<DiffusionRequestItem> result = service.callBaiduMegIn(request);

        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void callBaiduMegInShouldReturnEmptyWhenExceptionOccurs() {
        MEGRequestPrepare request = new MEGRequestPrepare();

        when(baiduUserDataService.getUserAttribute(request)).thenThrow(new RuntimeException("Test exception"));

        Mono<DiffusionRequestItem> result = service.callBaiduMegIn(request);

        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void dataBatchAppendShouldHandleEmptyBatch() {
        List<DiffusionRequestItem> emptyBatch = List.of();
        DiffusionGroupContext groupContext = new DiffusionGroupContext();
        groupContext.setTempFilename("test_temp_file.txt");

        Mono<Void> result = service.dataBatchAppend(emptyBatch, groupContext);

        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void dataBatchAppendShouldHandleNullItemsInBatch() {
        DiffusionRequestItem validItem = mock(DiffusionRequestItem.class);
        List<DiffusionRequestItem> batch = List.of(validItem);
        DiffusionGroupContext groupContext = new DiffusionGroupContext();
        groupContext.setTempFilename("test_temp_file.txt");

        when(JsonUtils.toJsonWithOutException(validItem)).thenReturn("{\"valid\":\"data\"}");

        Mono<Void> result = service.dataBatchAppend(batch, groupContext);

        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void dataBatchAppendShouldWriteToFile() throws IOException {
        DiffusionRequestItem item1 = mock(DiffusionRequestItem.class);
        DiffusionRequestItem item2 = mock(DiffusionRequestItem.class);
        List<DiffusionRequestItem> batch = List.of(item1, item2);
        DiffusionGroupContext groupContext = new DiffusionGroupContext();
        String tempFilename = "test_temp_file.txt";
        groupContext.setTempFilename(tempFilename);

        Path mockPath = mock(Path.class);
        try (MockedStatic<Paths> pathsMock = mockStatic(Paths.class);
             MockedStatic<Files> filesMock = mockStatic(Files.class)) {
            pathsMock.when(() -> Paths.get(tempFilename)).thenReturn(mockPath);

            when(JsonUtils.toJsonWithOutException(item1)).thenReturn("{\"item1\":\"data\"}");
            when(JsonUtils.toJsonWithOutException(item2)).thenReturn("{\"item2\":\"data\"}");

            Mono<Void> result = service.dataBatchAppend(batch, groupContext);

            StepVerifier.create(result)
                    .verifyComplete();

            String expectedContent = "{\"item1\":\"data\"}\n{\"item2\":\"data\"}\n";
            filesMock.when(() -> Files.writeString(
                    mockPath,
                    expectedContent,
                    StandardOpenOption.CREATE,
                    StandardOpenOption.APPEND
            )).thenReturn(null);
        }
    }

    @Test
    void dataBatchAppendShouldHandleIOException() throws IOException {
        DiffusionRequestItem item = mock(DiffusionRequestItem.class);
        List<DiffusionRequestItem> batch = List.of(item);
        DiffusionGroupContext groupContext = new DiffusionGroupContext();
        String tempFilename = "test_temp_file.txt";
        groupContext.setTempFilename(tempFilename);

        Path mockPath = mock(Path.class);
        try (MockedStatic<Paths> pathsMock = mockStatic(Paths.class);
             MockedStatic<Files> filesMock = mockStatic(Files.class)) {
            pathsMock.when(() -> Paths.get(tempFilename)).thenReturn(mockPath);

            when(JsonUtils.toJsonWithOutException(item)).thenReturn("{\"item\":\"data\"}");
            filesMock.when(() -> Files.writeString(
                    any(Path.class),
                    anyString(),
                    any(StandardOpenOption.class),
                    any(StandardOpenOption.class)
            )).thenThrow(new IOException("Test exception"));

            Mono<Void> result = service.dataBatchAppend(batch, groupContext);

            StepVerifier.create(result)
                    .verifyComplete();
        }
    }

    @AfterAll
    public static void tearDown() throws IOException {
        Files.deleteIfExists(Path.of("test_temp_file.txt"));
    }

    @Test
    void testPullRunningTaskWithTasks() {
        CustomerDiffusionTask task1 = new CustomerDiffusionTask();
        task1.setTaskId(1L);
        CustomerDiffusionTask task2 = new CustomerDiffusionTask();
        task2.setTaskId(2L);
        List<CustomerDiffusionTask> mockTasks = Arrays.asList(task1, task2);

        TaskSchedulerWithBLOBs scheduler1 = new TaskSchedulerWithBLOBs();
        TaskSchedulerWithBLOBs scheduler2 = new TaskSchedulerWithBLOBs();
        List<TaskSchedulerWithBLOBs> mockSchedulers = Arrays.asList(scheduler1, scheduler2);

        when(diffusionTaskMapper.selectByExample(any(CustomerDiffusionTaskCriteria.class)))
                .thenReturn(mockTasks);
        when(taskSchedulerService.queryRunningScheduler(anyList()))
                .thenReturn(mockSchedulers);

        List<TaskSchedulerWithBLOBs> result = service.pullRunningTask();

        assertNotNull(result);
        assertEquals(2, result.size());
        verify(diffusionTaskMapper, times(1)).selectByExample(any(CustomerDiffusionTaskCriteria.class));
        verify(taskSchedulerService, times(1)).queryRunningScheduler(anyList());
    }

    @Test
    void testPullRunningTaskNoTasks() {
        when(diffusionTaskMapper.selectByExample(any(CustomerDiffusionTaskCriteria.class)))
                .thenReturn(Collections.emptyList());
        List<TaskSchedulerWithBLOBs> result = service.pullRunningTask();

        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    void uploadTempFileToOSSWhenFileNotExistsShouldSetError() throws Exception {
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        DiffusionGroupContext group = new DiffusionGroupContext();
        group.setTempFilename("non_existent_file.txt");
        group.setObjectKey("test_key");

        service.uploadTempFileToOSS(context, group);

        Assertions.assertTrue(context.getErr() instanceof DeepSightException.GroupDiffusionDataNullFailedException);
        verifyNoInteractions(bosUtils);
    }

    @Test
    void uploadTempFileToOSSWhenFileExistsShouldUploadAndDelete() throws Exception {
        String tempFile = "test_temp_fi1le.txt";
        Files.createFile(Path.of(tempFile));

        DiffusionCalculateContext context = new DiffusionCalculateContext();
        DiffusionGroupContext group = new DiffusionGroupContext();
        group.setTempFilename(tempFile);
        group.setObjectKey("test_key");

        when(bosConfig.getBucket()).thenReturn(bucket);
        when(bucket.getDiffusion()).thenReturn("test_bucket");
        when(bosUtils.putObject(any(), any(), any())).thenReturn(null);

        service.uploadTempFileToOSS(context, group);

        verify(bosUtils).putObject(eq("test_bucket"), eq("test_key"), any(FileInputStream.class));
        Assertions.assertFalse(Files.exists(Path.of(tempFile)));
        Assertions.assertNull(context.getErr());
    }

    @Test
    void filterSeedGroup_NoFilterRule_ReturnsOriginalBatch() {
        // Arrange
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setFilterRule(DiffusionFilterEnum.NOT_FILTER.getCode());
        context.setDiffusionTask(task);

        List<DiffusionResponseItem> batch = List.of(
                new DiffusionResponseItem("asda", 0.8f),
                new DiffusionResponseItem("asdfasf", 0.9f)
        );

        // Act
        List<DiffusionResponseItem> result = service.filterSeedGroup(context, batch);

        // Assert
        Assertions.assertEquals(batch, result);
    }

    @Test
    void filterSeedGroup_EmptySeedGroup_ReturnsOriginalBatch() {
        // Arrange
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setFilterRule(DiffusionFilterEnum.REMOVE_SEED_GROUP.getCode());
        context.setDiffusionTask(task);
        context.setSeedGroup(new DiffusionGroupContext());

        List<DiffusionResponseItem> batch = List.of(
                new DiffusionResponseItem("asda", 0.8f),
                new DiffusionResponseItem("asdfasf", 0.9f)
        );

        // Act
        List<DiffusionResponseItem> result = service.filterSeedGroup(context, batch);

        // Assert
        Assertions.assertEquals(batch, result);
    }

    @Test
    void filterSeedGroup_EmptyOneIds_ReturnsOriginalBatch() {
        // Arrange
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setFilterRule(DiffusionFilterEnum.REMOVE_SEED_GROUP.getCode());
        context.setDiffusionTask(task);

        DiffusionGroupContext seedGroup = new DiffusionGroupContext();
        seedGroup.setGroupIds(List.of(1L));
        context.setSeedGroup(seedGroup);

        List<DiffusionResponseItem> batch = List.of(
                new DiffusionResponseItem("asda", 0.8f),
                new DiffusionResponseItem("asdfasf", 0.9f)
        );

        // Act
        List<DiffusionResponseItem> result = service.filterSeedGroup(context, batch);

        // Assert
        Assertions.assertEquals(0, result.size());
    }

    @Test
    void filterSeedGroup_ValidInput_FiltersCorrectly() {
        // Arrange
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setFilterRule(DiffusionFilterEnum.REMOVE_SEED_GROUP.getCode());
        context.setDiffusionTask(task);
        context.setTenantId("tenant1");

        DiffusionGroupContext seedGroup = new DiffusionGroupContext();
        seedGroup.setGroupIds(List.of(2L));
        context.setSeedGroup(seedGroup);

        List<DiffusionResponseItem> batch = List.of(
                new DiffusionResponseItem("id1", 0.8f),
                new DiffusionResponseItem("id3", 0.9f)
        );

        List<Map<String, Object>> dbResult = List.of(
                Map.of("oneId", "id1"),
                Map.of("oneId", "id3")
        );
        when(dorisService.selectList(anyString())).thenReturn(dbResult);

        // Act
        List<DiffusionResponseItem> result = service.filterSeedGroup(context, batch);

        // Assert
        Assertions.assertEquals(2, result.size());
        Assertions.assertTrue(result.stream().anyMatch(item -> "id1".equals(item.getOneId())));
        Assertions.assertTrue(result.stream().anyMatch(item -> "id3".equals(item.getOneId())));
        Assertions.assertFalse(result.stream().anyMatch(item -> "id2".equals(item.getOneId())));

        verify(dorisService).selectList(anyString());
    }

    @Test
    void filterSeedGroup_DorisException_ReturnsOriginalBatch() {
        // Arrange
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setFilterRule(DiffusionFilterEnum.REMOVE_SEED_GROUP.getCode());
        context.setDiffusionTask(task);
        context.setTenantId("tenant1");

        DiffusionGroupContext seedGroup = new DiffusionGroupContext();
        seedGroup.setGroupIds(List.of(2L));
        context.setSeedGroup(seedGroup);

        List<DiffusionResponseItem> batch = List.of(
                new DiffusionResponseItem("asda", 0.8f),
                new DiffusionResponseItem("asdfasf", 0.9f)
        );

        when(dorisService.selectList(anyString())).thenThrow(new RuntimeException("Mock DB error"));

        // Act
        List<DiffusionResponseItem> result = service.filterSeedGroup(context, batch);

        // Assert
        Assertions.assertEquals(batch, result);
        verify(dorisService).selectList(anyString());
    }

    @Test
    void queryBasicInfoWithOneIds_shouldReturnCorrectItems() {
        // Arrange
        String mockUserTableName = "mock_user_table";
        List<String> oneIds = List.of("id1", "id2");


        // Mock dorisService.selectList
        Map<String, Object> record1 = Map.of(
                Constants.TABLE_USER_ONE_ID, "id1",
                "age_group", "1",
                "gender", "1"
        );
        Map<String, Object> record2 = Map.of(
                Constants.TABLE_USER_ONE_ID, "id2",
                "age_group", "1",
                "gender", "1"
        );
        List<Map<String, Object>> mockRecords = List.of(record1, record2);
        when(dorisService.selectList(anyString())).thenReturn(mockRecords);

        Mono<List<DiffusionRequestItem>> resultMono = service.queryBasicInfoWithOneIds(mockUserTableName, oneIds);
        List<DiffusionRequestItem> result = resultMono.block();

        // Assert
        Assertions.assertEquals(2, result.size());

        // Verify first item
        DiffusionRequestItem item1 = result.get(0);
        Assertions.assertEquals("id1", item1.getOneId());
        Assertions.assertEquals(2, item1.getAttribute().size());

        // Verify second item
        DiffusionRequestItem item2 = result.get(1);
        Assertions.assertEquals("id2", item2.getOneId());
        Assertions.assertEquals(2, item2.getAttribute().size());
    }

    @Test
    void queryBasicInfoWithOneIds_shouldHandleEmptyResult() {
        // Arrange
        String mockUserTableName = "mock_user_table";
        List<String> oneIds = List.of("id1", "id2");

        try (var mockedORMUtils = mockStatic(ORMUtils.class)) {
            mockedORMUtils.when(() -> ORMUtils.generateQueryBasicInfoWithOneIds(mockUserTableName, oneIds))
                    .thenReturn("SELECT * FROM mock_user_table WHERE one_id IN ('id1','id2')");

            when(dorisService.selectList(anyString())).thenReturn(List.of());

            // Act
            Mono<List<DiffusionRequestItem>> resultMono = service.queryBasicInfoWithOneIds(mockUserTableName, oneIds);
            List<DiffusionRequestItem> result = resultMono.block();

            // Assert
            Assertions.assertTrue(result.isEmpty());
        }
    }

    @Test
    void queryBasicInfoWithOneIds_shouldHandleDatabaseError() {
        // Arrange
        String mockUserTableName = "mock_user_table";
        List<String> oneIds = List.of("id1", "id2");

        try (var mockedORMUtils = mockStatic(ORMUtils.class)) {
            mockedORMUtils.when(() -> ORMUtils.generateQueryBasicInfoWithOneIds(mockUserTableName, oneIds))
                    .thenReturn("SELECT * FROM mock_user_table WHERE one_id IN ('id1','id2')");

            when(dorisService.selectList(anyString())).thenThrow(new RuntimeException("DB error"));

            // Act
            Mono<List<DiffusionRequestItem>> resultMono = service.queryBasicInfoWithOneIds(mockUserTableName, oneIds);
            List<DiffusionRequestItem> result = resultMono.block();

            // Assert
            Assertions.assertTrue(result.isEmpty());
        }
    }

    @Test
    void processDataWhenSelfOperation_ShouldProcessBatchSuccessfully() {
        // Prepare test data
        Map<String, Object> item1 = Map.of("oneId", "id1");
        Map<String, Object> item2 = Map.of("oneId", "id2");
        List<Map<String, Object>> batch = List.of(item1, item2);

        DiffusionGroupContext groupContext = new DiffusionGroupContext();
        groupContext.setTempFilename("test_temp_file.txt");

        GroupDiffusionCalculateServiceImpl service = Mockito.mock(GroupDiffusionCalculateServiceImpl.class);


        DiffusionRequestItem requestItem1 = new DiffusionRequestItem();
        requestItem1.setOneId("id1");
        DiffusionRequestItem requestItem2 = new DiffusionRequestItem();
        requestItem2.setOneId("id2");
        List<DiffusionRequestItem> queryResult = List.of(requestItem1, requestItem2);
        // Mock dataBatchAppend to do nothing

        // Execute test
        Mono<Void> result = service.processDataWhenSelfOperation(batch, groupContext, "mock_user_table");

        // Verify behavior
        StepVerifier.create(result)
                .expectError();
    }

    @Test
    void processDataWhenSelfOperation_ShouldHandleEmptyBatch() {
        // Prepare empty batch
        List<Map<String, Object>> batch = List.of();
        DiffusionGroupContext groupContext = new DiffusionGroupContext();
        groupContext.setTempFilename("test_temp_file.txt");
        String mockUserTableName = "mock_user_table";

        GroupDiffusionCalculateServiceImpl service = Mockito.mock(GroupDiffusionCalculateServiceImpl.class);

        // Execute test
        Mono<Void> result = service.processDataWhenSelfOperation(batch, groupContext, mockUserTableName);

        // Verify behavior
        StepVerifier.create(result)
                .expectError();

        verify(service, never()).parserOneId(anyMap());
        verify(service, never()).queryBasicInfoWithOneIds(any(), any());
        verify(service, never()).dataBatchAppend(any(), any());
    }

    @Test
    void processDataWhenSelfOperation_ShouldHandleParserError() {
        // Prepare test data
        Map<String, Object> item = Map.of("oneId", "id1");
        List<Map<String, Object>> batch = List.of(item);

        DiffusionGroupContext groupContext = new DiffusionGroupContext();
        String mockUserTableName = "mock_user_table";

        GroupDiffusionCalculateServiceImpl service = Mockito.mock(GroupDiffusionCalculateServiceImpl.class);

        // Execute test
        Mono<Void> result = service.processDataWhenSelfOperation(batch, groupContext, mockUserTableName);

        // Verify behavior
        StepVerifier.create(result)
                .expectError(RuntimeException.class);

        verify(service, never()).queryBasicInfoWithOneIds(any(), any());
        verify(service, never()).dataBatchAppend(any(), any());
    }

    @Test
    void fetchGroupDataWhenSelfOperation_ShouldHandleZeroCountScenario() {
        // Arrange
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        context.setThreshold(0F);
        List<Long> groupIds = Lists.newArrayList(1L, 2L);
        DiffusionGroupContext group = new DiffusionGroupContext();
        group.setGroupIds(groupIds);
        String mockUserTableName = "mock_table";
        List<TableDescribeDto> tableSchemas = List.of();
        String filename = "test_file";

        Pair<String, String> mockSqlPair = Pair.of("count_sql", "query_sql");
        when(dorisService.getCount(anyString())).thenReturn(0L);

        // Act
        service.fetchGroupDataWhenSelfOperation(context, mockUserTableName, tableSchemas, group, filename);

        // Assert
        verify(dorisService, never()).queryDorisStreaming(anyString());
        assertEquals(0L, group.getCount());
    }

    @Test
    void fetchGroupDataWhenSelfOperation_ShouldProcessDataWhenCountPositive() {
        // Arrange
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        context.setThreshold(0F);
        DiffusionGroupContext group = new DiffusionGroupContext(List.of(1L, 2L));
        group.setCount(10L);
        String mockUserTableName = "mock_table";
        List<TableDescribeDto> tableSchemas = List.of();
        String filename = "test_file";

        Flux<Map<String, Object>> mockFlux = Flux.just(Map.of("key", "value"));

        GroupDiffusionCalculateServiceImpl service = Mockito.mock(GroupDiffusionCalculateServiceImpl.class);

        service.fetchGroupDataWhenSelfOperation(context, mockUserTableName, tableSchemas, group, filename);

        assertEquals(10L, group.getCount());
    }

    @Test
    void fetchGroupDataWhenSelfOperation_ShouldHandleErrorInStreamProcessing() {
        // Arrange
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        context.setThreshold(0F);
        DiffusionGroupContext group = new DiffusionGroupContext(List.of(1L, 2L));
        group.setCount(0L);
        String mockUserTableName = "mock_table";
        List<TableDescribeDto> tableSchemas = List.of();
        String filename = "test_file";
        GroupDiffusionCalculateServiceImpl service = Mockito.mock(GroupDiffusionCalculateServiceImpl.class);

        service.fetchGroupDataWhenSelfOperation(context, mockUserTableName, tableSchemas, group, filename);

        assertEquals(0L, group.getCount());
    }

    @Test
    void execGroupDiffusionShouldHandleEmptyDiffusionTask() {
        // Arrange
        long diffusionId = 1L;
        String userId = "testUser";
        when(groupDiffusionService.getCustomerDiffusionTaskWithId(diffusionId)).thenReturn(List.of());

        // Act
        service.execGroupDiffusion(diffusionId, userId);

        // Assert
        verify(groupDiffusionService).getCustomerDiffusionTaskWithId(diffusionId);
        verifyNoMoreInteractions(
                taskSchedulerService,
                customerGroupService,
                dorisService,
                operationService,
                bosUtils
        );
    }

    @Test
    void execGroupDiffusionShouldHandleBaiduOperationMode() {
        // Arrange
        long diffusionId = 1L;
        String userId = "testUser";
        String tenantId = "testTenant";
        String mockUserTableName = "mock_user_test";

        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setId(diffusionId);
        task.setTenantId(tenantId);
        task.setThreshold(0.5f);
        task.setTaskId(1L);
        task.setSeedGroup(1L);
        task.setPredictGroup("2,3");

        DiffusionCalculateContext context = new DiffusionCalculateContext();
        context.setDiffusionTask(task);
        context.setTenantId(tenantId);
        context.setThreshold(task.getThreshold());
        context.setExecId(1L);
        context.setOperationMode(OperationModeEnum.OPERATION_BY_BAIDU_OP);

        CustomerGroup seedGroup = new CustomerGroup();
        seedGroup.setId(1L);
        CustomerGroup predictGroup1 = new CustomerGroup();
        predictGroup1.setId(2L);
        CustomerGroup predictGroup2 = new CustomerGroup();
        predictGroup2.setId(3L);

        List<TableDescribeDto> tableSchemas = List.of(new TableDescribeDto());

        when(groupDiffusionService.getCustomerDiffusionTaskWithId(diffusionId)).thenReturn(List.of(task));
        when(operationService.detectTenantOperationMode(tenantId)).thenReturn(OperationModeEnum.OPERATION_BY_BAIDU_OP);
        when(dorisService.describeTableSchema(anyString())).thenReturn(tableSchemas);
        when(customerGroupService.retrieveCustomerGroupWithIds(List.of(1L), tenantId)).thenReturn(List.of(seedGroup));
        when(customerGroupService.retrieveCustomerGroupWithIds(List.of(2L, 3L), tenantId)).thenReturn(List.of(predictGroup1, predictGroup2));

        // Act
        service.execGroupDiffusion(diffusionId, userId);

        // Assert
        verify(groupDiffusionService).getCustomerDiffusionTaskWithId(diffusionId);
        verify(operationService).detectTenantOperationMode(tenantId);
        verify(customerGroupService, times(2)).retrieveCustomerGroupWithIds(any(), eq(tenantId));
    }

    @Test
    void execGroupDiffusionShouldHandleSelfOperationMode() {
        // Arrange
        long diffusionId = 1L;
        String userId = "testUser";
        String tenantId = "testTenant";
        String mockUserTableName = "mock_user_test";

        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setId(diffusionId);
        task.setTenantId(tenantId);
        task.setThreshold(0.5f);
        task.setTaskId(1L);
        task.setSeedGroup(1L);
        task.setPredictGroup("2,3");

        DiffusionCalculateContext context = new DiffusionCalculateContext();
        context.setDiffusionTask(task);
        context.setTenantId(tenantId);
        context.setThreshold(task.getThreshold());
        context.setExecId(1L);
        context.setOperationMode(OperationModeEnum.OPERATION_BY_SELF);

        CustomerGroup seedGroup = new CustomerGroup();
        seedGroup.setId(1L);
        CustomerGroup predictGroup1 = new CustomerGroup();
        predictGroup1.setId(2L);
        CustomerGroup predictGroup2 = new CustomerGroup();
        predictGroup2.setId(3L);

        List<TableDescribeDto> tableSchemas = List.of(new TableDescribeDto());

        when(groupDiffusionService.getCustomerDiffusionTaskWithId(diffusionId))
                .thenReturn(List.of(task));
        when(operationService.detectTenantOperationMode(tenantId))
                .thenReturn(OperationModeEnum.OPERATION_BY_SELF);
        when(dorisService.describeTableSchema(anyString()))
                .thenReturn(tableSchemas);
        when(customerGroupService.retrieveCustomerGroupWithIds(List.of(1L), tenantId))
                .thenReturn(List.of(seedGroup));
        when(customerGroupService.retrieveCustomerGroupWithIds(List.of(2L, 3L), tenantId))
                .thenReturn(List.of(predictGroup1, predictGroup2));

        // Act
        service.execGroupDiffusion(diffusionId, userId);

        // Assert
        verify(groupDiffusionService).getCustomerDiffusionTaskWithId(diffusionId);
        verify(operationService).detectTenantOperationMode(tenantId);
        verify(customerGroupService, times(2)).retrieveCustomerGroupWithIds(any(), eq(tenantId));
    }

    @Test
    void execGroupDiffusionShouldHandleTaskCheckFailure() {
        // Arrange
        long diffusionId = 1L;
        String userId = "testUser";
        String tenantId = "testTenant";

        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setId(diffusionId);
        task.setTenantId(tenantId);
        task.setTaskId(1L);
        task.setSeedGroup(null); // Will cause check failure
        task.setPredictGroup("");

        when(groupDiffusionService.getCustomerDiffusionTaskWithId(diffusionId))
                .thenReturn(List.of(task));

        // Act
        service.execGroupDiffusion(diffusionId, userId);

        // Assert
        verify(groupDiffusionService).getCustomerDiffusionTaskWithId(diffusionId);
        verify(taskSchedulerService).updateScheduler(any(), eq(TaskExecStatusEnum.FAILED));
        verifyNoInteractions(dorisService, customerGroupService);
    }

    @Test
    void parseDiffusionPredictResponseItemShouldReturnEmptyListWhenNoResultFiles() {
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        List<String> objectKeys = List.of("file1.txt", "file2.txt");

        List<DiffusionResponseItem> result = service.parseDiffusionPredictResponseItem(context, objectKeys);

        assertTrue(result.isEmpty());
        assertNotNull(context.getErr());
        assertInstanceOf(DeepSightException.GroupDiffusionOutputFeatureFileHandleException.class, context.getErr());
    }

    @Test
    void parseDiffusionPredictResponseItemShouldReturnEmptyListWhenNoResultFiles1() {
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        List<String> objectKeys = List.of("path/to/nonresultfile.txt");

        List<DiffusionResponseItem> result = service.parseDiffusionPredictResponseItem(context, objectKeys);

        assertTrue(result.isEmpty());
        assertInstanceOf(DeepSightException.GroupDiffusionOutputFeatureFileHandleException.class, context.getErr());
    }

    @Test
    void parseDiffusionPredictResponseItemShouldReturnFilteredItemsBasedOnSimilarity() {
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setSimilarity(80f);
        task.setJudgeCriteria(JudgeCriteriaEnum.SIMILARITY.getCode());
        context.setDiffusionTask(task);

        String resultFile = "path/to/resultfile.txt";
        List<String> objectKeys = List.of(resultFile);
        List<String> lines = List.of(
                "{\"score\":0.9,\"oneid\":\"id1\"}",
                "{\"score\":0.7,\"oneid\":\"id2\"}",
                "{\"oneid\":\"id3\"}"
        );

        DiffusionResponseItem item1 = new DiffusionResponseItem();
        item1.setScore(0.9f);
        item1.setOneId("id1");

        DiffusionResponseItem item2 = new DiffusionResponseItem();
        item2.setScore(0.7f);
        item2.setOneId("id2");

        service = Mockito.spy(service);
        doReturn(lines).when(service).ossFileReaderLines(any(), anyString());

        List<DiffusionResponseItem> result = service.parseDiffusionPredictResponseItem(context, objectKeys);

        assertEquals(1, result.size());
        assertEquals("id1", result.get(0).getOneId());
        assertEquals(0.9f, result.get(0).getScore());
    }

    @Test
    void parseDiffusionPredictResponseItemShouldUseDefaultSimilarityWhenNotSet() {
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            DiffusionCalculateContext context = new DiffusionCalculateContext();
            context.setDiffusionTask(new CustomerDiffusionTask());

            String resultFile = "path/to/resultfile.txt";
            List<String> objectKeys = List.of(resultFile);
            List<String> lines = List.of("{\"score\":0.5,\"oneid\":\"id1\"}");

            DiffusionResponseItem item = new DiffusionResponseItem();
            item.setScore(0.5f);
            item.setOneId("id1");

            jsonUtilsMock.when(() -> JsonUtils.toObjectWithoutException(anyString(), eq(DiffusionResponseItem.class)))
                    .thenReturn(item);

            service = Mockito.spy(service);
            doReturn(lines).when(service).ossFileReaderLines(any(), anyString());

            List<DiffusionResponseItem> result = service.parseDiffusionPredictResponseItem(context, objectKeys);

            assertEquals(1, result.size());
            assertEquals("id1", result.get(0).getOneId());
        }
    }

    @Test
    void parseDiffusionPredictResponseItemShouldSkipNullItems() {
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            DiffusionCalculateContext context = new DiffusionCalculateContext();
            context.setDiffusionTask(new CustomerDiffusionTask());

            String resultFile = "path/to/resultfile.txt";
            List<String> objectKeys = List.of(resultFile);
            List<String> lines = List.of("{\"invalid\":json}");

            jsonUtilsMock.when(() -> JsonUtils.toObjectWithoutException(anyString(), eq(DiffusionResponseItem.class)))
                    .thenReturn(null);

            service = Mockito.spy(service);
            doReturn(lines).when(service).ossFileReaderLines(any(), anyString());

            List<DiffusionResponseItem> result = service.parseDiffusionPredictResponseItem(context, objectKeys);

            assertTrue(result.isEmpty());
        }
    }

    @Test
    void parseDiffusionPredictResponseItem_ShouldReturnEmptyListWhenNoResultFiles() {
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        List<String> objectKeys = List.of("file1.txt", "file2.txt");

        List<DiffusionResponseItem> result = service.parseDiffusionPredictResponseItem(context, objectKeys);

        assertTrue(result.isEmpty());
        assertNotNull(context.getErr());
        verify(bosUtils, never()).getObject(any(), any());
    }

    @Test
    void parseDiffusionPredictResponseItem_ShouldReturnFilteredAndSortedResults() throws Exception {
        // Setup context
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setSimilarity(50f);
        task.setRanking(2);
        task.setJudgeCriteria(JudgeCriteriaEnum.SIMILARITY.getCode());
        context.setDiffusionTask(task);

        // Mock file content
        String json1 = "{\"score\":0.6,\"oneid\":\"id1\"}";
        String json2 = "{\"score\":0.8,\"oneid\":\"id2\"}";
        String json3 = "{\"score\":0.4,\"oneid\":\"id3\"}";
        List<String> lines = List.of(json1, json2, json3);

        // Mock object keys
        List<String> objectKeys = List.of("path/result_file1.txt");

        service = Mockito.spy(service);
        doReturn(lines).when(service).ossFileReaderLines(any(), anyString());

        List<DiffusionResponseItem> result = service.parseDiffusionPredictResponseItem(context, objectKeys);

        assertEquals(2, result.size());
        assertEquals(0.8f, result.get(0).getScore());
        assertEquals(0.6f, result.get(1).getScore());
        assertNull(context.getErr());
    }

    @Test
    void parseDiffusionPredictResponseItem_ShouldHandleJsonParseError() throws Exception {
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        List<String> objectKeys = List.of("path/result_file.txt");
        List<String> lines = List.of("invalid json");

        service = Mockito.spy(service);
        doReturn(lines).when(service).ossFileReaderLines(any(), anyString());

        List<DiffusionResponseItem> result = service.parseDiffusionPredictResponseItem(context, objectKeys);

        assertTrue(result.isEmpty());
        assertNull(context.getErr());
    }

    @Test
    void parseDiffusionPredictResponseItem_ShouldReturnAllResultsWhenNoSimilarityThreshold() {
        // Setup context
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setRanking(10);
        context.setDiffusionTask(task);

        // Mock file content
        String json1 = "{\"score\":0.6,\"oneid\":\"id1\"}";
        String json2 = "{\"score\":0.8,\"oneid\":\"id2\"}";
        List<String> lines = List.of(json1, json2);

        // Mock object keys
        List<String> objectKeys = List.of("path/result_file.txt");

        service = Mockito.spy(service);
        doReturn(lines).when(service).ossFileReaderLines(any(), anyString());

        List<DiffusionResponseItem> result = service.parseDiffusionPredictResponseItem(context, objectKeys);

        assertEquals(2, result.size());
        assertNull(context.getErr());
    }

    @Test
    void parseDiffusionPredictResponseItem_ShouldApplyDefaultRankingWhenNotSpecified() {
        // Setup context
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setSimilarity(0f);
        task.setJudgeCriteria((byte) 0);
        context.setDiffusionTask(task);

        // Mock file content - 3 items
        String json1 = "{\"score\":0.6,\"oneid\":\"id1\"}";
        String json2 = "{\"score\":0.8,\"oneid\":\"id2\"}";
        String json3 = "{\"score\":0.7,\"oneid\":\"id3\"}";
        List<String> lines = List.of(json1, json2, json3);

        // Mock object keys
        List<String> objectKeys = List.of("path/result_file.txt");

        service = Mockito.spy(service);
        doReturn(lines).when(service).ossFileReaderLines(any(), anyString());

        List<DiffusionResponseItem> result = service.parseDiffusionPredictResponseItem(context, objectKeys);

        assertEquals(3, result.size()); // Should return all since no ranking limit
        assertNull(context.getErr());
    }

    @Test
    public void testJson() {

        List<String> lines = Lists.newArrayList(
                "{\"oneid\": \"fd012ff0288b45398d341f64fb95ac5d\", \"score\": 0.500010012088751}",
                "{\"oneid\": \"7b72edb45ee1400fbcb8258e9936653b\", \"score\": 0.5000151135400301}",
                "{\"oneid\": \"bc95de42bcf24d1eb530c063e2377b7a\", \"score\": 0.5000107591959022}",
                "{\"oneid\": \"429b1e66e2bb41e4ae3e8eaeb153aba0\", \"score\": 0.5000095435298135}",
                "{\"oneid\": \"5e668ada41944b80acaea89c4e46c21c\", \"score\": 0.500011746025848}",
                "{\"oneid\": \"baac4b86f2ce4d66b3978d9974458dd1\", \"score\": 0.5000103129896161}",
                "{\"oneid\": \"d59d367e1e3f4bfdaf1fb12c7187216d\", \"score\": 0.500012069350305}",
                "{\"oneid\": \"55662f7168244a62a7864f1c9e2737cd\", \"score\": 0.5000127682824314}",
                "{\"oneid\": \"5f370b110be143e393cd7cc3938bc093\", \"score\": 0.5000131615506689}",
                "{\"oneid\": \"35d0ef45c28b4055a925da36775105ed\", \"score\": 0.5000110251403385}",
                "{\"oneid\": \"1f539e5ccd6b4364aa52d2ddd7ff5add\", \"score\": 0.5000118602283692}",
                "{\"oneid\": \"79f4f03c542644b2aa77d64d917fae4f\", \"score\": 0.5000128418696477}",
                "{\"oneid\": \"0deda0bd2162497dbd3e4d9cd75f6735\", \"score\": 0.5000129363306769}",
                "{\"oneid\": \"370baf740ac1446cb4171812338e1f43\", \"score\": 0.5000114206950457}",
                "{\"oneid\": \"48a8df93b9c6445db40368a34df621d5\", \"score\": 0.5000143284614751}",
                "{\"oneid\": \"5d1aabf35f0048619288aad07b8e5b85\", \"score\": 0.5000118990756164}",
                "{\"oneid\": \"3cc84d6e03d84a9a91062a8a0481779e\", \"score\": 0.5000115781922432}",
                "{\"oneid\": \"74670b5817844abd9d491ab305636ac3\", \"score\": 0.5000104704486148}",
                "{\"oneid\": \"6e579372a14d4dfea196591a88cd1817\", \"score\": 0.5000111437320811}",
                "{\"oneid\": \"36a32ebd62e445028fb34217318d2e40\", \"score\": 0.5000125564956761}",
                "{\"oneid\": \"ae1f4bc6e647415081f0ba06f0420fcb\", \"score\": 0.5000127258454086}",
                "{\"oneid\": \"09858d2112e045a5b34f2383b6a84b04\", \"score\": 0.5000121187840705}",
                "{\"oneid\": \"c45278b12e994b8d8b429ef5bde21f99\", \"score\": 0.5000122746650959}",
                "{\"oneid\": \"109491e1816d4b959070c41694e443c4\", \"score\": 2.5596667910576798e-05}",
                "{\"oneid\": \"ae1f4bc6e647415081f0ba06f0420fcb\", \"score\": 0.5000127258454086}",
                "{\"oneid\": \"9aa729d8db604b3dbada3f63908d85dd\", \"score\": 3.243797618779354e-05}",
                "{\"oneid\": \"328b73fbb409484985eb921463ace2a7\", \"score\": 2.477962516422849e-05}",
                "{\"oneid\": \"7603b1dee1b64d388b247eda79594f30\", \"score\": 3.0652256100438535e-05}",
                "{\"oneid\": \"a71f9d4aa6394392a61fda8bbf2ff334\", \"score\": 3.621106952778064e-05}",
                "{\"oneid\": \"09858d2112e045a5b34f2383b6a84b04\", \"score\": 0.5000121187840705}",
                "{\"oneid\": \"c45278b12e994b8d8b429ef5bde21f99\", \"score\": 0.5000122746650959}",
                "{\"oneid\": \"f99d31339f954731b7532d5cb50491ba\", \"score\": 2.6176283427048475e-05}",
                "{\"oneid\": \"42982e5295574abc8c926fa6701a5d1e\", \"score\": 2.9498522053472698e-05}",
                "{\"oneid\": \"c506b72adf484b978227d044e6dff983\", \"score\": 2.6554433134151623e-05}",
                "{\"oneid\": \"79f4f03c542644b2aa77d64d917fae4f\", \"score\": 0.5000128418696477}",
                "{\"oneid\": \"0deda0bd2162497dbd3e4d9cd75f6735\", \"score\": 0.5000129363306769}",
                "{\"oneid\": \"e02bcd42b2a84051831a0deea221ca7c\", \"score\": 2.192915417253971e-05}",
                "{\"oneid\": \"2acfb51b342543c19b29aafb67697cd0\", \"score\": 3.2359559554606676e-05}",
                "{\"oneid\": \"753b306894784c1d9749da3ad3f12857\", \"score\": 2.5596667910576798e-05}",
                "{\"oneid\": \"33e8cde3233f4997b068db62f687ed7d\", \"score\": 2.5596667910576798e-05}",
                "{\"oneid\": \"73f52b0cfaf44119920c50b822a24cfe\", \"score\": 2.89054551103618e-05}",
                "{\"oneid\": \"6e579372a14d4dfea196591a88cd1817\", \"score\": 0.5000111437320811}",
                "{\"oneid\": \"36a32ebd62e445028fb34217318d2e40\", \"score\": 0.5000125564956761}",
                "{\"oneid\": \"84d39feaa9bb4a6fa50b95d88fa53b64\", \"score\": 2.4874780137906782e-05}",
                "{\"oneid\": \"85c202405e6c4fdeb74a3ce7db0f613d\", \"score\": 2.4861905330908485e-05}",
                "{\"oneid\": \"1bf8b2a12ff94826b47926ae039ea307\", \"score\": 2.6272418836015277e-05}",
                "{\"oneid\": \"593a0d33837b4aa38ec34959344845ae\", \"score\": 2.7763455364038236e-05}",
                "{\"oneid\": \"3cc84d6e03d84a9a91062a8a0481779e\", \"score\": 0.5000115781922432}",
                "{\"oneid\": \"74670b5817844abd9d491ab305636ac3\", \"score\": 0.5000104704486148}",
                "{\"oneid\": \"8baac2dfb17b4ba1891340014b339b48\", \"score\": 3.0279503334895708e-05}",
                "{\"oneid\": \"4eb22518820248e49647ffe927c6acea\", \"score\": 3.005877442774363e-05}",
                "{\"oneid\": \"8d4048ef78834ac191663ea5125f71ff\", \"score\": 2.483837124600541e-05}",
                "{\"oneid\": \"64a7db479c8145459c29c6dc0275ee43\", \"score\": 2.367738306929823e-05}",
                "{\"oneid\": \"004197c6cd8d4a72aad3cc5d398710c4\", \"score\": 2.5596667910576798e-05}",
                "{\"oneid\": \"35d0ef45c28b4055a925da36775105ed\", \"score\": 0.5000110251403385}",
                "{\"oneid\": \"1f539e5ccd6b4364aa52d2ddd7ff5add\", \"score\": 0.5000118602283692}",
                "{\"oneid\": \"b554a79f6ed142beb69ad305f32ca74a\", \"score\": 2.5710622139740735e-05}",
                "{\"oneid\": \"4fbbc580492a467d8670820a45f5dce4\", \"score\": 2.398461765551474e-05}",
                "{\"oneid\": \"c53703f36c29459bb5afef3c612c81f0\", \"score\": 2.1850495613762178e-05}",
                "{\"oneid\": \"370baf740ac1446cb4171812338e1f43\", \"score\": 0.5000114206950457}",
                "{\"oneid\": \"48a8df93b9c6445db40368a34df621d5\", \"score\": 0.5000143284614751}",
                "{\"oneid\": \"5d1aabf35f0048619288aad07b8e5b85\", \"score\": 0.5000118990756164}",
                "{\"oneid\": \"08051109a4b440b4aa5abf15e7db6d66\", \"score\": 2.3233717001858167e-05}",
                "{\"oneid\": \"282e1eef57434349b5a56bc106b681c5\", \"score\": 2.988530832226388e-05}",
                "{\"oneid\": \"d31bf114f03d440e86ff2619027bf2cc\", \"score\": 2.421792669338174e-05}",
                "{\"oneid\": \"baac4b86f2ce4d66b3978d9974458dd1\", \"score\": 0.5000103129896161}",
                "{\"oneid\": \"d59d367e1e3f4bfdaf1fb12c7187216d\", \"score\": 0.500012069350305}",
                "{\"oneid\": \"55662f7168244a62a7864f1c9e2737cd\", \"score\": 0.5000127682824314}",
                "{\"oneid\": \"5f370b110be143e393cd7cc3938bc093\", \"score\": 0.5000131615515784}",
                "{\"oneid\": \"352facf1d01e4c99921401f5e858979d\", \"score\": 2.6811027055373415e-05}",
                "{\"oneid\": \"fd012ff0288b45398d341f64fb95ac5d\", \"score\": 0.500010012088751}",
                "{\"oneid\": \"7b72edb45ee1400fbcb8258e9936653b\", \"score\": 0.5000151135400301}",
                "{\"oneid\": \"bc95de42bcf24d1eb530c063e2377b7a\", \"score\": 0.5000107591959022}",
                "{\"oneid\": \"429b1e66e2bb41e4ae3e8eaeb153aba0\", \"score\": 0.5000095435389085}",
                "{\"oneid\": \"5e668ada41944b80acaea89c4e46c21c\", \"score\": 0.5000117460040201}"
        );


        Map<String, DiffusionResponseItem> resultMap = lines.stream()
                .map(line -> JsonUtils.toObjectWithoutException(line, DiffusionResponseItem.class))
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getScore()))
                .collect(Collectors.toMap(
                        DiffusionResponseItem::getOneId,
                        item -> item,
                        (k1, k2) -> k1
                ));

        List<DiffusionResponseItem> results = resultMap.values().stream()
                .sorted(Comparator.comparing(DiffusionResponseItem::getScore).reversed()).collect(Collectors.toList());

        System.out.println(results.size());

        results = results.subList(0, Math.min(50, results.size()));
        assertEquals(50, results.size());
    }

    @Test
    void parseDiffusionPredictResponseItem_ShouldFilterBySimilarity() {
        // Arrange
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setJudgeCriteria(JudgeCriteriaEnum.SIMILARITY.getCode());
        task.setSimilarity(85f);
        context.setDiffusionTask(task);
        List<String> objectKeys = List.of("tmp/result_file1.txt");
        String json1 = "{\"score\":0.8,\"oneid\":\"id1\"}";
        String json2 = "{\"score\":0.9,\"oneid\":\"id2\"}";

        service = Mockito.spy(service);
        doReturn(List.of(json1, json2)).when(service).ossFileReaderLines(any(), anyString());

        // Act
        List<DiffusionResponseItem> result = service.parseDiffusionPredictResponseItem(context, objectKeys);

        // Assert
        assertEquals(1, result.size());
        assertEquals(0.9f, result.get(0).getScore());
    }

    @Test
    void parseDiffusionPredictResponseItem_ShouldFilterByRanking() {
        // Arrange
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setJudgeCriteria(JudgeCriteriaEnum.RANKING.getCode());
        task.setRanking(1);
        context.setDiffusionTask(task);
        List<String> objectKeys = List.of("tmp/result_file1.txt");

        String json1 = "{\"score\":0.8,\"oneid\":\"id1\"}";
        String json2 = "{\"score\":0.9,\"oneid\":\"id2\"}";

        service = Mockito.spy(service);
        doReturn(List.of(json1, json2)).when(service).ossFileReaderLines(any(), anyString());

        // Act
        List<DiffusionResponseItem> result = service.parseDiffusionPredictResponseItem(context, objectKeys);

        // Assert
        assertEquals(1, result.size());
        assertEquals(0.9f, result.get(0).getScore());
    }

    @Test
    void parseDiffusionPredictResponseItem_ShouldReturnAllWhenNoCriteria() {
        // Arrange
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setJudgeCriteria((byte) 2); // invalid criteria
        context.setDiffusionTask(task);
        List<String> objectKeys = List.of("tmp/result_file1.txt");

        String json1 = "{\"score\":0.8,\"oneid\":\"id1\"}";
        String json2 = "{\"score\":0.9,\"oneid\":\"id2\"}";

        service = Mockito.spy(service);
        doReturn(List.of(json1, json2)).when(service).ossFileReaderLines(any(), anyString());

        // Act
        List<DiffusionResponseItem> result = service.parseDiffusionPredictResponseItem(context, objectKeys);

        // Assert
        assertEquals(2, result.size());
    }

    @Test
    void parseDiffusionPredictResponseItem_ShouldReturnEmptyListWhenNoResultFiles1() {
        // Arrange
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        context.setDiffusionTask(task);
        List<String> objectKeys = List.of("file1.txt", "file2.txt");

        // Act
        List<DiffusionResponseItem> result = service.parseDiffusionPredictResponseItem(context, objectKeys);

        // Assert
        assertTrue(result.isEmpty());
        assertNotNull(context.getErr());
    }

    @Test
    void parseDiffusionPredictResponseItem_ShouldReturnEmptyListWhenNoValidLines() {
        // Arrange
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        context.setDiffusionTask(task);
        List<String> objectKeys = List.of("result_file1.txt");

        // Act
        List<DiffusionResponseItem> result = service.parseDiffusionPredictResponseItem(context, objectKeys);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void parseDiffusionPredictResponseItem_ShouldReturnSortedItems() {
        // Arrange
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        context.setDiffusionTask(task);
        List<String> objectKeys = List.of("tmp/result_file1.txt");
        String json1 = "{\"score\":0.8,\"oneid\":\"id1\"}";
        String json2 = "{\"score\":0.9,\"oneid\":\"id2\"}";

        service = Mockito.spy(service);
        doReturn(List.of(json1, json2)).when(service).ossFileReaderLines(any(), anyString());

        // Act
        List<DiffusionResponseItem> result = service.parseDiffusionPredictResponseItem(context, objectKeys);

        // Assert
        assertEquals(2, result.size());
        assertEquals(0.9f, result.get(0).getScore());
        assertEquals(0.8f, result.get(1).getScore());
    }
}