package com.baidu.keyue.deepsight.schedule;

import com.baidu.keyue.deepsight.database.redis.RedisConfiguration;
import com.baidu.keyue.deepsight.schedule.task.MetricAggAsyncTask;
import com.baidu.keyue.deepsight.service.agg.AiobSessionMetricAggService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
public class MetricAggScheduler {

    @Value("${switch.aiobSessionMetricAgg:false}")
    private boolean aiobSessionMetricAgg;

    @Autowired
    private RedisConfiguration redisConfiguration;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private AiobSessionMetricAggService aiobSessionMetricAggService;

    @Autowired
    private MetricAggAsyncTask metricAggAsyncTask;

    public String generateSchedulerLockKey(String... keys) {
        return StringUtils.join(keys, "-");
    }

    /**
     * 统计外呼通话记录 90天内的统计指标
     */
    @Scheduled(cron = "${cron.pullBaiduData:0 0 2 * * *}")
    public void aiobSessionMetricAgg() {
        if (!aiobSessionMetricAgg) {
            log.info("aiobSessionMetricAgg is disabled. quit...");
            return;
        }

        log.info("aiobSessionMetricAgg task start");
        String lockKey = generateSchedulerLockKey(redisConfiguration.getPrefix(), "aiobSessionMetricAgg", "Lock");
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("aiobSessionMetricAgg get lock failed.");
            return;
        }

        // 通过 show tables 获取aiob_conversation_session_agg_（天级别指标明细）
        List<String> sessionAggTables = aiobSessionMetricAggService.getSessionAggTables();
        if (CollectionUtils.isEmpty(sessionAggTables)) {
            log.info("aiobSessionMetricAgg got empty diffusionTask. quit...");
            return;
        }

        try {
            log.info("aiobSessionMetricAgg sessionAggTables: {}", sessionAggTables);
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            for (String table : sessionAggTables) {
                futures.add(metricAggAsyncTask.processTable(table));
            }

            // 等待所有异步任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            // 统计 90天的外呼通话统计指标
        } catch (Exception e) {
            log.error("try { exec task failed, ", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        log.info("aiobSessionMetricAgg task finished.");
    }
}