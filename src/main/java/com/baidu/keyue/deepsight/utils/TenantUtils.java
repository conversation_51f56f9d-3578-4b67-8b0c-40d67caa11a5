package com.baidu.keyue.deepsight.utils;

import com.baidu.keyue.deepsight.config.Constants;
import lombok.experimental.UtilityClass;

@UtilityClass
public class TenantUtils {

    /**
     * 根据租户 id 生成 user 用户档案表
     * @param tenantId
     * @return
     */
    public String generateUserProfileTableName(String tenantId) {
        return Constants.DORIS_DEFAULT_USER_PROFILE_TABLE + "_" + tenantId;
    }

    /**
     * 根据租户 id 生成 user 用户基础信息表
     * @param tenantId
     * @return
     */
    public String generateMockUserTableName(String tenantId) {
        return Constants.DORIS_DEFAULT_LABEL_TABLE + "_" + tenantId;
    }

    /**
     * 根据租户 id 生成 外呼 表名
     * @param tenantId
     * @return
     */
    public static String generateAiobSessionTableName(String tenantId) {
        return Constants.DORIS_AIOB_SESSION_TABLE + "_" + tenantId;
    }


    /**
     * 根据租户 id 生成 记忆提取 表名
     * @param tenantId
     * @return
     */
    public static String generateMemoryExtractTableName(String tenantId) {
        return Constants.DORIS_MEMORY_EXTRACT_TABLE + "_" + tenantId;
    }

    /**
     * 根据租户 id 生成 外呼对话内容 表名
     * @param tenantId
     * @return
     */
    public static String generateAiobRecordTableName(String tenantId) {
        return Constants.DORIS_AIOB_RECORD_TABLE + "_" + tenantId;
    }

    /**
     * 根据租户 id 生成 客服对话内容 表名
     * @param tenantId
     * @return
     */
    public static String generateKeyueRecordTableName(String tenantId) {
        return Constants.DORIS_KEYUE_RECORD_TABLE + "_" + tenantId;
    }

    /**
     * 根据租户 id 生成 访客管理 表名
     * @param tenantId
     * @return
     */
    public static String generateVisitorManageTableName(String tenantId) {
        return Constants.DORIS_VISITOR_MANAGE_TABLE + "_" + tenantId;
    }

    /**
     * 根据租户 id 生成 id-mapping 表名
     * @param tenantId
     * @return
     */
    public static String generateIdMappingTableName(String tenantId) {
        return Constants.DORIS_ID_MAPPING_TABLE + "_" + tenantId;
    }

    /**
     * 根据租户 id 生成 外呼 SOP 节点统计表名
     * @param tenantId 租户 id
     * @return 表名
     */
    public static String generateAiobSOPNodeTableName(String tenantId) {
        return Constants.DORIS_AIOB_SOP_NODE_METRIC_TABLE_PREFIX + "_" + tenantId;
    }

    /**
     * 根据租户 id 生成 外呼 SOP 边统计表名
     * @param tenantId 租户 id
     * @return 表名
     */
    public static String generateAiobSOPEdgeTableName(String tenantId) {
        return Constants.DORIS_AIOB_SOP_EDGE_METRIC_TABLE_PREFIX + "_" + tenantId;
    }

    /**
     * 根据租户 id 生成 外呼 Debug 信息表名
     * @param tenantId 租户 id
     * @return 表名
     */
    public static String generateAiobDebugTableName(String tenantId) {
        return Constants.DORIS_AIOB_DEBUG_RECORD_TABLE_PREFIX + "_" + tenantId;
    }

    /**
     * 根据租户 id 生成 用户指标表名
     * @param tenantId 租户 id
     * @return 表名
     */
    public static String generateUserMetricTableName(String tenantId) {
        return Constants.USER_METRIC_AGG_TABLE_PREFIX + tenantId;
    }
}
