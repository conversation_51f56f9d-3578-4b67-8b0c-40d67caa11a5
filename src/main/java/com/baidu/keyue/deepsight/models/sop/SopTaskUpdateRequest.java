package com.baidu.keyue.deepsight.models.sop;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * SOP离线分析任务编辑请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SopTaskUpdateRequest extends SopTaskCfgRequestBase{

    /**
     * 任务ID
     */
    @NotNull(message = "任务ID不能为空")
    private Long id;

    @NotNull(message = "用户任务配置ID不能为空")
    private Long userConfigId;

}