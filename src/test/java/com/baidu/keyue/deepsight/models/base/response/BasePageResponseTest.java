package com.baidu.keyue.deepsight.models.base.response;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.exception.ErrorCode;

import org.junit.Before;
import org.junit.Test;
import org.slf4j.MDC;

public class BasePageResponseTest{

    private ErrorCode mockErrorCode;

    @Before
    public void setUp() {
        mockErrorCode = mock(ErrorCode.class);
        when(mockErrorCode.getCode()).thenReturn("400");
        when(mockErrorCode.getMessage()).thenReturn("Bad Request");
    }

    // testOf 用于测试 of
    // generated by Comate
    @Test
    public void testOf() {
        // Mock the request ID
        String requestId = "1234567890";
        MDC.put(Constants.REQUEST_ID_FIELD, requestId);
    
        // Call the method under test
        BasePageResponse<?> response = BasePageResponse.of(mockErrorCode);
    
        // Verify the results
        assertEquals(Integer.valueOf(400), response.getCode());
        assertEquals("Bad Request", response.getMessage());
        assertEquals(requestId, response.getRequestId());
    }

}