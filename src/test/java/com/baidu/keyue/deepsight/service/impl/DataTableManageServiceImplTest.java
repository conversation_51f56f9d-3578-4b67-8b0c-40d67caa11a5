package com.baidu.keyue.deepsight.service.impl;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.*;

import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.models.base.request.DeepSightWebContext;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableListRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableContentListRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.UpdateTableFieldConfigRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.TableDetailResponse;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;

import com.baidu.keyue.deepsight.service.datamanage.impl.DataTableManageServiceImpl;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class DataTableManageServiceImplTest{

    private TableFieldMetaInfo tableFieldMetaInfo;

    private GetTableContentListRequest request;

    @Mock
    private DataTableInfoMapper dataTableInfoMapper;

    @InjectMocks
    private DataTableManageServiceImpl dataTableManageService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        DeepSightWebContext context = new DeepSightWebContext();
        context.setTenantId(1L);
        WebContextHolder.setDeepSightWebContext(context);
    }

    // testCheckDuplicateKeysNoDuplicates 用于测试 checkDuplicateKeys
    // generated by Comate
    @Test
    public void testCheckDuplicateKeysNoDuplicates() {
        List<Map<String, String>> list = new ArrayList<>();
        Map<String, String> map1 = new HashMap<>();
        map1.put("key", "value1");
        Map<String, String> map2 = new HashMap<>();
        map2.put("key", "value2");
        list.add(map1);
        list.add(map2);
        try {
            dataTableManageService.checkDuplicateKeys(list);
            assertFalse("Expected no exception to be thrown", false);
        } catch (DeepSightException.ParamsErrorException e) {
            assertFalse("Exception should not be thrown", true);
        }
    }

    // testCheckDuplicateKeysWithDuplicates 用于测试 checkDuplicateKeys
    // generated by Comate
    @Test(expected = DeepSightException.ParamsErrorException.class)
    public void testCheckDuplicateKeysWithDuplicates() {
        List<Map<String, String>> list = new ArrayList<>();
        Map<String, String> map1 = new HashMap<>();
        map1.put("key", "value1");
        Map<String, String> map2 = new HashMap<>();
        map2.put("key", "value1");
        list.add(map1);
        list.add(map2);
        dataTableManageService.checkDuplicateKeys(list);
    }

    // testGetDataTableListWithNullRequest 用于测试 getDataTableList
    // generated by Comate
    @Test(expected = DeepSightException.ParamsErrorException.class)
    public void testGetDataTableListWithNullRequest() {
        // Act
//        dataTableManageService.getDataTableList(null);
    }

    // testGetDataTableListWithEmptyResult 用于测试 getDataTableList
    // generated by Comate
    @Test
    public void testGetDataTableListWithEmptyResult() {
        GetTableListRequest request = new GetTableListRequest();
        request.setPageNo(1);
        request.setPageSize(10);
        request.setTableName("testTable");
        request.setTableType(1);
        when(dataTableInfoMapper.countByExample(any(DataTableInfoCriteria.class))).thenReturn(0L);
        BasePageResponse.Page<TableDetailResponse> result = dataTableManageService.getDataTableList(request);
        assertEquals(Integer.valueOf(1), result.getPageNo());
        assertEquals(Integer.valueOf(10), result.getPageSize());
        assertEquals(Long.valueOf(0L), result.getTotal());
        assertEquals(0, result.getResults().size());
        verify(dataTableInfoMapper, times(1)).countByExample(any(DataTableInfoCriteria.class));
        verify(dataTableInfoMapper, times(0)).selectByExample(any(DataTableInfoCriteria.class));
    }

    // testGetTableContentNullRequest 用于测试 getTableContent
    // generated by Comate
    @Test(expected = DeepSightException.ParamsErrorException.class)
    public void testGetTableContentNullRequest() {
        request = new GetTableContentListRequest();
        request.setDataTableId(1L);
        request.setPageNo(1);
        request.setPageSize(10);
    
//        dataTableManageService.getTableContent(new GetTableContentListRequest(), true);
    }

    // testUpdateTableFieldConfigNullRequest 用于测试 updateTableFieldConfig
    // generated by Comate
    @Test(expected = DeepSightException.ParamsErrorException.class)
    public void testUpdateTableFieldConfigNullRequest() {
        UpdateTableFieldConfigRequest request = new UpdateTableFieldConfigRequest();
        request.setDataTableId(1L);
        request.setEnName("testField");
        request.setFieldTag(TableFieldTagEnum.PRIMARY.getCode());
        request.setIsFilterCriteria(true);
        request.setValueType("string");
        tableFieldMetaInfo = new TableFieldMetaInfo();
        tableFieldMetaInfo.setId(1L);
        tableFieldMetaInfo.setDataTableId(1L);
        tableFieldMetaInfo.setEnField("testField");
        tableFieldMetaInfo.setFieldTag(TableFieldTagEnum.PRIMARY.getCode());
        tableFieldMetaInfo.setIsFilterCriteria(true);
        tableFieldMetaInfo.setValueType("string");
        dataTableManageService.updateTableFieldConfig(request);
    }

}