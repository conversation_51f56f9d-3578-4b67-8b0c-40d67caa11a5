package com.baidu.keyue.deepsight.database.redis;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "redis")
public class RedisConfiguration {
    private String host;
    private String port;
    private String password;
    private String prefix;
}
