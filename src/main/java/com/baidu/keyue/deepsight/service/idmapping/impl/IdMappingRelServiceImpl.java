package com.baidu.keyue.deepsight.service.idmapping.impl;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.enums.DataTableStatusEnum;
import com.baidu.keyue.deepsight.enums.DbTypeEnum;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.OperationModeEnum;
import com.baidu.keyue.deepsight.enums.PresetEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.idmapping.request.relation.CreateIdMappingRelRequest;
import com.baidu.keyue.deepsight.models.idmapping.request.relation.DeleteIdMappingRelRequest;
import com.baidu.keyue.deepsight.models.idmapping.request.relation.ListIdMappingRelRequest;
import com.baidu.keyue.deepsight.models.idmapping.request.relation.UpdateIdMappingRelRequest;
import com.baidu.keyue.deepsight.models.idmapping.response.relation.IdMappingDatasetFieldListResponse;
import com.baidu.keyue.deepsight.models.idmapping.response.relation.IdMappingDatasetListResponse;
import com.baidu.keyue.deepsight.models.idmapping.response.relation.IdMappingRelItemResponse;
import com.baidu.keyue.deepsight.models.operation.response.OperationModeResponse;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingDataTable;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingDataTableCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRelation;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRelationCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendIdMappingRelationMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingDataTableMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingRelationMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.idmapping.IdMappingRelService;
import com.baidu.keyue.deepsight.service.idmapping.IdMappingRuleService;
import com.baidu.keyue.deepsight.service.operation.OperationService;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @className: IdMappingRelServiceImpl
 * @description:
 * @author: wangzhongcheng
 * @date: 2025/3/6 15:32
 */
@Slf4j
@Service
public class IdMappingRelServiceImpl implements IdMappingRelService {

    @Autowired
    private IdMappingRelationMapper idMappingRelMapper;

    @Autowired
    private ExtendIdMappingRelationMapper extendIdMappingRelationMapper;

    @Autowired
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @Autowired
    private DataTableInfoMapper dataTableInfoMapper;

    @Autowired
    private IdMappingDataTableMapper idMappingDataTableMapper;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private IdMappingRuleService idMappingRuleService;
    
    @Resource
    private DataTableManageService dataTableManageService;

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void createIdMappingRel(CreateIdMappingRelRequest request) {
        String userId = WebContextHolder.getUserId();
        String tenantId = WebContextHolder.getTenantId();
        Long dataTableId = request.getDataTableId();
        List<String> tableEnFields = request.getTableEnFields();
        if (isDupIdMappingRel(dataTableId)) {
            log.error("创建ID对失败，已存在该数据集的ID对 dataTableId: {}", dataTableId);
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "数据集ID对已存在");
        }

        // 插入数据集ID对记录
        Date date = new Date();
        IdMappingRelation idMappingRelation = new IdMappingRelation();
        assembleIdMappingRel(dataTableId, tableEnFields, idMappingRelation);
        idMappingRelation.setDataTableId(dataTableId);
        idMappingRelation.setCreator(userId);
        idMappingRelation.setModifier(userId);
        idMappingRelation.setTenantId(tenantId);
        idMappingRelation.setCreateTime(date);
        idMappingRelation.setUpdateTime(date);
        idMappingRelation.setDel(DelEnum.NOT_DELETED.getBoolean());
        idMappingRelation.setPreset(PresetEnum.NOT_PRESET.getBoolean());
        idMappingRelMapper.insertSelective(idMappingRelation);

        // 添加id映射配置
        if (request.getAddToIdMapping()) {
            idMappingRuleService.autoAddIdMappingRule(idMappingRelation);
        }

        // 刷新ID对的数据集到oneId刷新列表
        if (request.getRefreshOneId()) {
            autoAddIdMappingDataTable(idMappingRelation);
        }
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void updateIdMappingRel(UpdateIdMappingRelRequest request) {
        String userId = WebContextHolder.getUserId();
        Long idMappingRelId = request.getIdMappingRelId();
        List<String> tableEnFields = request.getTableEnFields();
        IdMappingRelation idMappingRelation = checkIdMappingRel(idMappingRelId);

        // 更新ID对
        Date date = new Date();
        Long dataTableId = idMappingRelation.getDataTableId();
        assembleIdMappingRel(dataTableId, tableEnFields, idMappingRelation);
        idMappingRelation.setModifier(userId);
        idMappingRelation.setUpdateTime(date);
        idMappingRelMapper.updateByPrimaryKeySelective(idMappingRelation);

        // 添加id映射配置
        if (request.getAddToIdMapping()) {
            idMappingRuleService.autoAddIdMappingRule(idMappingRelation);
        }

        // 刷新ID对的数据集到oneId刷新列表
        if (request.getRefreshOneId()) {
            autoAddIdMappingDataTable(idMappingRelation);
        }
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void deleteIdMappingRel(DeleteIdMappingRelRequest request) {
        String userId = WebContextHolder.getUserId();
        Long idMappingRelId = request.getIdMappingRelId();
        IdMappingRelation idMappingRelation = checkIdMappingRel(idMappingRelId);

        // 删除ID对
        Date date = new Date();
        idMappingRelation.setModifier(userId);
        idMappingRelation.setUpdateTime(date);
        idMappingRelation.setDel(DelEnum.DELETED.getBoolean());
        idMappingRelMapper.updateByPrimaryKeySelective(idMappingRelation);
    }

    @Override
    public boolean isExistPublicId(Long dataTableId, List<String> enFields) {
        String tenantId = WebContextHolder.getTenantId();
        return extendIdMappingRelationMapper.countPublicId(tenantId, dataTableId, enFields) > 0;
    }

    @Override
    public BasePageResponse.Page<IdMappingRelItemResponse> listIdMappingRel(ListIdMappingRelRequest request) {
        // 构造查询条件
        String tenantId = WebContextHolder.getTenantId();
        IdMappingRelationCriteria idMappingRelCriteria = new IdMappingRelationCriteria();
        idMappingRelCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        long count = idMappingRelMapper.countByExample(idMappingRelCriteria);
        if (count == 0) {
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), 0L, Lists.newArrayList());
        }

        // 翻页组件
        PageHelper.startPage(request.getPageNo(), request.getPageSize());

        // 按照创建时间排序
        idMappingRelCriteria.setOrderByClause(Constants.ORDER_BY_PRESET_WITH_CREATE_TIME_DESC);
        List<IdMappingRelation> idMappingRelations = idMappingRelMapper.selectByExample(idMappingRelCriteria);

        // 构造返回结果
        Map<String, Long> enFieldCountMap = getEnFieldCountToIdMappingMap(tenantId);
        Map<Long, String> dataTableNameMap = getDataTableNameMap(idMappingRelations);
        List<IdMappingRelItemResponse> resultList = idMappingRelations.stream()
                .map(idMappingRel ->
                        IdMappingRelItemResponse.convertFrom(idMappingRel, enFieldCountMap, dataTableNameMap))
                .toList();
        return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), count, resultList);
    }

    @Override
    public IdMappingDatasetListResponse listIdMappingDataset() {
        String tenantId = WebContextHolder.getTenantId();

        // 查询ID对已使用的数据集
        IdMappingRelationCriteria idMappingRelationCriteria = new IdMappingRelationCriteria();
        idMappingRelationCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<IdMappingRelation> idMappingRelations = idMappingRelMapper.selectByExample(idMappingRelationCriteria);
        List<Long> usedDataTableIds = idMappingRelations.stream().map(IdMappingRelation::getDataTableId).toList();

        // 查询数据集列表
        DataTableInfoCriteria dataTableInfoCriteria = new DataTableInfoCriteria();
        dataTableInfoCriteria.createCriteria()
                .andDbTypeEqualTo(DbTypeEnum.DORIS_TYPE.getDbType())
                .andStatusEqualTo(DataTableStatusEnum.CREATED.getStatus().byteValue())
                .andIsVisableEqualTo(true)
                .andTenantidEqualTo(tenantId)
                .andIsDelEqualTo(DelEnum.NOT_DELETED.getCode());
        dataTableInfoCriteria.setOrderByClause(Constants.ORDER_BY_CREATE_TIME_DESC);
        List<DataTableInfo> dataTableInfos = dataTableInfoMapper.selectByExample(dataTableInfoCriteria);
        return IdMappingDatasetListResponse.convertFrom(usedDataTableIds, dataTableInfos);
    }

    @Override
    public IdMappingDatasetFieldListResponse listIdMappingDatasetField(Long dataTableId) {
        // 校验数据表是否为当前租户数据表
        dataTableManageService.validDataTableByTenantId(dataTableId);
        // 通过选择的数据表字段查询对应的字段
        TableFieldMetaInfoCriteria tableFieldCriteria = new TableFieldMetaInfoCriteria();
        tableFieldCriteria.createCriteria()
                .andIsVisableEqualTo(true)
                .andEnFieldNotEqualTo(Constants.TABLE_USER_ONE_ID)
                .andDataTableIdEqualTo(dataTableId);
        List<TableFieldMetaInfo> tableFieldMetaInfos = tableFieldMetaInfoMapper.selectByExample(tableFieldCriteria);

        return IdMappingDatasetFieldListResponse.convertFrom(tableFieldMetaInfos);
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void initDefaultIdMappingRel(String tenantId, Map<String, Long> presetDataTableNameMap) {

        // 基础用户信息表预置ID对抽取
        initUserInfoIdMappingRel(tenantId, presetDataTableNameMap);

        // 客服记录对话表预置ID对抽取
        initKeyueRecordIdMappingRel(tenantId, presetDataTableNameMap);

        // 外呼记录对话表预置ID对抽取
        initAiobIdMappingRel(tenantId, presetDataTableNameMap);
    }

    /**
     * 获取id对英文字段出现的个数
     * 此处查了改租户下的所有id对，统计每个字段出现的次数，
     * 一个租户下id对的个数是少量（最多为数据集个数）的加载到内存计算
     * @param tenantId 租户id
     * @return k-英文字段名，v-字段出现次数
     */
    @Override
    public Map<String, Long> getEnFieldCountToIdMappingMap(String tenantId) {
        if (StringUtils.isEmpty(tenantId)) {
            log.error("ID对统计英文字段个数，租户ID不能为空");
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "租户ID不能为空");
        }

        IdMappingRelationCriteria idMappingRelCriteria = new IdMappingRelationCriteria();
        idMappingRelCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<IdMappingRelation> idMappingRelations = idMappingRelMapper.selectByExample(idMappingRelCriteria);
        return idMappingRelations.stream()
                .filter(idMappingRel -> StringUtils.isNotEmpty(idMappingRel.getEnFields()))
                .flatMap(idMappingRel ->
                        JsonUtils.toListUnchecked(idMappingRel.getEnFields(), List.class, String.class).stream())
                .collect(Collectors.groupingBy(e -> e, Collectors.counting()));
    }

    @Override
    public Map<String, String> getEnFieldtoCnFieldMap(String tenantId) {
        if (StringUtils.isEmpty(tenantId)) {
            log.error("ID对查询配置字段，租户ID不能为空");
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "租户ID不能为空");
        }

        IdMappingRelationCriteria idMappingRelCriteria = new IdMappingRelationCriteria();
        idMappingRelCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<IdMappingRelation> idMappingRelations = idMappingRelMapper.selectByExample(idMappingRelCriteria);

        Map<String, String> enFieldtoCnFieldMap = new HashMap<>();
        for (IdMappingRelation idMappingRel : idMappingRelations) {
            List<String> enFields = JsonUtils.toListUnchecked(idMappingRel.getEnFields(), List.class, String.class);
            List<String> cnFields = JsonUtils.toListUnchecked(idMappingRel.getCnFields(), List.class, String.class);
            for (int i = 0; i < enFields.size(); i++) {
                enFieldtoCnFieldMap.put(enFields.get(i), cnFields.get(i));
            }
        }
        return enFieldtoCnFieldMap;
    }

    @Override
    public void validateIdMappingDateTable(String tenantId) {
        // 配置的ID对关系数据集不能被删除
        List<String> delDataTableName = extendIdMappingRelationMapper.listDelDataTableName(tenantId);
        if (CollectionUtils.isNotEmpty(delDataTableName)) {
            String delDataTableNameShowStr = String.join(",", delDataTableName);
            log.error("ID对关系数据集已被删除：{}", delDataTableNameShowStr);
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST,
                    "ID对关系数据集已被删除，不能启动 oneId 生成任务 数据集名称：" + delDataTableNameShowStr);
        }
    }

    /**
     * 获取数据表名映射
     * @param idMappingRelations id对记录列表
     * @return k-数据表id，v-数据表名
     */
    private Map<Long, String> getDataTableNameMap(List<IdMappingRelation> idMappingRelations) {
        List<Long> dataTableIds = idMappingRelations.stream().map(IdMappingRelation::getDataTableId).toList();
        DataTableInfoCriteria dataTableInfoCriteria = new DataTableInfoCriteria();
        dataTableInfoCriteria.createCriteria().andIdIn(dataTableIds);
        List<DataTableInfo> dataTableInfos = dataTableInfoMapper.selectByExample(dataTableInfoCriteria);

        return dataTableInfos.stream()
                .collect(Collectors.toMap(DataTableInfo::getId, DataTableInfo::getCnName, (v1, v2) -> v1));
    }

    /**
     * 校验id对是否存在
     * @param id id对记录主键id
     * @return id对信息
     */
    private IdMappingRelation checkIdMappingRel(Long id) {
        IdMappingRelationCriteria idMappingRelationCriteria = new IdMappingRelationCriteria();
        idMappingRelationCriteria.createCriteria()
                .andIdEqualTo(id)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean())
                .andTenantIdEqualTo(WebContextHolder.getTenantId());
        List<IdMappingRelation> idMappingRelations = idMappingRelMapper.selectByExample(idMappingRelationCriteria);
        if (CollectionUtils.isEmpty(idMappingRelations)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND, "ID对不存在");
        }

        return idMappingRelations.get(0);
    }

    /**
     * 封装idMappingRel的字段信息
     * @param dataTableId 数据表id
     * @param tableEnFields 字段列表
     * @param idMappingRelation id对记录
     * @return 字段信息列表
     */
    public void assembleIdMappingRel(Long dataTableId,
                                      List<String> tableEnFields,
                                      IdMappingRelation idMappingRelation) {
        TableFieldMetaInfoCriteria tableFieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
        tableFieldMetaInfoCriteria.createCriteria()
                .andEnFieldIn(tableEnFields)
                .andDataTableIdEqualTo(dataTableId);
        tableFieldMetaInfoCriteria.setOrderByClause(Constants.ORDER_BY_ID_ASC);
        List<TableFieldMetaInfo> fieldMetaInfos = tableFieldMetaInfoMapper.selectByExample(tableFieldMetaInfoCriteria);
        List<String> enFields = new ArrayList<>(fieldMetaInfos.size());
        List<String> cnFields = new ArrayList<>(fieldMetaInfos.size());

        // 保证了英文和中文的顺序
        for (TableFieldMetaInfo fieldMetaInfo : fieldMetaInfos) {
            enFields.add(fieldMetaInfo.getEnField());
            cnFields.add(fieldMetaInfo.getCnField());
        }
        idMappingRelation.setEnFields(JsonUtils.toJsonUnchecked(enFields));
        idMappingRelation.setCnFields(JsonUtils.toJsonUnchecked(cnFields));
    }

    /**
     * 校验是否存在重复的id对
     * @param dataTableId 数据表id
     * @return true-存在，false-不存在
     */
    private boolean isDupIdMappingRel(Long dataTableId) {
        String tenantId = WebContextHolder.getTenantId();
        IdMappingRelationCriteria idMappingRelationCriteria = new IdMappingRelationCriteria();
        idMappingRelationCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDataTableIdEqualTo(dataTableId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<IdMappingRelation> idMappingRelations = idMappingRelMapper.selectByExample(idMappingRelationCriteria);
        return CollectionUtils.isNotEmpty(idMappingRelations);
    }

    /**
     * 自动添加id对数据表，已存在的不添加
     */
    @Transactional(rollbackOn = Exception.class)
    public void autoAddIdMappingDataTable(IdMappingRelation idMappingRelation) {
        // 获取该租户下所有已存在的待刷新数据集
        IdMappingDataTableCriteria mappingDataTableCriteria = new IdMappingDataTableCriteria();
        mappingDataTableCriteria.createCriteria()
                .andDataTableIdEqualTo(idMappingRelation.getDataTableId())
                .andTenantIdEqualTo(idMappingRelation.getTenantId());
        List<IdMappingDataTable> idMappingTables = idMappingDataTableMapper.selectByExample(mappingDataTableCriteria);

        if (!idMappingTables.isEmpty()) {
            IdMappingDataTable idMappingDataTable = idMappingTables.get(0);
            idMappingDataTable.setModifier(WebContextHolder.getUserId());
            idMappingDataTable.setUpdateTime(idMappingRelation.getUpdateTime());
            idMappingDataTable.setDel(DelEnum.NOT_DELETED.getBoolean());
            idMappingDataTableMapper.updateByPrimaryKeySelective(idMappingDataTable);
            return;
        }

        IdMappingDataTable idMappingDataTable = new IdMappingDataTable();
        idMappingDataTable.setPreset(PresetEnum.NOT_PRESET.getBoolean());
        idMappingDataTable.setDel(DelEnum.NOT_DELETED.getBoolean());
        idMappingDataTable.setCreator(WebContextHolder.getUserId());
        idMappingDataTable.setModifier(WebContextHolder.getUserId());
        idMappingDataTable.setTenantId(idMappingRelation.getTenantId());
        idMappingDataTable.setCreateTime(idMappingRelation.getUpdateTime());
        idMappingDataTable.setUpdateTime(idMappingRelation.getUpdateTime());
        idMappingDataTable.setDataTableId(idMappingRelation.getDataTableId());
        idMappingDataTableMapper.insert(idMappingDataTable);
    }

    /**
     * 初始化用户信息表预置ID对
     *
     * @param tenantId 租户id
     * @param dataTableNameMap k-数据集表名，v-数据集id
     */
    @Transactional(rollbackOn = Exception.class)
    public void initUserInfoIdMappingRel(String tenantId, Map<String, Long> dataTableNameMap) {
        Date date = new Date();
        Long userDataTableId = dataTableNameMap.get(TenantUtils.generateMockUserTableName(tenantId));
        IdMappingRelation userIdMappingRel = new IdMappingRelation();
        userIdMappingRel.setCreateTime(date);
        userIdMappingRel.setUpdateTime(date);
        userIdMappingRel.setTenantId(tenantId);
        userIdMappingRel.setDataTableId(userDataTableId);
        userIdMappingRel.setDel(DelEnum.NOT_DELETED.getBoolean());
        userIdMappingRel.setPreset(PresetEnum.PRESET.getBoolean());
        userIdMappingRel.setCreator(Constants.SYSTEM_DEFAULT_USER_ID);
        userIdMappingRel.setModifier(Constants.SYSTEM_DEFAULT_USER_ID);
        assembleIdMappingRel(userDataTableId, Constants.USER_TABLE_ID_MAPPING_DEFAULT_REL, userIdMappingRel);
        idMappingRelMapper.insert(userIdMappingRel);
    }

    /**
     * 初始化客服表预置ID对
     *
     * @param tenantId 租户id
     * @param dataTableNameMap k-数据集表名，v-数据集id
     */
    @Transactional(rollbackOn = Exception.class)
    public void initKeyueRecordIdMappingRel(String tenantId, Map<String, Long> dataTableNameMap) {
        Date date = new Date();
        List<String> tableEnFields;

        // 根据运营模式获取不同的默认id对
        OperationService operationService = applicationContext.getBean(OperationService.class);
        OperationModeResponse tenantOperationMode = operationService.getTenantOperationMode(tenantId);
        switch (OperationModeEnum.getByCode(tenantOperationMode.getType())) {
            case OPERATION_BY_SELF -> {
                tableEnFields = Constants.KEYUE_RECORD_TABLE_SELF_ID_MAPPING_DEFAULT_REL;
            }
            case OPERATION_BY_BAIDU_OP -> {
                tableEnFields = Constants.KEYUE_RECORD_TABLE_BAIDU_ID_MAPPING_DEFAULT_REL;
            }
            default -> {
                log.error("获取运营标识失败: {}", tenantOperationMode.getType());
                throw new DeepSightException.InitTenantFailedException(ErrorCode.INTERNAL_ERROR, "获取运营标识失败");
            }
        }
        Long keyueRecordDataTableId = dataTableNameMap.get(TenantUtils.generateKeyueRecordTableName(tenantId));
        IdMappingRelation keyueRecordIdMappingRel = new IdMappingRelation();
        keyueRecordIdMappingRel.setCreateTime(date);
        keyueRecordIdMappingRel.setUpdateTime(date);
        keyueRecordIdMappingRel.setTenantId(tenantId);
        keyueRecordIdMappingRel.setDataTableId(keyueRecordDataTableId);
        keyueRecordIdMappingRel.setDel(DelEnum.NOT_DELETED.getBoolean());
        keyueRecordIdMappingRel.setPreset(PresetEnum.PRESET.getBoolean());
        keyueRecordIdMappingRel.setCreator(Constants.SYSTEM_DEFAULT_USER_ID);
        keyueRecordIdMappingRel.setModifier(Constants.SYSTEM_DEFAULT_USER_ID);
        assembleIdMappingRel(keyueRecordDataTableId, tableEnFields, keyueRecordIdMappingRel);
        idMappingRelMapper.insert(keyueRecordIdMappingRel);
    }

    /**
     * 初始化外呼表预置ID对
     *
     * @param tenantId 租户id
     * @param dataTableNameMap k-数据集表名，v-数据集id
     */
    private void initAiobIdMappingRel(String tenantId, Map<String, Long> dataTableNameMap) {
        OperationService operationService = applicationContext.getBean(OperationService.class);
        OperationModeResponse tenantOperationMode = operationService.getTenantOperationMode(tenantId);
        if (OperationModeEnum.getByCode(tenantOperationMode.getType()) == OperationModeEnum.OPERATION_BY_SELF) {
            log.info("当前租户为自运营模式，无需初始化外呼表预置ID对");
            return;
        }

        Date date = new Date();
        List<String> tableEnFields = Constants.AIOB_TABLE_BAIDU_ID_MAPPING_DEFAULT_REL;
        Long keyueRecordDataTableId = dataTableNameMap.get(TenantUtils.generateAiobSessionTableName(tenantId));
        IdMappingRelation keyueRecordIdMappingRel = new IdMappingRelation();
        keyueRecordIdMappingRel.setCreateTime(date);
        keyueRecordIdMappingRel.setUpdateTime(date);
        keyueRecordIdMappingRel.setTenantId(tenantId);
        keyueRecordIdMappingRel.setDataTableId(keyueRecordDataTableId);
        keyueRecordIdMappingRel.setDel(DelEnum.NOT_DELETED.getBoolean());
        keyueRecordIdMappingRel.setPreset(PresetEnum.PRESET.getBoolean());
        keyueRecordIdMappingRel.setCreator(Constants.SYSTEM_DEFAULT_USER_ID);
        keyueRecordIdMappingRel.setModifier(Constants.SYSTEM_DEFAULT_USER_ID);
        assembleIdMappingRel(keyueRecordDataTableId, tableEnFields, keyueRecordIdMappingRel);
        idMappingRelMapper.insert(keyueRecordIdMappingRel);
    }
}