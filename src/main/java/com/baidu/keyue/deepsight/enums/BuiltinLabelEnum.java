package com.baidu.keyue.deepsight.enums;

import com.baidu.keyue.deepsight.constants.SqlConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 内置标签枚举
 * 定义系统预置的6个内置标签
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
@Getter
@AllArgsConstructor
public enum BuiltinLabelEnum {

    /**
     * 陌生号码接通意愿
     */
    CONNECT_WILLINGNESS("陌生号码接通意愿", BuiltinLabelCatalogEnum.SALES_PREFERENCE, "CONNECT_RATE", "user_metric", Collections.singletonList("CONNECT_RATE")),

    /**
     * 陌拜开拓接受度
     */
    COLD_CALL_ACCEPTANCE("陌拜开拓接受度", BuiltinLabelCatalogEnum.SALES_PREFERENCE, "FIRST_ROUND_HANGUP_RATE", "user_metric", Collections.singletonList("FIRST_ROUND_HANGUP_RATE")),

    /**
     * 平均对话轮次
     */
    AVG_CONVERSATION_ROUNDS("平均对话轮次", BuiltinLabelCatalogEnum.SALES_PREFERENCE, "AVG_ROUNDS", "user_metric", Collections.singletonList("AVG_ROUNDS")),

    /**
     * 机器人名称 + 意向标签后缀（非真实标签名，动态标签名）
     */
    ROBOT_INTENTION_TAG("机器人名称" + SqlConstants.StringConstants.INTENTION_LABEL_SUFFIX, BuiltinLabelCatalogEnum.INTENTION_TAG, "customTagList",
            "aiob_conversation_session_service", Collections.singletonList("customTagList")),

    /**
     * 大模型标签的key如产品兴趣（非真实标签名，动态标签名）
     */
    LLM_TAG_KEY("大模型标签key", BuiltinLabelCatalogEnum.CONVERSATION_MINING, "tagExtractInfo", "aiob_conversation_session_service", Collections.singletonList("tagExtractInfo")),

    /**
     * 营销电话投诉风险
     */
    CUSTOMER_SERVICE_RISK("营销电话投诉风险", BuiltinLabelCatalogEnum.COMPLAINT_RISK, "conversationContent",
            "aiob_conversation_session_service", Arrays.asList("startTime", "conversationContent"));

    /**
     * 标签名称
     */
    private final String labelName;

    /**
     * 标签目录枚举
     */
    private final BuiltinLabelCatalogEnum catalog;

    /**
     * 数据字段名
     */
    private final String fieldName;

    /**
     * 数据源表前缀
     */
    private final String tablePrefix;
    
    /**
     * 验证字段列表
     */
    private final List<String> validationFields;


    /**
     * 获取目录枚举
     * @return 目录枚举
     */
    public BuiltinLabelCatalogEnum getCatalogEnum() {
        return catalog;
    }

    /**
     * 根据标签名称获取对应的枚举
     * @param labelName 标签名称
     * @return 对应的枚举，如果不是静态内置标签则返回null
     * <AUTHOR>
     */
    public static BuiltinLabelEnum getBuiltinLabelEnum(String labelName) {
        for (BuiltinLabelEnum labelEnum : BuiltinLabelEnum.values()) {
            if (labelEnum.getLabelName().equals(labelName)) {
                return labelEnum;
            }
        }
        return null;
    }
}
