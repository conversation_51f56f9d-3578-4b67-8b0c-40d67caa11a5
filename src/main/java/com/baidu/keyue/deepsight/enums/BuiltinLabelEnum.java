package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 内置标签枚举
 * 定义系统预置的6个内置标签
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
@Getter
@AllArgsConstructor
public enum BuiltinLabelEnum {

    /**
     * 陌生号码接通意愿
     */
    CONNECT_WILLINGNESS("陌生号码接通意愿", BuiltinLabelCatalogEnum.SALES_PREFERENCE, "CONNECT_RATE", "user_metric"),

    /**
     * 陌拜开拓接受度
     */
    COLD_CALL_ACCEPTANCE("陌拜开拓接受度", BuiltinLabelCatalogEnum.SALES_PREFERENCE, "FIRST_ROUND_HANGUP_RATE", "user_metric"),

    /**
     * 平均对话轮次
     */
    AVG_CONVERSATION_ROUNDS("平均对话轮次", BuiltinLabelCatalogEnum.SALES_PREFERENCE, "AVG_ROUNDS", "user_metric"),

    /**
     * 机器人名称_意向标签
     */
    ROBOT_INTENTION_TAG("机器人名称_意向标签", BuiltinLabelCatalogEnum.INTENTION_TAG, "customTagList", "aiob_conversation_session_service"),

    /**
     * 大模型标签的key如产品兴趣
     */
    LLM_TAG_KEY("大模型标签key", BuiltinLabelCatalogEnum.CONVERSATION_MINING, "tagExtraInfo", "aiob_conversation_session_service"),

    /**
     * 客服电话接受风险
     */
    CUSTOMER_SERVICE_RISK("客服电话接受风险", BuiltinLabelCatalogEnum.COMPLAINT_RISK, "conversationContent", "aiob_conversation_session_service");

    /**
     * 不可规则化的标签名称列表
     */
    public static final List<String> UN_RULEABLE_NAMES = Arrays.asList(ROBOT_INTENTION_TAG.labelName, LLM_TAG_KEY.labelName, CUSTOMER_SERVICE_RISK.labelName);

    /**
     * 标签名称
     */
    private final String labelName;

    /**
     * 标签目录枚举
     */
    private final BuiltinLabelCatalogEnum catalog;

    /**
     * 数据字段名
     */
    private final String fieldName;

    /**
     * 数据源表前缀
     */
    private final String tablePrefix;

    /**
     * 判断是否为基于指标的标签（标签1-3）
     * @return true表示基于指标，false表示需要特殊处理
     */
    public boolean isMetricBased() {
        return this == CONNECT_WILLINGNESS || 
               this == COLD_CALL_ACCEPTANCE || 
               this == AVG_CONVERSATION_ROUNDS;
    }

    /**
     * 判断是否为特殊处理标签（标签4-6）
     * @return true表示需要特殊处理，false表示基于指标
     */
    public boolean isCustomProcessing() {
        return !isMetricBased();
    }

    /**
     * 获取一级目录名称
     * @return 一级目录名称
     */
    public String getPrimaryCatalog() {
        return catalog.getParent() != null ? catalog.getParent().getCatalogName() : catalog.getCatalogName();
    }

    /**
     * 获取二级目录名称
     * @return 二级目录名称
     */
    public String getSecondaryCatalog() {
        return catalog.getCatalogName();
    }

    /**
     * 获取完整目录路径
     * @return 目录路径，如 "外呼行为/电销偏好"
     */
    public String getFullCatalogPath() {
        return catalog.getFullPath();
    }

    /**
     * 获取目录枚举
     * @return 目录枚举
     */
    public BuiltinLabelCatalogEnum getCatalogEnum() {
        return catalog;
    }
}
