package com.baidu.keyue.deepsight.config.threadpool;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

/**
 * *@ClassName TaskSchedulerConfig
 * *@Description Scheduler配置
 * *<AUTHOR>
 * *@Date 2025/8/4 3:16 PM
 */
@Slf4j
@Configuration
public class TaskSchedulerConfig {
    
    @Value("${poll.scheduler.poll-size:10}")
    private Integer schedulerPollSize;

    @Bean
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(schedulerPollSize);
        // 等待所有任务完成再关闭线程池
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        scheduler.setAwaitTerminationSeconds(30);
        scheduler.setThreadNamePrefix("scheduled-task-");
        scheduler.setErrorHandler(t -> log.error("定时任务异常", t));
        return scheduler;
    }
}
