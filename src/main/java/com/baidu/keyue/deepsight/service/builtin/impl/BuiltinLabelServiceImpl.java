package com.baidu.keyue.deepsight.service.builtin.impl;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.constants.BuiltinLabelConstants;
import com.baidu.keyue.deepsight.constants.SqlConstants;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.base.request.DeepSightWebContext;
import com.baidu.keyue.deepsight.enums.BuiltinLabelCatalogEnum;
import com.baidu.keyue.deepsight.enums.BuiltinLabelEnum;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.enums.UpdateModEnum;
import com.baidu.keyue.deepsight.enums.LabelValueSaveModEnum;
import com.baidu.keyue.deepsight.models.label.LabelRule;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalog;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalogCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.LabelCatalogMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.LabelMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.builtin.BuiltinLabelRuleBuilder;
import com.baidu.keyue.deepsight.models.catalog.NewCatalogRequest;
import com.baidu.keyue.deepsight.service.builtin.BuiltinLabelService;
import com.baidu.keyue.deepsight.service.builtin.BuiltinLabelCalculateService;
import com.baidu.keyue.deepsight.service.catalog.LabelCatalogService;
import com.baidu.keyue.deepsight.service.label.LabelService;
import com.baidu.keyue.deepsight.service.builtin.DynamicCatalogService;
import com.baidu.keyue.deepsight.service.builtin.LabelFieldValidationService;
import com.baidu.keyue.deepsight.service.tenant.TenantInfoService;
import com.baidu.keyue.deepsight.models.label.NewLabelRequest;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 内置标签服务实现
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BuiltinLabelServiceImpl implements BuiltinLabelService {

    private final LabelCatalogService labelCatalogService;
    private final LabelMapper labelMapper;
    private final LabelCatalogMapper labelCatalogMapper;
    private final LabelService labelService;
    private final TableFieldMetaInfoMapper tableFieldMetaInfoMapper;
    private final DynamicCatalogService dynamicCatalogService;
    private final LabelFieldValidationService labelFieldValidationService;
    private final TenantInfoService tenantInfoService;
    private final BuiltinLabelCalculateService builtinLabelCalculateService;

    @Override
    @Transactional
    public void createBuiltinLabelsForTenant(String tenantId) {
        log.info("开始为租户 {} 创建内置标签", tenantId);
        
        try {
            // 1. 确保内置标签所需的目录存在
            ensureBuiltinLabelCatalogs(tenantId);
            
            // 创建机器人名称动态目录
            dynamicCatalogService.createRobotNameCatalogs(tenantId);

            // 2. 创建静态标签（标签1-3、6）
            for (BuiltinLabelEnum labelEnum : BuiltinLabelEnum.values()) {
                // 跳过动态标签4-5，单独处理
                if (labelEnum == BuiltinLabelEnum.ROBOT_INTENTION_TAG || labelEnum == BuiltinLabelEnum.LLM_TAG_KEY) {
                    continue;
                }
                
                try {
                    // 检查标签是否已存在
                    if (isLabelExists(tenantId, labelEnum.getLabelName())) {
                        log.info("标签已存在，跳过创建: {}", labelEnum.getLabelName());
                        continue;
                    }
                    
                    // 只对标签6（CUSTOMER_SERVICE_RISK）进行字段验证
                    if (labelEnum == BuiltinLabelEnum.CUSTOMER_SERVICE_RISK) {
                        if (!labelFieldValidationService.validateRequiredFields(tenantId, labelEnum)) {
                            log.warn("标签创建字段验证失败，跳过创建: {}", labelEnum.getLabelName());
                            continue;
                        }
                    }
                    
                    createBuiltinLabel(tenantId, labelEnum);
                } catch (Exception e) {
                    log.error("创建内置标签失败: {}", labelEnum.getLabelName(), e);
                    throw new DeepSightException.LabelOperatorFailedException(ErrorCode.INTERNAL_ERROR,
                        "创建内置标签失败: " + labelEnum.getLabelName());
                }
            }
            
            // 3. 创建动态标签4-5
            createDynamicLabels(tenantId);
            
            log.info("租户 {} 内置标签创建完成", tenantId);
        } catch (Exception e) {
            log.error("租户 {} 内置标签创建失败", tenantId, e);
            throw new DeepSightException.LabelOperatorFailedException(ErrorCode.INTERNAL_ERROR,
                "租户内置标签创建失败: " + tenantId);
        }
    }

    @Override
    public void triggerBuiltinLabelCalculation() {
        log.info("开始触发内置标签计算");

        try {
            // 1. 获取所有租户列表
            List<TenantInfo> tenantInfos = tenantInfoService.getAllTenantInfo();
            if (CollectionUtils.isEmpty(tenantInfos)) {
                log.info("没有找到租户信息，跳过内置标签计算");
                return;
            }

            // 2. 遍历每个租户
            for (TenantInfo tenantInfo : tenantInfos) {
                ensureAndProcessBuiltinLabelsByTenant(tenantInfo.getTenantid());
            }

        } catch (Exception e) {
            log.error("内置标签计算过程中发生异常", e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "内置标签计算过程中发生异常");
        }

        log.info("内置标签计算完成");
    }

    public void ensureAndProcessBuiltinLabelsByTenant(String tenantId) {
        log.info("开始处理租户 {} 的内置标签计算", tenantId);

        try {
            // 2.1 首先确保内置标签都存在，如果不存在则创建
            ensureBuiltinLabelsExist(tenantId);

            // 2.2 然后执行内置标签计算
            processBuiltinLabelsForTenant(tenantId);
        } catch (Exception e) {
            log.error("租户 {} 内置标签计算失败", tenantId, e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "租户内置标签计算失败");
        }
    }

    @Override
    public boolean isLabelExists(String tenantId, String labelName) {
        LabelCriteria criteria = new LabelCriteria();
        LabelCriteria.Criteria condition = criteria.createCriteria();
        condition.andTenantIdEqualTo(tenantId)
                .andLabelNameEqualTo(labelName)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        
        List<LabelWithBLOBs> labels = labelMapper.selectByExampleWithBLOBs(criteria);
        return CollectionUtils.isNotEmpty(labels);
    }

    /**
     * 创建动态标签4-5
     * @param tenantId 租户ID
     * <AUTHOR>
     */
    private void createDynamicLabels(String tenantId) {
        try {
            // 创建标签4：robotName + INTENTION_LABEL_SUFFIX
            createRobotIntentionLabels(tenantId);
            
            // 创建标签5：tagExtractInfo的key
            createTagExtractLabels(tenantId);
        } catch (Exception e) {
            log.error("创建动态标签失败，租户: {}", tenantId, e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "创建动态标签失败");
        }
    }
    
    /**
     * 创建机器人意向标签（标签4）
     * @param tenantId 租户ID
     * <AUTHOR>
     */
    private void createRobotIntentionLabels(String tenantId) {
        try {
            // 获取所有机器人名称
            List<String> robotNames = dynamicCatalogService.getRobotNames(tenantId);
            
            for (String robotName : robotNames) {
                String labelName = robotName + SqlConstants.StringConstants.INTENTION_LABEL_SUFFIX;
                
                // 检查标签是否已存在（在意向标签二级目录下）
                if (isLabelExistsInIntentionCatalog(tenantId, labelName)) {
                    log.info("机器人意向标签已存在，跳过创建: {}, robotName: {}", labelName, robotName);
                    continue;
                }
                
                // 进行字段验证（带上robotName维度）
                if (!labelFieldValidationService.validateRequiredFields(tenantId, BuiltinLabelEnum.ROBOT_INTENTION_TAG, robotName)) {
                    log.warn("机器人意向标签创建字段验证失败，跳过创建: {}, robotName: {}", labelName, robotName);
                    continue;
                }
                
                // 创建标签（在意向标签二级目录下）
                createDynamicBuiltinLabelInIntentionCatalog(tenantId, labelName, BuiltinLabelEnum.ROBOT_INTENTION_TAG);
                log.info("成功创建机器人意向标签: {}, robotName: {}", labelName, robotName);
            }
        } catch (Exception e) {
            log.error("创建机器人意向标签失败，租户: {}", tenantId, e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "创建机器人意向标签失败");
        }
    }
    
    /**
     * 创建标签提取信息标签（标签5）
     * @param tenantId 租户ID
     * <AUTHOR>
     */
    private void createTagExtractLabels(String tenantId) {
        try {
            // 获取robotName与tagExtractKeys的映射关系
            Map<String, List<String>> robotNameToKeysMap = dynamicCatalogService.getTagExtractKeysWithRobotName(tenantId);
            
            if (robotNameToKeysMap.isEmpty()) {
                log.info("租户 {} 没有找到tagExtractInfo与robotName的映射数据，跳过标签5创建", tenantId);
                return;
            }
            
            // 为每个机器人名称下分别创建对应的标签5
            for (Map.Entry<String, List<String>> entry : robotNameToKeysMap.entrySet()) {
                String robotName = entry.getKey();
                List<String> tagExtractKeys = entry.getValue();
                
                for (String key : tagExtractKeys) {
                    // 检查标签是否已存在（在对话挖掘目录下的机器人子目录中）标签名称就是key值
                    if (isLabelExistsInConversationMiningCatalog(tenantId, key, robotName)) {
                        log.info("标签提取信息标签已存在，跳过创建: {}, robotName: {}", key, robotName);
                        continue;
                    }
                    
                    // 创建标签（在对话挖掘目录下的机器人子目录中）
                    createDynamicBuiltinLabelInConversationMiningCatalog(tenantId, key, BuiltinLabelEnum.LLM_TAG_KEY, robotName);
                    log.info("成功创建标签提取信息标签: {}, robotName: {}", key, robotName);
                }
            }
        } catch (Exception e) {
            log.error("创建标签提取信息标签失败，租户: {}", tenantId, e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "创建标签提取信息标签失败");
        }
    }
    
    /**
     * 创建单个内置标签
     * 复用现有的LabelService.createLabel()方法，避免重复实现
     */
    private void createBuiltinLabel(String tenantId, BuiltinLabelEnum labelEnum) {
        String labelName = labelEnum.getLabelName();

        // 构建标签规则
        LabelRule labelRule = buildLabelRule(tenantId, labelEnum);

        // 获取或创建标签目录
        LabelCatalog catalog = getOrCreateLabelCatalog(tenantId, labelEnum);

        // 构建NewLabelRequest，复用现有的标签创建逻辑
        NewLabelRequest request = buildNewLabelRequest(labelEnum, catalog.getId(), labelRule);

        // 临时设置系统用户上下文（因为内置标签是系统创建的）
        DeepSightWebContext originalContext = WebContextHolder.getDeepSightWebContext();

        try {
            // 设置系统上下文
            WebContextHolder.setDeepSightWebContext(createSystemWebContext(tenantId));

            // 复用现有的标签创建逻辑
            labelService.createLabel(request);

            log.info("内置标签创建成功: tenantId={}, labelName={}", tenantId, labelName);
        } finally {
            // 恢复原始上下文
            if (originalContext != null) {
                WebContextHolder.setDeepSightWebContext(originalContext);
            } else {
                WebContextHolder.clean();
            }
        }
    }

    /**
     * 获取内置标签目录
     * 注意：由于内置标签的目录在 ensureBuiltinLabelCatalogs 中已经确保创建，
     * 这里的目录应该总是存在的，如果不存在说明系统有问题
     */
    private LabelCatalog getOrCreateLabelCatalog(String tenantId, BuiltinLabelEnum labelEnum) {
        BuiltinLabelCatalogEnum catalogEnum = labelEnum.getCatalogEnum();
        String catalogName = catalogEnum.getCatalogName();

        try {
            // 直接获取指定目录，内置标签的目录应该总是存在
            LabelCatalog catalog = labelCatalogService.checkLabelCatalog(tenantId, catalogName);
            log.debug("找到内置标签目录: {} (ID: {})", catalogName, catalog.getId());
            return catalog;
        } catch (DeepSightException.CatalogOperatorFailedException e) {
            // 内置标签目录不存在是异常情况，说明系统初始化有问题
            log.error("内置标签目录不存在，内置标签目录: {} (租户: {})", catalogEnum.getFullPath(), tenantId);
            throw new DeepSightException.CatalogOperatorFailedException(ErrorCode.NOT_FOUND, String.format(
                    "内置标签目录不存在: %s，请检查租户初始化是否正常 (租户: %s)", catalogEnum.getFullPath(), tenantId));
        }
    }

    /**
     * 构建标签规则
     */
    private LabelRule buildLabelRule(String tenantId, BuiltinLabelEnum labelEnum) {
        // 获取 mock_user 表的 dataTableId
        Long mockUserDataTableId = getMockUserDataTableId(tenantId);
        String mockUserTableName = TenantUtils.generateMockUserTableName(tenantId);
    
        // 构建标签规则（使用 mock_user 表的 dataTableId）
        LabelRule labelRule;
    
        switch (labelEnum) {
            case CONNECT_WILLINGNESS:
                labelRule = BuiltinLabelRuleBuilder.buildConnectWillingnessRule(
                    mockUserTableName, mockUserDataTableId);
                break;
            case COLD_CALL_ACCEPTANCE:
                labelRule = BuiltinLabelRuleBuilder.buildColdCallAcceptanceRule(
                    mockUserTableName, mockUserDataTableId);
                break;
            case AVG_CONVERSATION_ROUNDS:
                labelRule = BuiltinLabelRuleBuilder.buildAvgConversationRoundsRule(
                    mockUserTableName, mockUserDataTableId);
                break;
            default:
                // 其他标签暂时返回空规则
                labelRule = new LabelRule();
                labelRule.setLabelValueRules(new ArrayList<>());
        }
    
        return labelRule;
    }

    /**
     * 获取用户表的 dataTableId
     * @param tenantId 租户ID
     * @return datatable_info 表中的 ID
     */
    private Long getMockUserDataTableId(String tenantId) {
        String mockUserTableName = TenantUtils.generateMockUserTableName(tenantId);
        TableFieldMetaInfoCriteria tableFieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
        TableFieldMetaInfoCriteria.Criteria criteria = tableFieldMetaInfoCriteria.createCriteria();
        criteria.andTableEnNameEqualTo(mockUserTableName)
                .andIsVisableEqualTo(true);

        List<TableFieldMetaInfo> dataTableInfos = tableFieldMetaInfoMapper.selectByExample(tableFieldMetaInfoCriteria);
        if (CollectionUtils.isEmpty(dataTableInfos)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND,
                    "用户表不存在: " + mockUserTableName);
        }
        return dataTableInfos.get(0).getId();
    }

    /**
     * 确保内置标签所需的目录存在
     *
     * @param tenantId 租户ID
     */
    private void ensureBuiltinLabelCatalogs(String tenantId) {
        log.info("开始检查租户 {} 的内置标签目录", tenantId);

        // 1. 先创建内置标签专用的一级目录（默认目录已在租户初始化时创建）
        for (BuiltinLabelCatalogEnum primaryCatalog : BuiltinLabelCatalogEnum.getBuiltinPrimaryCatalogs()) {
            try {
                ensureCatalogExists(tenantId, primaryCatalog.getCatalogName(),
                    BuiltinLabelConstants.CatalogHierarchy.ROOT_CATALOG_ID);
                log.debug("内置标签一级目录检查完成: {}", primaryCatalog.getCatalogName());
            } catch (Exception e) {
                log.warn("内置标签一级目录检查失败: {}", primaryCatalog.getCatalogName(), e);
                throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR,
                    "内置标签一级目录检查失败: " + primaryCatalog.getCatalogName());
            }
        }

        // 2. 再创建所有二级目录
        for (BuiltinLabelCatalogEnum secondaryCatalog : BuiltinLabelCatalogEnum.getSecondaryCatalogs()) {
            try {
                // 先找到父目录
                LabelCatalog parentCatalog = labelCatalogService.checkLabelCatalog(
                    tenantId, secondaryCatalog.getParent().getCatalogName());
                ensureCatalogExists(tenantId, secondaryCatalog.getCatalogName(), parentCatalog.getId());
                log.debug("内置二级目录检查完成: {}", secondaryCatalog.getFullPath());
            } catch (Exception e) {
                log.warn("内置二级目录检查失败: {}", secondaryCatalog.getFullPath(), e);
                throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR,
                    "内置二级目录检查失败: " + secondaryCatalog.getFullPath());
            }
        }

        log.info("租户 {} 内置标签目录检查完成", tenantId);
    }

    /**
     * 确保指定目录存在，如果不存在则创建
     */
    private void ensureCatalogExists(String tenantId, String catalogName, Long parentId) {
        try {
            // 检查目录是否已存在
            if (Objects.equals(parentId, BuiltinLabelConstants.CatalogHierarchy.ROOT_CATALOG_ID)) {
                // 一级目录，直接按名称查找
                labelCatalogService.checkLabelCatalog(tenantId, catalogName);
                log.debug("目录已存在: {}", catalogName);
            } else {
                // 二级目录，需要检查是否在指定父目录下存在
                // 这里简化处理，如果checkLabelCatalog找到同名目录就认为存在
                labelCatalogService.checkLabelCatalog(tenantId, catalogName);
                log.debug("目录已存在: {}", catalogName);
            }
        } catch (DeepSightException.CatalogOperatorFailedException e) {
            // 目录不存在，创建新目录
            log.info("创建新目录: {} (父目录ID: {})", catalogName, parentId);
            try {
                NewCatalogRequest request = new NewCatalogRequest();
                request.setCatalogName(catalogName);
                request.setParentId(parentId != null ? parentId : BuiltinLabelConstants.CatalogHierarchy.ROOT_CATALOG_ID);
                labelCatalogService.createLabelCatalog(request);
                log.info("目录创建成功: {}", catalogName);
            } catch (Exception createException) {
                log.error("创建目录失败: {}", catalogName, createException);
                throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR,
                    "创建目录失败: " + catalogName);
            }
        }
    }

    /**
     * 构建NewLabelRequest，用于复用现有的标签创建逻辑
     */
    private NewLabelRequest buildNewLabelRequest(BuiltinLabelEnum labelEnum, Long catalogId, LabelRule labelRule) {
        NewLabelRequest request = new NewLabelRequest();

        // 基本信息
        request.setLabelName(labelEnum.getLabelName());
        request.setCatalogId(catalogId);
        request.setLabelRule(labelRule);

        // 内置标签的固定配置
        request.setUpdateMod(UpdateModEnum.REPLACE);           // 替换模式
        request.setLabelValueSaveMod(LabelValueSaveModEnum.SINGLE); // 单值模式
        request.setTriggerMod(TriggerModeEnum.MANUAL);         // 手动触发
        request.setTriggerFrequency(null);                     // 手动触发不需要频率
        request.setTriggerFrequencyValue(null);                // 手动触发不需要频率值
        request.setIsBuiltin(true);                            // 标记为内置标签

        return request;
    }

    /**
     * 检查标签是否在意向标签目录下存在
     * @param tenantId 租户ID
     * @param labelName 标签名称
     * @return 是否存在
     * <AUTHOR>
     */
    private boolean isLabelExistsInIntentionCatalog(String tenantId, String labelName) {
        try {
            // 获取意向标签目录
            LabelCatalog intentionCatalog = labelCatalogService.checkLabelCatalog(tenantId, BuiltinLabelCatalogEnum.INTENTION_TAG.getCatalogName());
            
            // 查询该目录下是否存在指定标签
            LabelCriteria criteria = new LabelCriteria();
            LabelCriteria.Criteria condition = criteria.createCriteria();
            condition.andTenantIdEqualTo(tenantId)
                    .andLabelNameEqualTo(labelName)
                    .andCatalogIdEqualTo(intentionCatalog.getId())
                    .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
            
            List<LabelWithBLOBs> labels = labelMapper.selectByExampleWithBLOBs(criteria);
            return CollectionUtils.isNotEmpty(labels);
        } catch (Exception e) {
            log.error("检查意向标签目录下标签存在性失败: labelName={}", labelName, e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "检查意向标签目录下标签存在性失败");
        }
    }
    
    /**
     * 检查标签是否在对话挖掘目录下的机器人子目录中存在
     * @param tenantId 租户ID
     * @param labelName 标签名称
     * @param robotName 机器人名称
     * @return 是否存在
     * <AUTHOR>
     */
    private boolean isLabelExistsInConversationMiningCatalog(String tenantId, String labelName, String robotName) {
        try {
            // 获取对话挖掘目录下的机器人子目录
            LabelCatalog robotCatalog = getConversationMiningRobotCatalog(tenantId, robotName);
            if (robotCatalog == null) {
                return false;
            }
            
            // 查询该目录下是否存在指定标签
            LabelCriteria criteria = new LabelCriteria();
            LabelCriteria.Criteria condition = criteria.createCriteria();
            condition.andTenantIdEqualTo(tenantId)
                    .andLabelNameEqualTo(labelName)
                    .andCatalogIdEqualTo(robotCatalog.getId())
                    .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
            
            List<LabelWithBLOBs> labels = labelMapper.selectByExampleWithBLOBs(criteria);
            return CollectionUtils.isNotEmpty(labels);
        } catch (Exception e) {
            log.error("检查对话挖掘目录下机器人子目录标签存在性失败: labelName={}, robotName={}", labelName, robotName, e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "检查对话挖掘目录下机器人子目录标签存在性失败");
        }
    }
    
    /**
     * 在意向标签目录下创建动态内置标签
     * @param tenantId 租户ID
     * @param labelName 标签名称
     * @param labelEnum 标签枚举
     * <AUTHOR>
     */
    private void createDynamicBuiltinLabelInIntentionCatalog(String tenantId, String labelName, BuiltinLabelEnum labelEnum) {
        try {
            // 获取意向标签目录
            LabelCatalog intentionCatalog = labelCatalogService.checkLabelCatalog(tenantId, BuiltinLabelCatalogEnum.INTENTION_TAG.getCatalogName());
            
            // 构建标签规则
            LabelRule labelRule = buildLabelRule(tenantId, labelEnum);

            // 构建NewLabelRequest
            NewLabelRequest request = buildNewLabelRequest(labelEnum, intentionCatalog.getId(), labelRule);
            request.setLabelName(labelName); // 使用动态标签名称

            // 临时设置系统用户上下文
            DeepSightWebContext originalContext = WebContextHolder.getDeepSightWebContext();

            try {
                // 设置系统上下文
                WebContextHolder.setDeepSightWebContext(createSystemWebContext(tenantId));

                // 动态标签设置为动态标签
                request.setIsDynamic(true);

                // 复用现有的标签创建逻辑
                labelService.createLabel(request);

                log.info("意向标签目录下动态内置标签创建成功: tenantId={}, labelName={}", tenantId, labelName);
            } finally {
                // 恢复原始上下文
                if (originalContext != null) {
                    WebContextHolder.setDeepSightWebContext(originalContext);
                } else {
                    WebContextHolder.clean();
                }
            }
        } catch (Exception e) {
            log.error("在意向标签目录下创建动态内置标签失败: labelName={}", labelName, e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "在意向标签目录下创建动态内置标签失败");
        }
    }
    
    /**
     * 在对话挖掘目录下的机器人子目录中创建动态内置标签
     * @param tenantId 租户ID
     * @param labelName 标签名称
     * @param labelEnum 标签枚举
     * @param robotName 机器人名称
     * <AUTHOR>
     */
    private void createDynamicBuiltinLabelInConversationMiningCatalog(String tenantId, String labelName, BuiltinLabelEnum labelEnum, String robotName) {
        try {
            // 获取对话挖掘目录下的机器人子目录
            LabelCatalog robotCatalog = getConversationMiningRobotCatalog(tenantId, robotName);
            if (robotCatalog == null) {
                log.error("对话挖掘目录下机器人子目录不存在，无法创建标签: robotName={}, labelName={}", robotName, labelName);
                return;
            }
            
            // 构建标签规则
            LabelRule labelRule = buildLabelRule(tenantId, labelEnum);

            // 构建NewLabelRequest
            NewLabelRequest request = buildNewLabelRequest(labelEnum, robotCatalog.getId(), labelRule);
            request.setLabelName(labelName); // 使用动态标签名称

            // 临时设置系统用户上下文
            DeepSightWebContext originalContext = WebContextHolder.getDeepSightWebContext();

            try {
                // 设置系统上下文
                WebContextHolder.setDeepSightWebContext(createSystemWebContext(tenantId));

                // 动态标签设置为动态标签
                request.setIsDynamic(true);

                // 复用现有的标签创建逻辑
                labelService.createLabel(request);

                log.info("对话挖掘目录下机器人子目录动态内置标签创建成功: tenantId={}, labelName={}, robotName={}", tenantId, labelName, robotName);
            } finally {
                // 恢复原始上下文
                if (originalContext != null) {
                    WebContextHolder.setDeepSightWebContext(originalContext);
                } else {
                    WebContextHolder.clean();
                }
            }
        } catch (Exception e) {
            log.error("在对话挖掘目录下机器人子目录创建动态内置标签失败: labelName={}, robotName={}", labelName, robotName, e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "在对话挖掘目录下机器人子目录创建动态内置标签失败");
        }
    }

    
    /**
     * 获取对话挖掘目录下的机器人子目录
     * @param tenantId 租户ID
     * @param robotName 机器人名称
     * @return 对话挖掘目录下的机器人子目录
     * <AUTHOR>
     */
    private LabelCatalog getConversationMiningRobotCatalog(String tenantId, String robotName) {
        try {
            // 先获取对话挖掘二级目录
            LabelCatalog conversationMiningCatalog = labelCatalogService.checkLabelCatalog(tenantId, BuiltinLabelCatalogEnum.CONVERSATION_MINING.getCatalogName());
            
            // 再查找具体的机器人三级目录
            LabelCatalogCriteria criteria = new LabelCatalogCriteria();
            LabelCatalogCriteria.Criteria condition = criteria.createCriteria();
            condition.andTenantIdEqualTo(tenantId)
                    .andParentIdEqualTo(conversationMiningCatalog.getId())
                    .andCatalogNameEqualTo(robotName)
                    .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
            
            List<LabelCatalog> catalogs = labelCatalogMapper.selectByExample(criteria);
            if (CollectionUtils.isNotEmpty(catalogs)) {
                return catalogs.get(0);
            }
            
            log.warn("对话挖掘目录下机器人子目录不存在: robotName={}", robotName);
            return null;
        } catch (Exception e) {
            log.error("获取对话挖掘目录下机器人子目录失败: robotName={}", robotName, e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "获取对话挖掘目录下机器人子目录失败");
        }
    }
    
    /**
     * 创建系统用户Web上下文，用于内置标签创建
     */
    private DeepSightWebContext createSystemWebContext(String tenantId) {
        UserAuthInfo authInfo = new UserAuthInfo();
        authInfo.setUserId(BuiltinLabelConstants.SystemUser.SYSTEM_USER_ID);
        authInfo.setUserName(Constants.SYSTEM_DEFAULT_USER_ID);
        authInfo.setTenantId(Long.parseLong(tenantId));

        return new DeepSightWebContext(authInfo);
    }

    /**
     * 确保租户的内置标签都存在，如果不存在则创建
     * 复用现有的 createBuiltinLabelsForTenant 逻辑
     * @param tenantId 租户ID
     * <AUTHOR>
     */
    private void ensureBuiltinLabelsExist(String tenantId) {
        log.info("检查租户 {} 的内置标签是否完整", tenantId);

        try {
            // 检查静态内置标签（标签1-3、6）是否都存在
            boolean needCreateStaticLabels = false;
            for (BuiltinLabelEnum labelEnum : BuiltinLabelEnum.values()) {
                // 跳过动态标签4-5，它们会在createBuiltinLabelsForTenant中单独处理
                if (labelEnum == BuiltinLabelEnum.ROBOT_INTENTION_TAG || labelEnum == BuiltinLabelEnum.LLM_TAG_KEY) {
                    continue;
                }

                if (!isLabelExists(tenantId, labelEnum.getLabelName())) {
                    log.info("发现缺失的内置标签: {} (租户: {})", labelEnum.getLabelName(), tenantId);
                    needCreateStaticLabels = true;
                    break;
                }
            }

            // 如果有缺失的标签，调用完整的创建流程
            // 这个方法会检查每个标签是否存在，只创建不存在的标签
            if (needCreateStaticLabels) {
                log.info("租户 {} 存在缺失的内置标签，开始补充创建", tenantId);
                createBuiltinLabelsForTenant(tenantId);
                log.info("租户 {} 内置标签补充创建完成", tenantId);
            } else {
                log.info("租户 {} 的内置标签都已存在", tenantId);
            }

        } catch (Exception e) {
            log.error("检查或创建租户 {} 的内置标签失败", tenantId, e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "检查或创建内置标签失败");
        }
    }

    /**
     * 处理单个租户的内置标签计算
     * @param tenantId 租户ID
     * <AUTHOR>
     */
    private void processBuiltinLabelsForTenant(String tenantId) {
        log.info("开始处理租户 {} 的内置标签", tenantId);

        try {
            // 查询该租户的所有内置标签
            List<LabelWithBLOBs> builtinLabels = getBuiltinLabelsForTenant(tenantId);
            if (CollectionUtils.isEmpty(builtinLabels)) {
                log.info("租户 {} 没有内置标签，跳过处理", tenantId);
                return;
            }

            log.info("租户 {} 找到 {} 个内置标签", tenantId, builtinLabels.size());

            // 遍历每个内置标签，根据类型执行不同的计算逻辑
            for (LabelWithBLOBs label : builtinLabels) {
                try {
                    processBuiltinLabel(tenantId, label);
                } catch (Exception e) {
                    log.error("处理租户 {} 的内置标签 {} 失败", tenantId, label.getLabelName(), e);
                    // 继续处理下一个标签
                }
            }

        } catch (Exception e) {
            log.error("处理租户 {} 的内置标签时发生异常", tenantId, e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "处理租户内置标签时发生异常");
        }
    }

    /**
     * 获取租户的所有内置标签
     * 直接查询 is_builtin=1 的标签，避免全表扫描后过滤
     * @param tenantId 租户ID
     * @return 内置标签列表
     * <AUTHOR>
     */
    private List<LabelWithBLOBs> getBuiltinLabelsForTenant(String tenantId) {
        LabelCriteria criteria = new LabelCriteria();
        LabelCriteria.Criteria condition = criteria.createCriteria();
        condition.andTenantIdEqualTo(tenantId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean())
                .andIsBuiltinEqualTo(true); // 直接查询内置标签

        return labelMapper.selectByExampleWithBLOBs(criteria);
    }



    /**
     * 处理单个内置标签的计算
     * @param tenantId 租户ID
     * @param label 标签信息
     * <AUTHOR>
     */
    @Override
    public void processBuiltinLabel(String tenantId, LabelWithBLOBs label) {
        String labelName = label.getLabelName();
        log.info("开始处理内置标签: {} (租户: {})", labelName, tenantId);

        try {
            // 根据标签类型选择不同的处理方式
            BuiltinLabelEnum labelEnum = BuiltinLabelEnum.getBuiltinLabelEnum(labelName);

            if (labelEnum != null) {
                // 静态内置标签（标签1-3、6）
                builtinLabelCalculateService.processStaticBuiltinLabel(tenantId, label, labelEnum);
            } else if (labelName.endsWith(SqlConstants.StringConstants.INTENTION_LABEL_SUFFIX)) {
                // 动态标签4：机器人名称_意向标签
                builtinLabelCalculateService.processDynamicIntentionLabel(tenantId, label);
            } else {
                // 动态标签5：大模型标签key 或其他动态标签
                builtinLabelCalculateService.processDynamicLLMLabel(tenantId, label);
            }

            log.info("内置标签处理完成: {} (租户: {})", labelName, tenantId);

        } catch (Exception e) {
            log.error("处理内置标签失败: {} (租户: {})", labelName, tenantId, e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "处理内置标签失败");
        }
    }



}
