package com.baidu.keyue.deepsight.service.tenant;

import cn.hutool.core.lang.Assert;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.database.doris.DorisConfiguration;
import com.baidu.keyue.deepsight.database.doris.DorisTableTemplateConfig;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.ExecModEnum;
import com.baidu.keyue.deepsight.enums.FilterTypeEnum;
import com.baidu.keyue.deepsight.enums.FuncEnum;
import com.baidu.keyue.deepsight.enums.LabelValueSaveModEnum;
import com.baidu.keyue.deepsight.enums.LogicEnum;
import com.baidu.keyue.deepsight.enums.RuleTypeEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TaskTypeEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.enums.UpdateModEnum;
import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.label.LabelRule;
import com.baidu.keyue.deepsight.models.label.LabelValueRule;
import com.baidu.keyue.deepsight.models.label.NewLabelRequest;
import com.baidu.keyue.deepsight.models.rules.dto.RuleFilter;
import com.baidu.keyue.deepsight.models.rules.dto.RuleGroup;
import com.baidu.keyue.deepsight.models.rules.dto.RuleNode;
import com.baidu.keyue.deepsight.models.rules.response.FilterEnumInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalog;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelField;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.LabelCatalogMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.LabelFieldMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.LabelMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TaskInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TenantInfoMapper;
import com.baidu.keyue.deepsight.service.builtin.BuiltinLabelService;
import com.baidu.keyue.deepsight.service.catalog.LabelCatalogService;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.datamanage.impl.TableRecordCommonService;
import com.baidu.keyue.deepsight.service.rules.RuleParseService;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @ClassName TenantV3UpgradeHandler
 * @Description v1.1.2租户升级
 * <AUTHOR>
 * @Date 2025/5/15 3:18 PM
 */
@Slf4j
@Service
@Order(4)
public class TenantV3UpgradeHandler extends TenantUpgradeHandler {

    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private TenantInfoMapper tenantInfoMapper;

    @Resource
    private DataTableManageService dataTableManageService;

    @Resource
    private DataTableInfoMapper dataTableInfoMapper;

    @Resource
    private LabelCatalogService labelCatalogService;

    @Resource
    private LabelFieldMapper labelFieldMapper;

    @Resource
    private DorisConfiguration dorisConfiguration;

    @Resource
    private TaskInfoMapper taskInfoMapper;

    @Resource
    private LabelMapper labelMapper;

    @Resource
    private RuleParseService ruleParseService;
    @Resource
    private RedissonClient redisson;

    @Resource
    private DorisTableTemplateConfig dorisTableTemplateConfig;

    @Resource
    private TableRecordCommonService tableRecordCommonService;

    @Resource
    private DorisService dorisService;

    @Resource
    private LabelCatalogMapper labelCatalogMapper;
    
    @Resource
    @Lazy
    private TenantInfoService tenantInfoService;
    
    @Resource
    private TenantV4UpgradeHandler tenantV4UpgradeHandler;

    @Resource
    private BuiltinLabelService builtinLabelService;

    /**
     * 外呼对话内容新增字段
     */
    private final List<String> aiobRecordField = List.of("start", "queryId");
    /**
     * 外呼对话记录新增字段
     */
    private final List<String> aiobSessionField = List.of("sipCode", "botVersionId", "customTagList");

    private final Map<String, TableFieldMetaInfo> aiobRecordFieldMap = new HashMap<>();
    private final Map<String, TableFieldMetaInfo> aiobSessionFieldMap = new HashMap<>();

    /**
     * 预置标签
     * gender,age_group,occupation,city_level,education_level
     * 用户性别,用户年龄段,职业类型,常驻城市线级,教育水平
     */
    private final Set<String> presetLabelNames =
            Set.of("gender", "age_group", "occupation", "city_level", "education_level");


    @PostConstruct
    public void init() throws IOException {
        setVersion(4);
        initDorisSql("upgrade/doris/v1.1.2.sql");
        dorisTableTemplateConfig.getAiobRecordFieldsInfo().forEach(field -> aiobRecordFieldMap.put(field.getEnField(), field));
        dorisTableTemplateConfig.getAiobSessionFieldsInfo().forEach(field -> aiobSessionFieldMap.put(field.getEnField(), field));
        addNextHandler(tenantV4UpgradeHandler);
    }

    public TenantV3UpgradeHandler(DorisService dorisService) {
        super(dorisService);
    }

    @Override
    protected boolean needUpdateTenant(TenantInfo tenant) {
        // 再次检查版本
        Integer version = tenantInfoService.queryTenantInfo(tenant.getTenantid()).getVersion();
        return Objects.equals(version, 3);
    }

    @Override
    protected void executeUpgrade(TenantDTO tenant) {
        transactionTemplate.execute(status -> {
            try {
                String tenantId = tenant.getTenantId();
                log.info("tenant v1.1.2 upgrade start.tenantId is {}.", tenantId);
                // 新旧租户-隐藏外呼内容record表
                updateAiobRecordTableVisible(tenantId);
                log.info("v1.1.2 Upgrade close updateAiobRecordTableVisible over");
                // 新旧租户：新增Doris表
                execDorisTableSql(tenantId);
                log.info("v1.1.2 Upgrade close execDorisTableSql over");
                // 新旧租户-预置标签：
                presetUserLabel(tenantId);
                log.info("v1.1.2 Upgrade close presetUserLabel over");
                // 新旧租户-内置标签：
                createBuiltinLabels(tenantId);
                log.info("v1.1.2 Upgrade close createBuiltinLabels over");
                // 老租户升级历史数据
                if (!tenant.getIsInit()) {
                    // 历史表操作
                    oldTableUpgrade(tenantId);
                    log.info("v1.1.2 Upgrade oldTableUpgrade over");
                }
                // 更新责任链租户信息
                TenantInfo info = tenant.getTenantInfo();
                info.setVersion(version);
                // 更新数据库租户版本
                TenantInfo tenantInfo = new TenantInfo();
                tenantInfo.setVersion(version);
                tenantInfo.setId(info.getId());
                tenantInfoMapper.updateByPrimaryKeySelective(tenantInfo);
            } catch (Exception e) {
                log.error("tenant v1.1.2 Upgrade error!, tenantId:{} ", tenant.getTenantId(), e);
                status.setRollbackOnly();
                throw new DeepSightException.InitTenantFailedException(ErrorCode.INTERNAL_ERROR, "租户v1.1.2版本升级失败");
            }
            return null;
        });
    }

    /**
     * 老租户表升级
     * 只针对老租户的升级
     * Doris、mysql字段信息修改、添加、删除
     *
     * @param tenantId 租户id
     */
    public void oldTableUpgrade(String tenantId) {
        // 执行除建表外的其他语句：历史Doris表新增字段
        for (String sql : this.sqlList) {
            if (sql.toUpperCase().contains("CREATE TABLE")) {
                continue;
            }
            // 将^&符号作为租户ID替代符号
            String execSql = sql.replaceAll("\\^&", tenantId) + ";";
            dorisService.operationSchema(execSql);
        }
        // 外呼对话记录表增加字段元信息
        String aiobSessionTableName = TenantUtils.generateAiobSessionTableName(tenantId);
        tableRecordCommonService.saveUpgradeFieldMetaInfo(aiobSessionTableName, aiobSessionField, aiobSessionFieldMap);
        // 外呼对话内容表增加字段元信息
        String aiobRecordTableName = TenantUtils.generateAiobRecordTableName(tenantId);
        tableRecordCommonService.saveUpgradeFieldMetaInfo(aiobRecordTableName, aiobRecordField, aiobRecordFieldMap);
    }

    /**
     * 预置用户标签
     * 预置与基础标签目录下
     * gender,age_group,occupation,city_level,education_level
     * 用户性别,用户年龄段,职业类型,常驻城市线级,教育水平
     * 去除字段枚举值"未知"
     *
     * @param tenantId 租户ID
     */
    public void presetUserLabel(String tenantId) {
        // 初始化标签目录，会检查是否存，不存在则创建
        labelCatalogService.initTenantCatalog(tenantId);
        // 查询基础标签目录
        LabelCatalog labelCatalog = labelCatalogService.checkLabelCatalog(tenantId, "基础标签");
        // 查询字段信息
        String tableName = TenantUtils.generateMockUserTableName(tenantId);
        DataTableInfo dataTableInfo = dataTableManageService.validDataTableByTableName(tableName, tenantId);
        List<VisibleFieldResponse> visibleFields = dataTableManageService.getVisibleFields(tableName, tenantId, false);
        Set<String> fields = new HashSet<>();
        visibleFields.stream()
                .filter(field -> {
                    if (fields.contains(field.getEnName())) {
                        return false;
                    }
                    fields.add(field.getEnName());
                    return presetLabelNames.contains(field.getEnName());
                })
                .forEach(field -> createLabel(tenantId, labelCatalog, field, dataTableInfo));
    }

    /**
     * 创建标签
     *
     * @param tenantId      租户ID
     * @param labelCatalog  标签目录
     * @param field         字段
     * @param dataTableInfo 表信息
     */
    @Transactional
    public void createLabel(String tenantId, LabelCatalog labelCatalog, VisibleFieldResponse field, DataTableInfo dataTableInfo) {
        String userId = Constants.SYSTEM_DEFAULT_USER_ID;
        NewLabelRequest request = buildNewLabelRequest(labelCatalog, field, dataTableInfo.getId());
        for (LabelValueRule labelValueRule : request.getLabelRule().getLabelValueRules()) {
            ruleParseService.checkRuleGroupTenantPermission(labelValueRule.getRuleGroup(), tenantId);
        }
        request.getLabelRule().checkLabelRule(ruleParseService);
        // 标签值租户全局唯一
        String key = StringUtils.join("createLabel", tenantId, request.getLabelName(), "-");
        RLock lock = redisson.getLock(key);
        if (!lock.tryLock()) {
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST);
        }
        try {
            // 校验目录是否存在
            labelCatalogService.getCatalogDetail(request.getCatalogId(), tenantId);
            // 检查标签名称是否重复
            if (isDupLabelName(tenantId, request.getCatalogId(), request.getLabelName())) {
                log.warn("{} 标签已存在:{}", tenantId, request.getLabelName());
                return;
            }
            // insert label
            LabelWithBLOBs label = buildLabel(tenantId, userId, request);
            // 标签计算任务
            Long taskId = createNewLabelCalTask(request.getTriggerMod());
            label.setTask(taskId);
            // 标签字段
            Long fieldId = createNewLabelField(tenantId, request.getLabelName());
            label.setField(fieldId);
            label.setDel(DelEnum.NOT_DELETED.getBoolean());
            label.setCreator(Constants.SYSTEM_DEFAULT_USER_ID);
            label.setModifier(Constants.SYSTEM_DEFAULT_USER_ID);
            Date now = new Date();
            label.setCreateTime(now);
            label.setUpdateTime(now);
            labelMapper.insert(label);
        } catch (DeepSightException.LabelOperatorFailedException |
                 DeepSightException.CatalogOperatorFailedException e) {
            log.error("tenant upgrade v1.1.2 LabelService.newLabel error : ", e);
            throw new DeepSightException.LabelOperatorFailedException(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            log.error("tenant upgrade v1.1.2 LabelService.newLabel INTERNAL_ERROR : ", e);
            throw new DeepSightException.LabelOperatorFailedException(ErrorCode.INTERNAL_ERROR, "标签创建失败");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public Long createNewLabelField(String tenantId, String labelName) {
        String userId = Constants.DEFAULT_USER_ID;
        Date now = new Date();
        LabelField labelField = new LabelField();
        labelField.setFieldType("array");
        labelField.setFieldDesc(labelName);
        labelField.setTableSpace(dorisConfiguration.getDb());
        labelField.setLabelTable(TenantUtils.generateMockUserTableName(tenantId));
        labelField.setDel(DelEnum.NOT_DELETED.getBoolean());
        labelField.setCreator(userId);
        labelField.setModifier(userId);
        labelField.setCreateTime(now);
        labelField.setUpdateTime(now);
        labelFieldMapper.insert(labelField);
        Long fieldId = labelField.getId();
        String fieldName = String.format(Constants.DORIS_LABEL_FIELD_TEM, fieldId);
        try {
            dorisService.operationSchema(String.format(
                    "ALTER TABLE %s.%s ADD COLUMN %s array<varchar(150)> NULL DEFAULT \"[]\"",
                    dorisConfiguration.getDb(), TenantUtils.generateMockUserTableName(tenantId), fieldName));
            dorisService.operationSchema(String.format(
                    "ALTER TABLE %s.%s ADD COLUMN %s array<varchar(150)> NULL DEFAULT \"[]\"",
                    dorisConfiguration.getDb(), TenantUtils.generateUserProfileTableName(tenantId), fieldName));
        } catch (Exception e) {
            log.error("LabelFieldService.newDorisField error : ", e);
        }
        return fieldId;
    }

    public Long createNewLabelCalTask(TriggerModeEnum triggerMod) {
        Date now = new Date();
        TaskInfo taskInfo = new TaskInfo();
        taskInfo.setTaskDesc("预置标签计算任务");
        taskInfo.setTaskType(TaskTypeEnum.LABEL_DWS_TASK.getCode());
        taskInfo.setTriggerMod(triggerMod.getCode());
        taskInfo.setDel(DelEnum.NOT_DELETED.getBoolean());
        taskInfo.setCreator(Constants.SYSTEM_DEFAULT_USER_ID);
        taskInfo.setModifier(Constants.SYSTEM_DEFAULT_USER_ID);
        taskInfo.setCreateTime(now);
        taskInfo.setUpdateTime(now);
        taskInfoMapper.insert(taskInfo);
        return taskInfo.getId();
    }

    @NotNull
    private static LabelWithBLOBs buildLabel(String tenantId, String userId, NewLabelRequest request) throws IOException {
        LabelWithBLOBs label = new LabelWithBLOBs();
        label.setUserId(userId);
        label.setTenantId(tenantId);
        label.setCatalogId(request.getCatalogId());
        label.setLabelName(request.getLabelName());
        label.setLabelValueUpdateMod(request.getUpdateMod().getCode());
        label.setLabelValueSaveMod(request.getLabelValueSaveMod().getCode());
        label.setTriggerMod(request.getTriggerMod().getCode());
        if (Objects.nonNull(request.getTriggerFrequency())) {
            label.setTriggerFrequency(request.getTriggerFrequency().getCode());
        }
        if (Objects.nonNull(request.getTriggerFrequencyValue())) {
            label.setTriggerFrequencyValue(JsonUtils.toJson(request.getTriggerFrequencyValue()));
        }
        label.setLabelRule(JsonUtils.toJson(request.getLabelRule()));
        label.setExecMod(ExecModEnum.RULE.getCode());
        label.setLabelCalStatus(TaskExecStatusEnum.PENDING.getCode());
        label.setDistribution(null);
        label.setRecalculate(false);
        return label;
    }

    /**
     * 构建创建标签请求
     *
     * @param labelCatalog 标签目录
     * @param field        字段
     * @param tableId      表ID
     * @return 创建标签请求
     */
    private NewLabelRequest buildNewLabelRequest(LabelCatalog labelCatalog, VisibleFieldResponse field, Long tableId) {
        NewLabelRequest request = new NewLabelRequest();
        request.setLabelName(field.getCnName());
        request.setCatalogId(labelCatalog.getId());
        request.setUpdateMod(UpdateModEnum.REPLACE);
        request.setLabelValueSaveMod(LabelValueSaveModEnum.SINGLE);
        request.setTriggerMod(TriggerModeEnum.MANUAL);
        LabelRule labelRule = new LabelRule();
        List<LabelValueRule> labelValueRules = new ArrayList<>();
        List<FilterEnumInfo> fieldEnums = field.getEnums();
        Assert.notEmpty(fieldEnums, () -> new DeepSightException.BusinessException(ErrorCode.BAD_REQUEST, field.getEnName() + "枚举配置为空"));
        Long fieldId = field.getId();
        for (FilterEnumInfo fieldEnum : field.getEnums()) {
            String fieldValue = fieldEnum.getKey();
            String labelValue = fieldEnum.getValue();
            String desc = fieldEnum.getDesc();
            // 根据产品需求，跳过"未知"枚举
            if (Objects.equals(labelValue, "未知")) {
                continue;
            }
            // 构建过滤规则
            RuleFilter ruleFilter = new RuleFilter();
            ruleFilter.setFieldId(fieldId);
            ruleFilter.setType(FilterTypeEnum.convertFrom(field.getFieldType()));
            ruleFilter.setFunction(FuncEnum.CONTAIN);
            ruleFilter.setParams(List.of(fieldValue));
            // 构建规则节点
            RuleNode ruleNode = new RuleNode();
            ruleNode.setType(RuleTypeEnum.USER);
            ruleNode.setDataTableId(tableId);
            ruleNode.setFilters(List.of(ruleFilter));
            // 构建规则嵌套分组
            RuleGroup ruleGroupDetail = new RuleGroup();
            ruleGroupDetail.setName(labelValue);
            ruleGroupDetail.setRelation(LogicEnum.AND);
            ruleGroupDetail.setRuleNodes(List.of(ruleNode));
            // 构建规则分组
            RuleGroup ruleGroup = new RuleGroup();
            ruleGroup.setRelation(LogicEnum.AND);
            ruleGroup.setRuleGroups(List.of(ruleGroupDetail));
            // 构建标签规则
            LabelValueRule labelValueRule = new LabelValueRule();
            labelValueRule.setLabelValue(labelValue);
            labelValueRule.setLabelValueDesc(desc);
            labelValueRule.setRuleGroup(ruleGroup);
            // 添加标签值规则组
            labelValueRules.add(labelValueRule);
        }
        labelRule.setLabelValueRules(labelValueRules);
        request.setLabelRule(labelRule);
        return request;
    }

    private boolean isDupLabelName(String tenantId, long catalogId, String labelName) {
        // 校验标签名是否重复
        LabelCriteria criteria = new LabelCriteria();
        criteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andCatalogIdEqualTo(catalogId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean())
                .andLabelNameEqualTo(labelName);
        return labelMapper.countByExample(criteria) > 0;
    }

    /**
     * 创建内置标签
     *
     * @param tenantId 租户ID
     */
    public void createBuiltinLabels(String tenantId) {
        try {
            log.info("开始为租户 {} 创建内置标签", tenantId);
            builtinLabelService.createBuiltinLabelsForTenant(tenantId);
            log.info("租户 {} 内置标签创建完成", tenantId);
        } catch (Exception e) {
            log.error("租户 {} 内置标签创建失败", tenantId, e);
            // 不抛出异常，避免影响租户升级流程
        }
    }

    /**
     * 更新外呼对话内容表可见属性为false
     *
     * @param tenantId 租户ID
     */
    public void updateAiobRecordTableVisible(String tenantId) {
        String tableName = TenantUtils.generateAiobRecordTableName(tenantId);
        DataTableInfo tableInfo = dataTableManageService.getTableDetailWithTableName(tableName);
        DataTableInfo update = new DataTableInfo();
        update.setId(tableInfo.getId());
        update.setIsVisable(false);
        dataTableInfoMapper.updateByPrimaryKeySelective(update);
    }
}
