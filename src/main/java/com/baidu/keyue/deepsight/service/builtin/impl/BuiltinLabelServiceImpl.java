package com.baidu.keyue.deepsight.service.builtin.impl;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.constants.BuiltinLabelConstants;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.base.request.DeepSightWebContext;
import com.baidu.keyue.deepsight.models.builtin.UserMetricTableInfo;
import com.baidu.keyue.deepsight.enums.BuiltinLabelCatalogEnum;
import com.baidu.keyue.deepsight.enums.BuiltinLabelEnum;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.enums.UpdateModEnum;
import com.baidu.keyue.deepsight.enums.LabelValueSaveModEnum;
import com.baidu.keyue.deepsight.models.label.LabelRule;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalog;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.LabelMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.builtin.BuiltinLabelRuleBuilder;
import com.baidu.keyue.deepsight.models.catalog.NewCatalogRequest;
import com.baidu.keyue.deepsight.service.builtin.BuiltinLabelService;
import com.baidu.keyue.deepsight.service.catalog.LabelCatalogService;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.service.label.LabelService;
import com.baidu.keyue.deepsight.models.label.NewLabelRequest;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 内置标签服务实现
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BuiltinLabelServiceImpl implements BuiltinLabelService {

    private final LabelCatalogService labelCatalogService;
    private final LabelMapper labelMapper;
    private final DorisService dorisService;
    private final LabelService labelService;
    private final TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @Override
    @Transactional
    public void createBuiltinLabelsForTenant(String tenantId) {
        log.info("开始为租户 {} 创建内置标签", tenantId);
        
        try {
            // 1. 确保内置标签所需的目录存在
            ensureBuiltinLabelCatalogs(tenantId);

            // 2. 遍历所有内置标签枚举
            for (BuiltinLabelEnum labelEnum : BuiltinLabelEnum.values()) {
                createBuiltinLabel(tenantId, labelEnum);
            }
            
            log.info("租户 {} 内置标签创建完成", tenantId);
        } catch (Exception e) {
            log.error("租户 {} 内置标签创建失败", tenantId, e);
            throw new DeepSightException.LabelOperatorFailedException(ErrorCode.INTERNAL_ERROR,
                "租户内置标签创建失败: " + tenantId);
        }
    }

    @Override
    public void triggerBuiltinLabelCalculation() {
        log.info("开始触发内置标签计算");
        
        // TODO: 实现内置标签计算逻辑
        // 1. 获取所有租户列表
        // 2. 查询所有内置标签
        // 3. 遍历每个内置标签，根据类型执行不同的计算逻辑
        //    - 标签1-3：复用现有标签计算逻辑
        //    - 标签4-6：执行自定义处理逻辑
        
        log.info("内置标签计算完成");
    }

    @Override
    public boolean isLabelExists(String tenantId, String labelName) {
        LabelCriteria criteria = new LabelCriteria();
        LabelCriteria.Criteria condition = criteria.createCriteria();
        condition.andTenantIdEqualTo(tenantId)
                .andLabelNameEqualTo(labelName)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        
        List<LabelWithBLOBs> labels = labelMapper.selectByExampleWithBLOBs(criteria);
        return CollectionUtils.isNotEmpty(labels);
    }

    /**
     * 创建单个内置标签
     * 复用现有的LabelService.createLabel()方法，避免重复实现
     */
    private void createBuiltinLabel(String tenantId, BuiltinLabelEnum labelEnum) {
        String labelName = labelEnum.getLabelName();

        // 检查标签是否已存在
        if (isLabelExists(tenantId, labelName)) {
            log.info("标签已存在，跳过创建: tenantId={}, labelName={}", tenantId, labelName);
            return;
        }

        // 构建标签规则
        LabelRule labelRule = buildLabelRule(tenantId, labelEnum);

        // 获取或创建标签目录
        LabelCatalog catalog = getOrCreateLabelCatalog(tenantId, labelEnum);

        // 构建NewLabelRequest，复用现有的标签创建逻辑
        NewLabelRequest request = buildNewLabelRequest(labelEnum, catalog.getId(), labelRule);

        // 临时设置系统用户上下文（因为内置标签是系统创建的）
        DeepSightWebContext originalContext = WebContextHolder.getDeepSightWebContext();

        try {
            // 设置系统上下文
            WebContextHolder.setDeepSightWebContext(createSystemWebContext(tenantId));

            // 复用现有的标签创建逻辑
            labelService.createLabel(request);

            log.info("内置标签创建成功: tenantId={}, labelName={}", tenantId, labelName);
        } finally {
            // 恢复原始上下文
            if (originalContext != null) {
                WebContextHolder.setDeepSightWebContext(originalContext);
            } else {
                WebContextHolder.clean();
            }
        }
    }

    /**
     * 获取内置标签目录
     * 注意：由于内置标签的目录在 ensureBuiltinLabelCatalogs 中已经确保创建，
     * 这里的目录应该总是存在的，如果不存在说明系统有问题
     */
    private LabelCatalog getOrCreateLabelCatalog(String tenantId, BuiltinLabelEnum labelEnum) {
        BuiltinLabelCatalogEnum catalogEnum = labelEnum.getCatalogEnum();
        String catalogName = catalogEnum.getCatalogName();

        try {
            // 直接获取指定目录，内置标签的目录应该总是存在
            LabelCatalog catalog = labelCatalogService.checkLabelCatalog(tenantId, catalogName);
            log.debug("找到内置标签目录: {} (ID: {})", catalogName, catalog.getId());
            return catalog;
        } catch (DeepSightException.CatalogOperatorFailedException e) {
            // 内置标签目录不存在是异常情况，说明系统初始化有问题
            log.error("内置标签目录不存在，内置标签目录: {} (租户: {})", catalogEnum.getFullPath(), tenantId);
            throw new DeepSightException.CatalogOperatorFailedException(ErrorCode.NOT_FOUND, String.format(
                    "内置标签目录不存在: %s，请检查租户初始化是否正常 (租户: %s)", catalogEnum.getFullPath(), tenantId));
        }
    }

    /**
     * 构建标签规则
     */
    private LabelRule buildLabelRule(String tenantId, BuiltinLabelEnum labelEnum) {
        // 获取 mock_user 表的 dataTableId
        Long mockUserDataTableId = getMockUserDataTableId(tenantId);
        String mockUserTableName = TenantUtils.generateMockUserTableName(tenantId);
    
        // 构建标签规则（使用 mock_user 表的 dataTableId）
        LabelRule labelRule;
    
        switch (labelEnum) {
            case CONNECT_WILLINGNESS:
                labelRule = BuiltinLabelRuleBuilder.buildConnectWillingnessRule(
                    mockUserTableName, mockUserDataTableId);
                break;
            case COLD_CALL_ACCEPTANCE:
                labelRule = BuiltinLabelRuleBuilder.buildColdCallAcceptanceRule(
                    mockUserTableName, mockUserDataTableId);
                break;
            case AVG_CONVERSATION_ROUNDS:
                labelRule = BuiltinLabelRuleBuilder.buildAvgConversationRoundsRule(
                    mockUserTableName, mockUserDataTableId);
                break;
            default:
                // 其他标签暂时返回空规则
                labelRule = new LabelRule();
                labelRule.setLabelValueRules(new ArrayList<>());
        }
    
        return labelRule;
    }

    /**
     * 获取用户表的 dataTableId
     * @param tenantId 租户ID
     * @return datatable_info 表中的 ID
     */
    private Long getMockUserDataTableId(String tenantId) {
        String mockUserTableName = TenantUtils.generateMockUserTableName(tenantId);
        TableFieldMetaInfoCriteria tableFieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
        TableFieldMetaInfoCriteria.Criteria criteria = tableFieldMetaInfoCriteria.createCriteria();
        criteria.andTableEnNameEqualTo(mockUserTableName)
                .andIsVisableEqualTo(true);

        List<TableFieldMetaInfo> dataTableInfos = tableFieldMetaInfoMapper.selectByExample(tableFieldMetaInfoCriteria);
        if (CollectionUtils.isEmpty(dataTableInfos)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND,
                    "用户表不存在: " + mockUserTableName);
        }
        return dataTableInfos.get(0).getId();
    }

    /**
     * 确保内置标签所需的目录存在
     *
     * @param tenantId 租户ID
     */
    private void ensureBuiltinLabelCatalogs(String tenantId) {
        log.info("开始检查租户 {} 的内置标签目录", tenantId);

        // 1. 先创建内置标签专用的一级目录（默认目录已在租户初始化时创建）
        for (BuiltinLabelCatalogEnum primaryCatalog : BuiltinLabelCatalogEnum.getBuiltinPrimaryCatalogs()) {
            try {
                ensureCatalogExists(tenantId, primaryCatalog.getCatalogName(),
                    BuiltinLabelConstants.CatalogHierarchy.ROOT_CATALOG_ID);
                log.debug("内置标签一级目录检查完成: {}", primaryCatalog.getCatalogName());
            } catch (Exception e) {
                log.warn("内置标签一级目录检查失败: {}", primaryCatalog.getCatalogName(), e);
            }
        }

        // 2. 再创建所有二级目录
        for (BuiltinLabelCatalogEnum secondaryCatalog : BuiltinLabelCatalogEnum.getSecondaryCatalogs()) {
            try {
                // 先找到父目录
                LabelCatalog parentCatalog = labelCatalogService.checkLabelCatalog(
                    tenantId, secondaryCatalog.getParent().getCatalogName());
                ensureCatalogExists(tenantId, secondaryCatalog.getCatalogName(), parentCatalog.getId());
                log.debug("内置二级目录检查完成: {}", secondaryCatalog.getFullPath());
            } catch (Exception e) {
                log.warn("内置二级目录检查失败: {}", secondaryCatalog.getFullPath(), e);
            }
        }

        log.info("租户 {} 内置标签目录检查完成", tenantId);
    }

    /**
     * 确保指定目录存在，如果不存在则创建
     */
    private void ensureCatalogExists(String tenantId, String catalogName, Long parentId) {
        try {
            // 检查目录是否已存在
            if (Objects.equals(parentId, BuiltinLabelConstants.CatalogHierarchy.ROOT_CATALOG_ID)) {
                // 一级目录，直接按名称查找
                labelCatalogService.checkLabelCatalog(tenantId, catalogName);
                log.debug("目录已存在: {}", catalogName);
            } else {
                // 二级目录，需要检查是否在指定父目录下存在
                // 这里简化处理，如果checkLabelCatalog找到同名目录就认为存在
                labelCatalogService.checkLabelCatalog(tenantId, catalogName);
                log.debug("目录已存在: {}", catalogName);
            }
        } catch (DeepSightException.CatalogOperatorFailedException e) {
            // 目录不存在，创建新目录
            log.info("创建新目录: {} (父目录ID: {})", catalogName, parentId);
            try {
                NewCatalogRequest request = new NewCatalogRequest();
                request.setCatalogName(catalogName);
                request.setParentId(parentId != null ? parentId : BuiltinLabelConstants.CatalogHierarchy.ROOT_CATALOG_ID);
                labelCatalogService.createLabelCatalog(request);
                log.info("目录创建成功: {}", catalogName);
            } catch (Exception createException) {
                log.error("创建目录失败: {}", catalogName, createException);
                // 不抛出异常，继续处理其他目录
            }
        }
    }

    /**
     * 获取用户指标表信息
     * 直接从Doris查询user_metric表的结构信息
     *
     * @param tenantId 租户ID
     * @return 用户指标表信息
     */
    private UserMetricTableInfo getUserMetricTableInfo(String tenantId) {
        String userMetricTableName = Constants.USER_METRIC_AGG_TABLE_PREFIX + tenantId;

        try {
            // 检查表是否存在
            if (!dorisService.existTable(userMetricTableName)) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND,
                    "用户指标表不存在: " + userMetricTableName);
            }

            return new UserMetricTableInfo(userMetricTableName);
        } catch (DeepSightException.ParamsErrorException e) {
            // 重新抛出业务异常
            log.error("获取用户指标表信息失败，租户: {}, 表名: {}", tenantId, userMetricTableName, e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR,
                "获取用户指标表信息失败: " + userMetricTableName);
        }
    }

    /**
     * 构建NewLabelRequest，用于复用现有的标签创建逻辑
     */
    private NewLabelRequest buildNewLabelRequest(BuiltinLabelEnum labelEnum, Long catalogId, LabelRule labelRule) {
        NewLabelRequest request = new NewLabelRequest();

        // 基本信息
        request.setLabelName(labelEnum.getLabelName());
        request.setCatalogId(catalogId);
        request.setLabelRule(labelRule);

        // 内置标签的固定配置
        request.setUpdateMod(UpdateModEnum.REPLACE);           // 替换模式
        request.setLabelValueSaveMod(LabelValueSaveModEnum.SINGLE); // 单值模式
        request.setTriggerMod(TriggerModeEnum.MANUAL);         // 手动触发
        request.setTriggerFrequency(null);                     // 手动触发不需要频率
        request.setTriggerFrequencyValue(null);                // 手动触发不需要频率值

        return request;
    }

    /**
     * 创建系统用户Web上下文，用于内置标签创建
     */
    private DeepSightWebContext createSystemWebContext(String tenantId) {
        UserAuthInfo authInfo = new UserAuthInfo();
        authInfo.setUserId(BuiltinLabelConstants.SystemUser.SYSTEM_USER_ID);
        authInfo.setUserName(BuiltinLabelConstants.SystemUser.SYSTEM_USER_NAME);
        authInfo.setTenantId(Long.parseLong(tenantId));

        return new DeepSightWebContext(authInfo);
    }

}
