package com.baidu.keyue.deepsight.service.user.impl;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.models.agg.AiobAggMetricCal;
import com.baidu.keyue.deepsight.service.user.AiobMetricQueryService;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jooq.DSLContext;
import org.jooq.SQLDialect;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2025/8/13 11:34
 */
@Service
@Slf4j
public class AiobMetricQueryServiceImpl implements AiobMetricQueryService {

    @Autowired
    private DorisService dorisService;

    @Override
    public AiobAggMetricCal selectByTenantId(String tenantId, String oneId) {
        // sql构造
        DSLContext dsl = DSL.using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        String selectSql = dsl.selectDistinct(DSL.field(Constants.TABLE_USER_ONE_ID))
                .from(DSL.table(Constants.USER_METRIC_AGG_TABLE_PREFIX + tenantId))
                .where(DSL.field(Constants.TABLE_USER_ONE_ID).eq(oneId))
                .getSQL();
        log.debug("aiob metric query sql: " + selectSql);

        // 查询
        List<Map<String, Object>> queryRes = dorisService.selectList(selectSql);
        if (CollectionUtils.isEmpty(queryRes)) {
            return new AiobAggMetricCal();
        }

        Map<String, Object> res = queryRes.get(0);
        return AiobAggMetricCal.builder()
                .oneId(oneId)
                .connectRate((Float) res.get("connect_rate"))
                .firstRoundHangupRate((Float) res.get("first_round_hangup_rate"))
                .avgRounds((Float) res.get("avg_rounds"))
                .avgDuration((Float) res.get("avg_duration"))
                .lastCallDate((LocalDate) res.get("last_call_date"))
                .build();
    }

}
