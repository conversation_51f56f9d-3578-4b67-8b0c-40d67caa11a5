package com.baidu.keyue.deepsight.service.memory;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.database.doris.DorisConfiguration;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.config.MemoryCalculateConfiguration;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.RuleTypeEnum;
import com.baidu.keyue.deepsight.enums.SwitchEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TaskTypeEnum;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.label.TriggerFrequencyValue;
import com.baidu.keyue.deepsight.models.memory.cal.MemoryCalculateContext;
import com.baidu.keyue.deepsight.models.memory.cal.MemoryExtractMessage;
import com.baidu.keyue.deepsight.models.rules.dto.DqlParseResult;
import com.baidu.keyue.deepsight.models.rules.dto.RuleFilter;
import com.baidu.keyue.deepsight.models.rules.dto.RuleNode;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtractCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtractWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendMemoryExtractMapper;
import com.baidu.keyue.deepsight.service.rules.RuleManagerService;
import com.baidu.keyue.deepsight.service.rules.RuleParseService;
import com.baidu.keyue.deepsight.service.tasks.TaskInfoService;
import com.baidu.keyue.deepsight.service.tasks.TaskSchedulerService;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
public class MemoryCalculateService extends MemoryService {
    @Autowired
    private MemoryCalculateConfiguration memoryCalculateConfiguration;
    @Autowired
    private DorisConfiguration dorisConfiguration;

    @Autowired
    private TaskSchedulerService taskSchedulerService;

    @Autowired
    private TaskInfoService taskInfoService;

    @Autowired
    private DorisService dorisService;

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    private ExtendMemoryExtractMapper memoryExtractMapper;

    @Autowired
    private RuleManagerService ruleManagerService;

    @Autowired
    private RuleParseService ruleParseService;

    protected List<MemoryExtractWithBLOBs> getCronTaskObjList(List<Long> taskIdList) {
        MemoryExtractCriteria criteria = new MemoryExtractCriteria();
        criteria.createCriteria()
                .andCalStatusNotEqualTo(TaskExecStatusEnum.RUNNING.getCode())
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean())
                .andTaskIn(taskIdList)
                .andStatusEqualTo(SwitchEnum.ON.getBoolean());
        return memoryExtractMapper.selectByExampleWithBLOBs(criteria);
    }

    protected List<MemoryExtractWithBLOBs> getRealtimeTaskList() {
        MemoryExtractCriteria criteria = new MemoryExtractCriteria();
        criteria.createCriteria()
                .andTriggerModEqualTo(TriggerModeEnum.REALTIME.getCode())
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean())
                .andStatusEqualTo(SwitchEnum.ON.getBoolean());
        return memoryExtractMapper.selectByExampleWithBLOBs(criteria);
    }

    public List<Pair<MemoryExtractWithBLOBs, TaskInfo>> pullWaitExecMemoryTask(TriggerModeEnum triggerMod) {
        List<MemoryExtractWithBLOBs> list = null;
        List<TaskInfo> taskInfoList = null;
        if (TriggerModeEnum.REALTIME.equals(triggerMod)) {
            list = getRealtimeTaskList();
            if (CollectionUtils.isEmpty(list)) {
                return Collections.emptyList();
            }
            List<Long> taskIds = list.stream().map(MemoryExtractWithBLOBs::getTask).distinct().toList();
            taskInfoList = taskInfoService.getTaskDetailWithIds(TaskTypeEnum.MEMORY_EXTRACT, taskIds);
        } else {
            taskInfoList = taskInfoService.pullWaitExecTask(TaskTypeEnum.MEMORY_EXTRACT, triggerMod);
            if (CollectionUtils.isEmpty(taskInfoList)) {
                return Collections.emptyList();
            }
            List<Long> taskIds = taskInfoList.stream().map(TaskInfo::getId).distinct().toList();
            list = getCronTaskObjList(taskIds);
        }
        if (CollectionUtils.isEmpty(list) || CollectionUtils.isEmpty(taskInfoList)) {
            return Collections.emptyList();
        }

        Map<Long, TaskInfo> taskMap = taskInfoList.stream()
                .collect(Collectors.toMap(TaskInfo::getId, Function.identity(), (k1, k2) -> k2));
        if (taskMap.isEmpty()) {
            return Collections.emptyList();
        }
        return list.stream()
                .map(item -> {
                            Long taskId = item.getTask();
                            if (Objects.isNull(taskId)) {
                                return null;
                            }
                            TaskInfo task = taskMap.get(taskId);
                            if (Objects.isNull(task)) {
                                return null;
                            }
                            return new ImmutablePair<>(item, task);
                        }
                ).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public void execRightNow(long memId) {
        MemoryExtractWithBLOBs record = memoryExtractMapper.selectByPrimaryKey(memId);
        long taskId = record.getTask();
        TaskInfo task = taskInfoService.getTaskDetailWithId(taskId);
        execByScheduler(record, task);
    }

    public void execByScheduler(MemoryExtractWithBLOBs memory, TaskInfo task) {
        MemoryCalculateContext calculateExecInstance = new MemoryCalculateContext();
        calculateExecInstance.setMemory(memory);
        calculateExecInstance.setInstanceId("");
        calculateExecInstance.setTriggerMode(TriggerModeEnum.getByCode(memory.getTriggerMod()));
        calculateExecInstance.setTenantId(memory.getTenantId());

        if (calculateExecInstance.notRealtimeTask()) {
            long execId = newSchedulerRecord(memory.getTask());
            calculateExecInstance.setExecId(execId);
        } else {
            calculateExecInstance.setExecId(-1L);
        }

        onProcessing(calculateExecInstance);

        List<RuleFilter> dataFilterRule = Lists.newArrayList();
        if (StringUtils.isNotBlank(memory.getDataFilterRule())) {
            dataFilterRule = JsonUtils.toListUnchecked(memory.getDataFilterRule(), List.class, RuleFilter.class);
        }
        DataTableInfo tableInfo = getDataSetTableDetail(memory.getDataTableId());
        if (Objects.isNull(tableInfo)) {
            calculateExecInstance.setErr(new DeepSightException.MemoryExtractOperatorFailedException(ErrorCode.INTERNAL_ERROR, "数据源不存在"));
            onFailed(calculateExecInstance);
            log.warn("MemoryService.execByScheduler got null tableInfo, memoryTask: {}, execId: {}, err: ",
                    calculateExecInstance.getMemory().getId(), calculateExecInstance.getExecId(), calculateExecInstance.getErr());
            return;
        }
        TableFieldMetaInfo fieldMetaInfo = getFiledMetaInfoMap(memory.getFieldId(), memory.getDataTableId());
        if (Objects.isNull(fieldMetaInfo)) {
            calculateExecInstance.setErr(new DeepSightException.MemoryExtractOperatorFailedException(ErrorCode.INTERNAL_ERROR, "数据源MetaInfo不存在"));
            onFailed(calculateExecInstance);
            log.warn("MemoryService.execByScheduler got null fieldMetaInfo, memoryTask: {}, execId: {}, err: ",
                    calculateExecInstance.getMemory().getId(), calculateExecInstance.getExecId(), calculateExecInstance.getErr());
            return;
        }

        List<MemoryExtractMessage> messages = fetchDataRows(calculateExecInstance, tableInfo, dataFilterRule, fieldMetaInfo);
        if (Objects.nonNull(calculateExecInstance.getErr())) {
            onFailed(calculateExecInstance);
            log.warn("MemoryService.execByScheduler MemoryExtractExec Failed, memoryTask: {}, execId: {}, err: ",
                    calculateExecInstance.getMemory().getId(), calculateExecInstance.getExecId(), calculateExecInstance.getErr());
            return;
        }

        if (CollectionUtils.isEmpty(messages)) {
            calculateExecInstance.setCount(0);
        } else {
            messages.forEach(this::kafkaMessageSend);
            calculateExecInstance.setCount(messages.size());
        }

        onFinished(calculateExecInstance, task);
        log.info("MemoryService.execByScheduler MemoryExtractExec, memoryTask: {} execId: {}, count: {}",
                calculateExecInstance.getMemory().getId(), calculateExecInstance.getExecId(), calculateExecInstance.getCount());
    }

    private List<MemoryExtractMessage> fetchDataRows(MemoryCalculateContext calculateExecInstance, DataTableInfo tableInfo,
                                                     List<RuleFilter> dataFilterRule, TableFieldMetaInfo fieldMetaInfo) {
        List<Map<String, Object>> recordList;
        String tableName = tableInfo.getTableName();
        if (tableName.startsWith(Constants.DORIS_AIOB_RECORD_TABLE)
                || tableName.startsWith(Constants.DORIS_AIOB_SESSION_TABLE)) {
            recordList = fetchAiobTalkData(calculateExecInstance);
        } else if (tableName.startsWith(Constants.DORIS_KEYUE_RECORD_TABLE)) {
            recordList = fetchCustomerTalkData(calculateExecInstance, dataFilterRule);
        } else {
            recordList = fetchOthersData(calculateExecInstance, fieldMetaInfo, dataFilterRule);
        }
        return buildMemoryExtractMessage(calculateExecInstance.getMemory(), recordList);
    }

    private List<Map<String, Object>> fetchAiobTalkData(MemoryCalculateContext calculateExecInstance) {
        String sessionTable = TenantUtils.generateAiobSessionTableName(calculateExecInstance.getTenantId());

        String timeFilter = "";
        if (TriggerModeEnum.REALTIME.equals(calculateExecInstance.getTriggerMode())) {
            timeFilter = DatetimeUtils.backToDate(1);
        } else {
            TriggerFrequencyEnum triggerFrequencyEnum = TriggerFrequencyEnum.getByCode(calculateExecInstance.getMemory().getTriggerFrequency());
            TriggerFrequencyValue triggerFrequencyValue = JsonUtils.toObjectWithoutException(
                    calculateExecInstance.getMemory().getTriggerFrequencyValue(), TriggerFrequencyValue.class);
            timeFilter = DatetimeUtils.backToWithFrequency(triggerFrequencyEnum, triggerFrequencyValue);
        }

        String sqlStr = ORMUtils.getMemoryCalculateAiobSession(sessionTable, timeFilter);

        calculateExecInstance.setRuleSql(sqlStr);
        List<Map<String, Object>> recordList = null;
        try {
            recordList = dorisService.selectList(sqlStr);
        } catch (Exception e) {
            log.error("MemoryService.execByScheduler selectList error", e);
            calculateExecInstance.setErr(new DeepSightException.MemoryExtractOperatorFailedException(ErrorCode.INTERNAL_ERROR, "拉取数据失败"));
            return Lists.newArrayList();
        }

        if (CollectionUtils.isEmpty(recordList)) {
            return null;
        }

        List<Map<String, Object>> mergeRecordList = Lists.newArrayList();
        recordList.forEach(record -> {
            Object oneId = record.get(Constants.TABLE_USER_ONE_ID);
            Object sessionId = record.get("sessionId");
            Object content = record.get("conversationContent");
            if (Objects.isNull(oneId) || Objects.isNull(content)) {
                return;
            }
            Map<String, Object> t = new HashMap<>();
            t.put("user_id", oneId);
            t.put("external_id", sessionId);
            t.put("text", content);
            mergeRecordList.add(t);
        });

        return mergeRecordList;
    }

    private List<Map<String, Object>> fetchCustomerTalkData(MemoryCalculateContext calculateExecInstance, List<RuleFilter> dataFilterRule) {
        List<String> selectFields = Lists.newArrayList(Constants.TABLE_USER_ONE_ID, "sessionId", "queryText");
        List<Map<String, Object>> recordList = fetchData(calculateExecInstance, selectFields, dataFilterRule);

        List<Map<String, Object>> results = Lists.newArrayList();
        if (CollectionUtils.isEmpty(recordList)) {
            return results;
        }

        recordList.forEach(item -> {
            Map<String, Object> m = new HashMap<>();
            m.put("user_id", item.get(Constants.TABLE_USER_ONE_ID));
            m.put("external_id", item.get("sessionId"));
            m.put("text", item.get("queryText"));
            results.add(m);
        });
        return results;
    }

    private List<Map<String, Object>> fetchOthersData(MemoryCalculateContext calculateExecInstance, TableFieldMetaInfo fieldMetaInfo, List<RuleFilter> dataFilterRule) {
        List<String> selectFields = Lists.newArrayList(Constants.TABLE_USER_ONE_ID, fieldMetaInfo.getEnField());
        List<Map<String, Object>> recordList = fetchData(calculateExecInstance, selectFields, dataFilterRule);
        List<Map<String, Object>> results = Lists.newArrayList();
        if (CollectionUtils.isEmpty(recordList)) {
            return results;
        }

        recordList.forEach(item -> {
            Map<String, Object> m = new HashMap<>();
            m.put("user_id", item.get(Constants.TABLE_USER_ONE_ID));
            m.put("external_id", "");
            m.put("text", item.get(fieldMetaInfo.getEnField()));
            results.add(m);
        });
        return results;

    }

    private List<Map<String, Object>> fetchData(MemoryCalculateContext calculateExecInstance, List<String> selectFields, List<RuleFilter> dataFilterRule) {
        String timeFilter;
        if (TriggerModeEnum.REALTIME.equals(calculateExecInstance.getTriggerMode())) {
            timeFilter = String.format(" %s > '%s'", Constants.DORIS_DEFAULT_DATA_INSERT_DATE_FIELD, DatetimeUtils.backToDate(1));
        } else {
            TriggerFrequencyEnum triggerFrequencyEnum = TriggerFrequencyEnum.getByCode(calculateExecInstance.getMemory().getTriggerFrequency());
            TriggerFrequencyValue triggerFrequencyValue = JsonUtils.toObjectWithoutException(
                    calculateExecInstance.getMemory().getTriggerFrequencyValue(), TriggerFrequencyValue.class);
            timeFilter = String.format(" %s > '%s'", Constants.DORIS_DEFAULT_DATA_INSERT_DATE_FIELD,
                    DatetimeUtils.backToWithFrequency(triggerFrequencyEnum, triggerFrequencyValue));
        }

        RuleNode ruleNode;

        String sqlStr;
        if (CollectionUtils.isEmpty(dataFilterRule)) {
            ruleNode = RuleNode.builder().dataTableId(calculateExecInstance.getMemory().getDataTableId()).filters(Lists.newArrayList()).type(RuleTypeEnum.DATASET).build();
            DqlParseResult dqlParseResult = ruleManagerService.parseRuleNode(ruleNode);
            dqlParseResult.setSelect(String.join(Constants.SEPARATOR, selectFields));
            sqlStr = dqlParseResult.parseDorisSql();
            sqlStr += " WHERE ";
            sqlStr += timeFilter;
        } else {
            ruleNode = RuleNode.builder().dataTableId(calculateExecInstance.getMemory().getDataTableId()).filters(dataFilterRule).type(RuleTypeEnum.DATASET).build();
            DqlParseResult dqlParseResult = ruleManagerService.parseRuleNode(ruleNode);
            dqlParseResult.setSelect(Constants.TABLE_USER_ONE_ID);
            sqlStr = String.format("select %s from %s where `%s` in (%s)",
                    String.join(Constants.SEPARATOR, selectFields), dqlParseResult.getFrom(), Constants.TABLE_USER_ONE_ID, dqlParseResult.parseDorisSql());

            sqlStr += " AND ";
            sqlStr += timeFilter;
        }

        calculateExecInstance.setRuleSql(sqlStr);
        List<Map<String, Object>> recordList = null;

        try {
            recordList = dorisService.selectList(sqlStr);
        } catch (Exception e) {
            log.error("MemoryService.fetchData selectList error", e);
            calculateExecInstance.setErr(new DeepSightException.MemoryExtractOperatorFailedException(ErrorCode.INTERNAL_ERROR, "拉取数据失败"));
            return Lists.newArrayList();
        }
        return recordList;
    }

    public void onFinished(MemoryCalculateContext calculateExecInstance, TaskInfo task) {
        if (calculateExecInstance.notRealtimeTask()) {
            taskSchedulerService.updateScheduler(calculateExecInstance, TaskExecStatusEnum.SUCCESS);
        }
        memoryExtractMapper.updateCalTaskStatus(calculateExecInstance.getMemory().getId(), TaskExecStatusEnum.SUCCESS.getCode());

        if (!TriggerModeEnum.REALTIME.equals(calculateExecInstance.getTriggerMode())) {
            taskInfoService.updateNextCalDate(task);
        }
    }

    public void onProcessing(MemoryCalculateContext calculateExecInstance) {
        if (calculateExecInstance.notRealtimeTask()) {
            taskSchedulerService.updateScheduler(calculateExecInstance, TaskExecStatusEnum.RUNNING);
        }
        memoryExtractMapper.updateCalTaskStatus(calculateExecInstance.getMemory().getId(), TaskExecStatusEnum.RUNNING.getCode());
    }

    public void onFailed(MemoryCalculateContext calculateExecInstance) {
        if (calculateExecInstance.notRealtimeTask()) {
            taskSchedulerService.updateScheduler(calculateExecInstance, TaskExecStatusEnum.FAILED);
        }
        memoryExtractMapper.updateCalTaskStatus(calculateExecInstance.getMemory().getId(), TaskExecStatusEnum.FAILED.getCode());
    }

    private List<MemoryExtractMessage> buildMemoryExtractMessage(
            MemoryExtractWithBLOBs memory, List<Map<String, Object>> recordList) {
        List<MemoryExtractMessage> messages = Lists.newArrayList();
        if (CollectionUtils.isEmpty(recordList)) {
            return messages;
        }

        recordList.forEach(record -> {
            MemoryExtractMessage message = new MemoryExtractMessage();
            message.setMemoryId(memory.getId());
            message.setDatasetId(memory.getDataTableId());
            message.setModelUrl(memoryCalculateConfiguration.getModelUrl());
            message.setDorisSpace(dorisConfiguration.getDb());
            message.setDorisTable(TenantUtils.generateMemoryExtractTableName(memory.getTenantId()));
            message.setTenantId(memory.getTenantId());
            message.setPrompt(memory.getPrompt());

            String text = String.valueOf(record.get("text"));
            if (StringUtils.isNotBlank(text)) {
                message.setUserId(String.valueOf(record.get("user_id")));
                message.setExternalId(String.valueOf(record.get("external_id")));
                message.setText(text);
                messages.add(message);
            }
        });
        return messages;
    }

    /**
     * 创建 task scheduler 执行记录
     * @param taskId 任务 id
     * @return 记录 id
     */
    private long newSchedulerRecord(long taskId) {
        return taskSchedulerService.newTaskScheduler(taskId, "CronScheduler");
    }

    private void kafkaMessageSend(MemoryExtractMessage data) {
        try {
            String msg = JsonUtils.toJson(data);
            kafkaTemplate.send(memoryCalculateConfiguration.getKafkaConfig().getKafkaTopic(), msg);
        } catch (Exception e) {
            log.error("kafka message send error", e);
        }
    }

}
