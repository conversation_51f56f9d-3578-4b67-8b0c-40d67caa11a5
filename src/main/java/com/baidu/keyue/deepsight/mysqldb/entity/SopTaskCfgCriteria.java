package com.baidu.keyue.deepsight.mysqldb.entity;

import java.util.ArrayList;
import java.util.List;

/**
 * SOP任务配置查询条件类
 */
public class SopTaskCfgCriteria {

    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public SopTaskCfgCriteria() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(String value) {
            addCriterion("task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(String value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(String value) {
            addCriterion("task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(String value) {
            addCriterion("task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(String value) {
            addCriterion("task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(String value) {
            addCriterion("task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLike(String value) {
            addCriterion("task_id like", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotLike(String value) {
            addCriterion("task_id not like", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<String> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<String> values) {
            addCriterion("task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(String value1, String value2) {
            addCriterion("task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(String value1, String value2) {
            addCriterion("task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andRobotIdIsNull() {
            addCriterion("robot_id is null");
            return (Criteria) this;
        }

        public Criteria andRobotIdIsNotNull() {
            addCriterion("robot_id is not null");
            return (Criteria) this;
        }

        public Criteria andRobotIdEqualTo(String value) {
            addCriterion("robot_id =", value, "robotId");
            return (Criteria) this;
        }

        public Criteria andRobotIdNotEqualTo(String value) {
            addCriterion("robot_id <>", value, "robotId");
            return (Criteria) this;
        }

        public Criteria andRobotIdGreaterThan(String value) {
            addCriterion("robot_id >", value, "robotId");
            return (Criteria) this;
        }

        public Criteria andRobotIdGreaterThanOrEqualTo(String value) {
            addCriterion("robot_id >=", value, "robotId");
            return (Criteria) this;
        }

        public Criteria andRobotIdLessThan(String value) {
            addCriterion("robot_id <", value, "robotId");
            return (Criteria) this;
        }

        public Criteria andRobotIdLessThanOrEqualTo(String value) {
            addCriterion("robot_id <=", value, "robotId");
            return (Criteria) this;
        }

        public Criteria andRobotIdLike(String value) {
            addCriterion("robot_id like", value, "robotId");
            return (Criteria) this;
        }

        public Criteria andRobotIdNotLike(String value) {
            addCriterion("robot_id not like", value, "robotId");
            return (Criteria) this;
        }

        public Criteria andRobotIdIn(List<String> values) {
            addCriterion("robot_id in", values, "robotId");
            return (Criteria) this;
        }

        public Criteria andRobotIdNotIn(List<String> values) {
            addCriterion("robot_id not in", values, "robotId");
            return (Criteria) this;
        }

        public Criteria andRobotIdBetween(String value1, String value2) {
            addCriterion("robot_id between", value1, value2, "robotId");
            return (Criteria) this;
        }

        public Criteria andRobotIdNotBetween(String value1, String value2) {
            addCriterion("robot_id not between", value1, value2, "robotId");
            return (Criteria) this;
        }

        public Criteria andRobotSceneIsNull() {
            addCriterion("robot_scene is null");
            return (Criteria) this;
        }

        public Criteria andRobotSceneIsNotNull() {
            addCriterion("robot_scene is not null");
            return (Criteria) this;
        }

        public Criteria andRobotSceneEqualTo(Byte value) {
            addCriterion("robot_scene =", value, "robotScene");
            return (Criteria) this;
        }

        public Criteria andRobotSceneNotEqualTo(Byte value) {
            addCriterion("robot_scene <>", value, "robotScene");
            return (Criteria) this;
        }

        public Criteria andRobotSceneGreaterThan(Byte value) {
            addCriterion("robot_scene >", value, "robotScene");
            return (Criteria) this;
        }

        public Criteria andRobotSceneGreaterThanOrEqualTo(Byte value) {
            addCriterion("robot_scene >=", value, "robotScene");
            return (Criteria) this;
        }

        public Criteria andRobotSceneLessThan(Byte value) {
            addCriterion("robot_scene <", value, "robotScene");
            return (Criteria) this;
        }

        public Criteria andRobotSceneLessThanOrEqualTo(Byte value) {
            addCriterion("robot_scene <=", value, "robotScene");
            return (Criteria) this;
        }

        public Criteria andRobotSceneIn(List<Byte> values) {
            addCriterion("robot_scene in", values, "robotScene");
            return (Criteria) this;
        }

        public Criteria andRobotSceneNotIn(List<Byte> values) {
            addCriterion("robot_scene not in", values, "robotScene");
            return (Criteria) this;
        }

        public Criteria andRobotSceneBetween(Byte value1, Byte value2) {
            addCriterion("robot_scene between", value1, value2, "robotScene");
            return (Criteria) this;
        }

        public Criteria andRobotSceneNotBetween(Byte value1, Byte value2) {
            addCriterion("robot_scene not between", value1, value2, "robotScene");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andTaskMsgIsNull() {
            addCriterion("task_msg is null");
            return (Criteria) this;
        }

        public Criteria andTaskMsgIsNotNull() {
            addCriterion("task_msg is not null");
            return (Criteria) this;
        }

        public Criteria andTaskMsgEqualTo(String value) {
            addCriterion("task_msg =", value, "taskMsg");
            return (Criteria) this;
        }

        public Criteria andTaskMsgNotEqualTo(String value) {
            addCriterion("task_msg <>", value, "taskMsg");
            return (Criteria) this;
        }

        public Criteria andTaskMsgGreaterThan(String value) {
            addCriterion("task_msg >", value, "taskMsg");
            return (Criteria) this;
        }

        public Criteria andTaskMsgGreaterThanOrEqualTo(String value) {
            addCriterion("task_msg >=", value, "taskMsg");
            return (Criteria) this;
        }

        public Criteria andTaskMsgLessThan(String value) {
            addCriterion("task_msg <", value, "taskMsg");
            return (Criteria) this;
        }

        public Criteria andTaskMsgLessThanOrEqualTo(String value) {
            addCriterion("task_msg <=", value, "taskMsg");
            return (Criteria) this;
        }

        public Criteria andTaskMsgLike(String value) {
            addCriterion("task_msg like", value, "taskMsg");
            return (Criteria) this;
        }

        public Criteria andTaskMsgNotLike(String value) {
            addCriterion("task_msg not like", value, "taskMsg");
            return (Criteria) this;
        }

        public Criteria andTaskMsgIn(List<String> values) {
            addCriterion("task_msg in", values, "taskMsg");
            return (Criteria) this;
        }

        public Criteria andTaskMsgNotIn(List<String> values) {
            addCriterion("task_msg not in", values, "taskMsg");
            return (Criteria) this;
        }

        public Criteria andTaskMsgBetween(String value1, String value2) {
            addCriterion("task_msg between", value1, value2, "taskMsg");
            return (Criteria) this;
        }

        public Criteria andTaskMsgNotBetween(String value1, String value2) {
            addCriterion("task_msg not between", value1, value2, "taskMsg");
            return (Criteria) this;
        }

        // ===================== 新增：task_name 常规条件 =====================
        public Criteria andTaskNameIsNull() {
            addCriterion("task_name is null");
            return (Criteria) this;
        }

        public Criteria andTaskNameIsNotNull() {
            addCriterion("task_name is not null");
            return (Criteria) this;
        }

        public Criteria andTaskNameEqualTo(String value) {
            addCriterion("task_name =", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameNotEqualTo(String value) {
            addCriterion("task_name <>", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameIn(List<String> values) {
            addCriterion("task_name in", values, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameNotIn(List<String> values) {
            addCriterion("task_name not in", values, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameBetween(String value1, String value2) {
            addCriterion("task_name between", value1, value2, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameNotBetween(String value1, String value2) {
            addCriterion("task_name not between", value1, value2, "taskName");
            return (Criteria) this;
        }

        /**
         * 使用 MySQL LOCATE() 实现包含匹配：LOCATE(#{value}, task_name) > 0
         * 注意：需要在 Mapper XML 中按 typeHandler 进行专门渲染（见下方 XML 补丁）。
         */
        public Criteria andTaskNameContainsByLocate(String value) {
            if (value == null) {
                throw new RuntimeException("Value for taskName cannot be null");
            }
            // 利用 typeHandler 作为“标记”，在 XML 中识别并按自定义语句渲染
            criteria.add(new Criterion("task_name", value, "LOCATE_TASK_NAME_CONTAINS"));
            return (Criteria) this;
        }

        // ===================== 新增：robot_name 常规条件 =====================
        public Criteria andRobotNameIsNull() {
            addCriterion("robot_name is null");
            return (Criteria) this;
        }

        public Criteria andRobotNameIsNotNull() {
            addCriterion("robot_name is not null");
            return (Criteria) this;
        }

        public Criteria andRobotNameEqualTo(String value) {
            addCriterion("robot_name =", value, "robotName");
            return (Criteria) this;
        }

        public Criteria andRobotNameNotEqualTo(String value) {
            addCriterion("robot_name <>", value, "robotName");
            return (Criteria) this;
        }

        public Criteria andRobotNameIn(List<String> values) {
            addCriterion("robot_name in", values, "robotName");
            return (Criteria) this;
        }

        public Criteria andRobotNameNotIn(List<String> values) {
            addCriterion("robot_name not in", values, "robotName");
            return (Criteria) this;
        }

        public Criteria andRobotNameBetween(String value1, String value2) {
            addCriterion("robot_name between", value1, value2, "robotName");
            return (Criteria) this;
        }

        public Criteria andRobotNameNotBetween(String value1, String value2) {
            addCriterion("robot_name not between", value1, value2, "robotName");
            return (Criteria) this;
        }

        /**
         * 使用 MySQL LOCATE() 实现包含匹配：LOCATE(#{value}, robot_name) > 0
         * 注意：需要在 Mapper XML 中按 typeHandler 进行专门渲染（见下方 XML 补丁）。
         */
        public Criteria andRobotNameContainsByLocate(String value) {
            if (value == null) {
                throw new RuntimeException("Value for robotName cannot be null");
            }
            criteria.add(new Criterion("robot_name", value, "LOCATE_ROBOT_NAME_CONTAINS"));
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        /**
         * 这里把 MBG 原有的 typeHandler 字段用作“渲染标记”，在 XML 里按需识别
         * 并不是实际的 MyBatis TypeHandler。
         */
        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}