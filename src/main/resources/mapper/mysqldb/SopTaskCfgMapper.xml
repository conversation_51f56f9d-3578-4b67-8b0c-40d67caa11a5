<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.keyue.deepsight.mysqldb.mapper.SopTaskCfgMapper">
  <resultMap id="BaseResultMap" type="com.baidu.keyue.deepsight.mysqldb.entity.SopTaskCfg">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_cfg_id" jdbcType="BIGINT" property="userCfgId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="task_id" jdbcType="VARCHAR" property="taskId" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="robot_id" jdbcType="VARCHAR" property="robotId" />
    <result column="robot_name" jdbcType="VARCHAR" property="robotName" />
    <result column="robot_scene" jdbcType="TINYINT" property="robotScene" />
    <result column="robot_version" jdbcType="VARCHAR" property="robotVersion" />
    <result column="is_auto_answer" jdbcType="TINYINT" property="isAutoAnswer" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tags" jdbcType="VARCHAR" property="tags" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="task_msg" jdbcType="VARCHAR" property="taskMsg" />
  </resultMap>

  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>

                <!-- 基于 typeHandler 的 LOCATE 渲染 -->
                <when test="criterion.typeHandler == 'LOCATE_TASK_NAME_CONTAINS'">
                  and LOCATE(#{criterion.value}, task_name) > 0
                </when>
                <when test="criterion.typeHandler == 'LOCATE_ROBOT_NAME_CONTAINS'">
                  and LOCATE(#{criterion.value}, robot_name) > 0
                </when>

                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>

                <!-- 与上方一致，支持 LOCATE -->
                <when test="criterion.typeHandler == 'LOCATE_TASK_NAME_CONTAINS'">
                  and LOCATE(#{criterion.value}, task_name) > 0
                </when>
                <when test="criterion.typeHandler == 'LOCATE_ROBOT_NAME_CONTAINS'">
                  and LOCATE(#{criterion.value}, robot_name) > 0
                </when>

                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <sql id="Base_Column_List">
    id, user_cfg_id, tenant_id, task_id, task_name, robot_id, robot_name, robot_scene,
    robot_version, is_auto_answer, creator, modifier, create_time, update_time, tags, status, task_msg
  </sql>

  <select id="selectByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.SopTaskCfgCriteria" resultMap="BaseResultMap">
    select
    <if test="distinct">distinct</if>
    <include refid="Base_Column_List" />
    from sop_task_cfg
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sop_task_cfg
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sop_task_cfg
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <delete id="deleteByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.SopTaskCfgCriteria">
    delete from sop_task_cfg
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>

  <insert id="insert" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.SopTaskCfg">
    insert into sop_task_cfg (
      id, user_cfg_id, tenant_id, task_id, task_name, robot_id, robot_name, robot_scene,
      robot_version, is_auto_answer, creator, modifier, create_time, update_time, tags, status, task_msg
    )
    values (
             #{id,jdbcType=BIGINT}, #{userCfgId,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{taskId,jdbcType=VARCHAR},
             #{taskName,jdbcType=VARCHAR}, #{robotId,jdbcType=VARCHAR}, #{robotName,jdbcType=VARCHAR}, #{robotScene,jdbcType=TINYINT},
             #{robotVersion,jdbcType=VARCHAR}, #{isAutoAnswer,jdbcType=TINYINT}, #{creator,jdbcType=VARCHAR},
             #{modifier,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
             #{tags,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{taskMsg,jdbcType=VARCHAR}
           )
  </insert>

  <insert id="insertSelective" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.SopTaskCfg">
    insert into sop_task_cfg
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">id,</if>
      <if test="userCfgId != null">user_cfg_id,</if>
      <if test="tenantId != null">tenant_id,</if>
      <if test="taskId != null">task_id,</if>
      <if test="taskName != null">task_name,</if>
      <if test="robotId != null">robot_id,</if>
      <if test="robotName != null">robot_name,</if>
      <if test="robotScene != null">robot_scene,</if>
      <if test="robotVersion != null">robot_version,</if>
      <if test="isAutoAnswer != null">is_auto_answer,</if>
      <if test="creator != null">creator,</if>
      <if test="modifier != null">modifier,</if>
      <if test="createTime != null">create_time,</if>
      <if test="updateTime != null">update_time,</if>
      <if test="tags != null">tags,</if>
      <if test="status != null">status,</if>
      <if test="taskMsg != null">task_msg,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">#{id,jdbcType=BIGINT},</if>
      <if test="userCfgId != null">#{userCfgId,jdbcType=BIGINT},</if>
      <if test="tenantId != null">#{tenantId,jdbcType=VARCHAR},</if>
      <if test="taskId != null">#{taskId,jdbcType=VARCHAR},</if>
      <if test="taskName != null">#{taskName,jdbcType=VARCHAR},</if>
      <if test="robotId != null">#{robotId,jdbcType=VARCHAR},</if>
      <if test="robotName != null">#{robotName,jdbcType=VARCHAR},</if>
      <if test="robotScene != null">#{robotScene,jdbcType=TINYINT},</if>
      <if test="robotVersion != null">#{robotVersion,jdbcType=VARCHAR},</if>
      <if test="isAutoAnswer != null">#{isAutoAnswer,jdbcType=TINYINT},</if>
      <if test="creator != null">#{creator,jdbcType=VARCHAR},</if>
      <if test="modifier != null">#{modifier,jdbcType=VARCHAR},</if>
      <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
      <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
      <if test="tags != null">#{tags,jdbcType=VARCHAR},</if>
      <if test="status != null">#{status,jdbcType=TINYINT},</if>
      <if test="taskMsg != null">#{taskMsg,jdbcType=VARCHAR},</if>
    </trim>
  </insert>

  <select id="countByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.SopTaskCfgCriteria" resultType="java.lang.Long">
    select count(*) from sop_task_cfg
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <update id="updateByExampleSelective" parameterType="map">
    update sop_task_cfg
    <set>
      <if test="record.id != null">id = #{record.id,jdbcType=BIGINT},</if>
      <if test="record.userCfgId != null">user_cfg_id = #{record.userCfgId,jdbcType=BIGINT},</if>
      <if test="record.tenantId != null">tenant_id = #{record.tenantId,jdbcType=VARCHAR},</if>
      <if test="record.taskId != null">task_id = #{record.taskId,jdbcType=VARCHAR},</if>
      <if test="record.taskName != null">task_name = #{record.taskName,jdbcType=VARCHAR},</if>
      <if test="record.robotId != null">robot_id = #{record.robotId,jdbcType=VARCHAR},</if>
      <if test="record.robotName != null">robot_name = #{record.robotName,jdbcType=VARCHAR},</if>
      <if test="record.robotScene != null">robot_scene = #{record.robotScene,jdbcType=TINYINT},</if>
      <if test="record.robotVersion != null">robot_version = #{record.robotVersion,jdbcType=VARCHAR},</if>
      <if test="record.isAutoAnswer != null">is_auto_answer = #{record.isAutoAnswer,jdbcType=TINYINT},</if>
      <if test="record.creator != null">creator = #{record.creator,jdbcType=VARCHAR},</if>
      <if test="record.modifier != null">modifier = #{record.modifier,jdbcType=VARCHAR},</if>
      <if test="record.createTime != null">create_time = #{record.createTime,jdbcType=TIMESTAMP},</if>
      <if test="record.updateTime != null">update_time = #{record.updateTime,jdbcType=TIMESTAMP},</if>
      <if test="record.tags != null">tags = #{record.tags,jdbcType=VARCHAR},</if>
      <if test="record.status != null">status = #{record.status,jdbcType=TINYINT},</if>
      <if test="record.taskMsg != null">task_msg = #{record.taskMsg,jdbcType=VARCHAR},</if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <update id="updateByExample" parameterType="map">
    update sop_task_cfg
    set id = #{record.id,jdbcType=BIGINT},
    user_cfg_id = #{record.userCfgId,jdbcType=BIGINT},
    tenant_id = #{record.tenantId,jdbcType=VARCHAR},
    task_id = #{record.taskId,jdbcType=VARCHAR},
    task_name = #{record.taskName,jdbcType=VARCHAR},
    robot_id = #{record.robotId,jdbcType=VARCHAR},
    robot_name = #{record.robotName,jdbcType=VARCHAR},
    robot_scene = #{record.robotScene,jdbcType=TINYINT},
    robot_version = #{record.robotVersion,jdbcType=VARCHAR},
    is_auto_answer = #{record.isAutoAnswer,jdbcType=TINYINT},
    creator = #{record.creator,jdbcType=VARCHAR},
    modifier = #{record.modifier,jdbcType=VARCHAR},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    tags = #{record.tags,jdbcType=VARCHAR},
    status = #{record.status,jdbcType=TINYINT},
    task_msg = #{record.taskMsg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <update id="updateByPrimaryKeySelective" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.SopTaskCfg">
    update sop_task_cfg
    <set>
      <if test="userCfgId != null">user_cfg_id = #{userCfgId,jdbcType=BIGINT},</if>
      <if test="tenantId != null">tenant_id = #{tenantId,jdbcType=VARCHAR},</if>
      <if test="taskId != null">task_id = #{taskId,jdbcType=VARCHAR},</if>
      <if test="taskName != null">task_name = #{taskName,jdbcType=VARCHAR},</if>
      <if test="robotId != null">robot_id = #{robotId,jdbcType=VARCHAR},</if>
      <if test="robotName != null">robot_name = #{robotName,jdbcType=VARCHAR},</if>
      <if test="robotScene != null">robot_scene = #{robotScene,jdbcType=TINYINT},</if>
      <if test="robotVersion != null">robot_version = #{robotVersion,jdbcType=VARCHAR},</if>
      <if test="isAutoAnswer != null">is_auto_answer = #{isAutoAnswer,jdbcType=TINYINT},</if>
      <if test="creator != null">creator = #{creator,jdbcType=VARCHAR},</if>
      <if test="modifier != null">modifier = #{modifier,jdbcType=VARCHAR},</if>
      <if test="createTime != null">create_time = #{createTime,jdbcType=TIMESTAMP},</if>
      <if test="updateTime != null">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>
      <if test="tags != null">tags = #{tags,jdbcType=VARCHAR},</if>
      <if test="status != null">status = #{status,jdbcType=TINYINT},</if>
      <if test="taskMsg != null">task_msg = #{taskMsg,jdbcType=VARCHAR},</if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.SopTaskCfg">
    update sop_task_cfg
    set user_cfg_id = #{userCfgId,jdbcType=BIGINT},
        tenant_id = #{tenantId,jdbcType=VARCHAR},
        task_id = #{taskId,jdbcType=VARCHAR},
        task_name = #{taskName,jdbcType=VARCHAR},
        robot_id = #{robotId,jdbcType=VARCHAR},
        robot_name = #{robotName,jdbcType=VARCHAR},
        robot_scene = #{robotScene,jdbcType=TINYINT},
        robot_version = #{robotVersion,jdbcType=VARCHAR},
        is_auto_answer = #{isAutoAnswer,jdbcType=TINYINT},
        creator = #{creator,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        tags = #{tags,jdbcType=VARCHAR},
        status = #{status,jdbcType=TINYINT},
        task_msg = #{taskMsg,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 自定义查询 -->
  <select id="selectByTenantAndTaskAndRobotAndScene" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sop_task_cfg
    where tenant_id = #{tenantId,jdbcType=VARCHAR}
    and task_id = #{taskId,jdbcType=VARCHAR}
    and robot_id = #{robotId,jdbcType=VARCHAR}
    and robot_scene = #{robotScene,jdbcType=TINYINT}
    limit 1
  </select>

  <select id="selectByTenantAndStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sop_task_cfg
    where tenant_id = #{tenantId,jdbcType=VARCHAR}
    <if test="status != null">
      and status = #{status,jdbcType=TINYINT}
    </if>
    order by create_time desc
  </select>
</mapper>