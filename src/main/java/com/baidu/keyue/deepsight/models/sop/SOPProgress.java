package com.baidu.keyue.deepsight.models.sop;

import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMeta;
import com.baidu.keyue.deepsight.utils.UUIDUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * *@Author: dongjiacheng01
 * *@Description: 快捷场景流程DTO
 * *@Date: 16:25 2025/5/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@Slf4j
public class SOPProgress {

    /**
     * 预置节点名称 - 其他节点
     */
    private static final String NODE_NAME_OTHER = "其他";
    /**
     * 预置节点名称 - 挂断节点
     */
    private static final String NODE_NAME_HANGUP = "挂断";

    private List<SOPStepWithNodes> list;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Builder
    public static class SOPStepWithNodes {

        private String step;

        private List<String> nodes;

        /**
         * 为节点预测算子返回结果添加预置「其他」、「挂断」节点
         */
        public void addPresetNode() {
            nodes.add(NODE_NAME_OTHER);
            nodes.add(NODE_NAME_HANGUP);
        }

        /**
         * 仅添加挂断节点
         */
        public void addHangupNodeOnly(){
            nodes.add(NODE_NAME_HANGUP);
        }

        /**
         * 仅添加其他节点
         */
        public void addOtherNodeOnly() {
            nodes.add(NODE_NAME_OTHER);
        }

    }

    /**
     * 转换为markdown
     */
    public String toMarkDown() {
        StringBuilder sb = new StringBuilder();
        for (SOPStepWithNodes item : list) {
            if (item.getStep() != null) {
                sb.append("# ").append(item.getStep()).append("\n");
                if (item.getNodes() != null) {
                    for (String node : item.getNodes()) {
                        sb.append("## ").append(node).append("\n");
                    }
                }
            }
        }
        return sb.toString();
    }

    /**
     * 校验用户提交的Markdown
     */
    public static void validMarkdown(String markdown) {
        String[] lines = markdown.split("\\R");

        String lastH1 = null;
        boolean h1HasH2 = false;
        boolean hasLine = false;
        Set<String> h2Set = Sets.newHashSet();

        for (String line : lines) {
            String trimmed = line.trim();
            if (StringUtils.isBlank(trimmed)) {
                continue;
            }
            hasLine = true;
            if (trimmed.startsWith("#") && !trimmed.startsWith("##")) {
                if (StringUtils.isNotBlank(lastH1) && !h1HasH2) {
                    throw new DeepSightException.MarkDownH2EmptyException();
                }
                lastH1 = trimmed.substring(1).trim();
                h1HasH2 = false;
                h2Set.clear();
            } else if (trimmed.startsWith("##")) {
                if (StringUtils.isBlank(lastH1)) {
                    throw new DeepSightException.MarkDownH1EmptyException();
                }
                String h2 = trimmed.substring(2).trim();
                if (StringUtils.equalsIgnoreCase(lastH1, h2)) {
                    throw new DeepSightException.MarkDownSameH1AndH2Exception();
                }
                if (h2Set.contains(h2)) {
                    throw new DeepSightException.MarkDownSameH2Exception();
                }
                h1HasH2 = true;
                h2Set.add(h2);
            } else {
                if (StringUtils.isNotBlank(lastH1)) {
                    throw new DeepSightException.MarkDownH2EmptyException();
                }
                throw new DeepSightException.MarkDownH1EmptyException();
            }
        }
        if (!hasLine) {
            throw new DeepSightException.MarkDownH1EmptyException();
        }
        if (StringUtils.isNotBlank(lastH1) && !h1HasH2) {
            throw new DeepSightException.MarkDownH2EmptyException();
        }
    }

    /**
     * 从markdown中构造
     */
    public static SOPProgress convertFromMarkdown(String md) {
        if (StringUtils.isBlank(md)) {
            return new SOPProgress(List.of());
        }
        String[] lines = md.split("\\r?\\n");
        List<SOPStepWithNodes> result = Lists.newArrayList();
        String currentStep = null;
        List<String> currentNodes = Lists.newArrayList();
        for (String line : lines) {
            line = line.trim();
            if (line.startsWith("# ")) {
                if (StringUtils.isNotBlank(currentStep)) {
                    result.add(new SOPStepWithNodes(currentStep, Lists.newArrayList(currentNodes)));
                }
                currentStep = line.substring(2).trim();
                currentNodes.clear();
            } else if (line.startsWith("## ")) {
                currentNodes.add(line.substring(3).trim());
            } else if (!line.isEmpty()) {
                throw new DeepSightException.NodeMarkDownInvalid();
            }
        }
        if (StringUtils.isNotBlank(currentStep)) {
            result.add(new SOPStepWithNodes(currentStep, Lists.newArrayList(currentNodes)));
        }
        return new SOPProgress(result);
    }

    /**
     * 转换为meta列表
     */
    public List<AiobSopMeta> toMetas(String taskId, String rule, String robotVer) {
        List<AiobSopMeta> metaList = Lists.newArrayListWithCapacity(list.size());
        int stepId = 0;
        for (SOPStepWithNodes item : list) {
            // 首节点 添加「其他」预置节点
            if (stepId == 0) {
                item.addOtherNodeOnly();
            }
            // 中间步骤 添加「其他」、「挂断」预置节点
            if (stepId > 0) {
                item.addPresetNode();
            }
            for (int i = 0; i < item.getNodes().size(); i++) {
                String node = item.getNodes().get(i);
                AiobSopMeta meta = new AiobSopMeta();
                meta.setTenantId(WebContextHolder.getTenantId());
                meta.setTaskId(StringUtils.EMPTY);
                meta.setStepId(String.valueOf(stepId));
                meta.setStepName(item.getStep());
                meta.setNodeId(i + UUIDUtils.genUUID());
                meta.setNodeName(node);
                meta.setManualCheck(Boolean.TRUE);
                meta.setVersion(robotVer);
                meta.setTaskRule(rule);
                meta.setDel(Boolean.FALSE);
                meta.setCreator(WebContextHolder.getUserId());
                meta.setModifier(WebContextHolder.getUserId());
                meta.setCreateTime(new Date());
                meta.setUpdateTime(new Date());
                metaList.add(meta);
            }
            stepId++;
        }
        return metaList;
    }

    /**
     * 将meta列表转换为sop
     */
    public static SOPProgress convertFromMetas(List<AiobSopMeta> metaList) {
        Map<String, List<String>> stepToNodeList = Maps.newLinkedHashMap();
        for (AiobSopMeta meta : metaList) {
            if (!stepToNodeList.containsKey(meta.getStepName())) {
                stepToNodeList.put(meta.getStepName(), Lists.newArrayList());
            }
            stepToNodeList.get(meta.getStepName()).add(meta.getNodeName());
        }
        List<SOPStepWithNodes> list = Lists.newArrayListWithCapacity(stepToNodeList.size());
        for (String step : stepToNodeList.keySet()) {
            SOPStepWithNodes node = new SOPStepWithNodes();
            node.setStep(step);
            // 过滤预置节点
            node.setNodes(
                    stepToNodeList.get(step).stream()
                            .filter(s -> !NODE_NAME_OTHER.equals(s))
                            .filter(s -> !NODE_NAME_HANGUP.equals(s))
                            .toList()
            );
            list.add(node);
        }
        return SOPProgress.builder().list(list).build();
    }

}
