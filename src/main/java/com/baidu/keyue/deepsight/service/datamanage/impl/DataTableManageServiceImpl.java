package com.baidu.keyue.deepsight.service.datamanage.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.DataTableStatusEnum;
import com.baidu.keyue.deepsight.enums.DbTypeEnum;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.DeleteStatusEnum;
import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.enums.TableFieldTypeEnum;
import com.baidu.keyue.deepsight.enums.TableFieldValueEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.datamanage.dto.FieldEnumMappingDTO;
import com.baidu.keyue.deepsight.models.datamanage.dto.TableFieldInfoDTO;
import com.baidu.keyue.deepsight.models.datamanage.request.CreateTableSchemaRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.DeleteTableRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.FieldAiMappingRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.FieldShowConfigSaveRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetLLMGenFieldEnumRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetLLMGenTableInfoRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableContentListRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableDetailRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableFieldRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableListRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.FieldShowConfigQueryRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableUserInfoRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.TableRecordRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.UpdateTableFieldConfigRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.UpdateTableSchemaRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.CreateTableResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.FieldShowConfigResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.LLMGenFieldEnumResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.LLMGenFieldResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.TableDetailResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.TableFieldDetailResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.predict.PredictDataSource;
import com.baidu.keyue.deepsight.models.rules.response.DatasetPropertiesResult;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.FieldEncryConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.FieldEncryConfigCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.FieldEncryConfigMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.ai.AiBaseService;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.datamanage.DbConfService;
import com.baidu.keyue.deepsight.service.datamanage.FieldShowConfigService;
import com.baidu.keyue.deepsight.service.dataprediction.DataPredictionService;
import com.baidu.keyue.deepsight.service.rules.RuleManagerService;
import com.baidu.keyue.deepsight.utils.AESUtils;
import com.baidu.keyue.deepsight.utils.FileUtil;
import com.baidu.keyue.deepsight.utils.MobileProcessUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.baidu.keyue.deepsight.enums.DbTypeEnum.getDbEnum;


/**
 * <AUTHOR>
 * @className DataTableMange
 * @description 数据表管理实现类
 * @date 2024/12/27 17:16
 */
@Slf4j
@Service
public class DataTableManageServiceImpl implements DataTableManageService {

    @Autowired
    private DataTableInfoMapper dataTableInfoMapper;

    @Autowired
    private FieldEncryConfigMapper fieldEncryConfigMapper;

    @Autowired
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @Autowired
    private RuleManagerService ruleManagerService;

    @Autowired
    private DataPredictionService dataPredictionService;

    @Autowired
    private DorisService dorisService;

    @Autowired
    @Lazy
    private AccessTokenService accessTokenService;

    @Resource
    private AiBaseService aiBaseService;

    @Autowired
    private DbConfServiceFactory dbConfServiceFactory;

    @Resource
    private FieldShowConfigService fieldShowConfigService;

    @Value("${ai.fieldEnumApi.url}")
    private String genFieldEnumUrl;
    @Value("${ai.token}")
    private String llmToken;

    @Value("${ai.fieldInfoApi.url}")
    private String genFieldInfoUrl;

    @Value("${ai.template.genField}")
    private String genFieldTemplate;

    @Autowired
    private WebClient webClient;

    @Value("${ai.qianfan.url}")
    private String qianFanUrl;

    @Value("${ai.qianfan.authorization}")
    private String qianFanToken;

    @Value("${data-table-manager.default-show-fields-size}")
    private Integer defaultShowSize;
    @Autowired
    @Lazy
    private TableRecordCommonService tableRecordCommonService;


    @Override
    @Transactional
    public CreateTableResponse createTable(CreateTableSchemaRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        // 查询是否存在
        Long dataTableId = request.getDataTableId();
        DataTableInfo dataTableInfo = null;
        if (null != dataTableId) {
            dataTableInfo = validDataTableByTenantId(dataTableId);
        }
        // 表字段重名&主键重复校验
        checkTableField(request.getTableFieldInfos());
        CreateTableResponse response = new CreateTableResponse();
        if (null == dataTableInfo) {
            // 检验是否重名
            if (null != getTableByEnName(tenantId, request.getEnName()) || null != getTableByCnName(tenantId, request.getTableName())) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "数据集存在重复名称");
            }
            // 初始化新建数据表信息
            DataTableInfo tableInfo = initCreateTableInfo(request, tenantId);
            // 创建中
            tableInfo.setDbType(request.getDbType());
            tableInfo.setStatus(DataTableStatusEnum.CREATING.getStatus().byteValue());
            dataTableInfoMapper.insertSelective(tableInfo);
            // 存储表字段信息
            for (int i = 0; i < request.getTableFieldInfos().size(); i++) {
                TableFieldMetaInfo metaInfo = convertTableFieldMetaInfo(request.getTableFieldInfos().get(i));
                metaInfo.setDataTableId(getTableByEnName(tenantId, request.getEnName()).getId());
                metaInfo.setTableEnName(tableInfo.getTableName());
                metaInfo.setNumber(i + 1);
                log.info("metaInfo is {}", JSON.toJSONString(metaInfo));
                tableFieldMetaInfoMapper.insertSelective(metaInfo);
            }
            // 自定义数据集自动增加洞察系统写入时间字段
            addDeepSightDateField(request, tenantId, tableInfo, tableInfo.getDbType());
        } else {
            // 未提交状态才能提交创建
            if (!DataTableStatusEnum.NOT_SUBMIT.getStatus().equals(dataTableInfo.getStatus().intValue())) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "数据集已创建");
            }
            // 检查是否有名称修改
            if (!request.getEnName().equals(dataTableInfo.getEnName())
                    || !request.getTableName().equals(dataTableInfo.getCnName())) {
                // 检验是否重名
                if (null != getTableByEnName(tenantId, request.getEnName()) || null != getTableByCnName(tenantId, request.getTableName())) {
                    throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "数据集存在重复名称");
                }
            }
            // 更新数据表信息
            dataTableInfo.setCnName(request.getTableName());
            dataTableInfo.setEnName(request.getEnName());
            dataTableInfo.setDataType(request.getTableType());
            dataTableInfo.setDescription(request.getDesc());
            dataTableInfo.setTenantid(tenantId);
            dataTableInfo.setDbType(request.getDbType());
            String tableName = request.getEnName() + "_" + tenantId;
            dataTableInfo.setTableName(tableName);
            // 创建中
            dataTableInfo.setStatus(DataTableStatusEnum.CREATING.getStatus().byteValue());
            dataTableInfoMapper.updateByPrimaryKey(dataTableInfo);
            // 存储表字段信息
            for (int i = 0; i < request.getTableFieldInfos().size(); i++) {
                TableFieldMetaInfoCriteria tableFieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
                TableFieldInfoDTO fieldInfo = request.getTableFieldInfos().get(i);
                tableFieldMetaInfoCriteria.createCriteria()
                        .andEnFieldEqualTo(fieldInfo.getEnName())
                        .andDataTableIdEqualTo(dataTableInfo.getId());
                TableFieldMetaInfo metaInfo = convertTableFieldMetaInfo(fieldInfo);
                metaInfo.setDataTableId(dataTableInfo.getId());
                metaInfo.setNumber(i + 1);
                metaInfo.setTableEnName(dataTableInfo.getTableName());
                tableFieldMetaInfoMapper.updateByExampleSelective(metaInfo, tableFieldMetaInfoCriteria);
            }
            // 如果不包含deepsight_datetime字段，添加
            TableFieldMetaInfoCriteria criteria = new TableFieldMetaInfoCriteria();
            criteria.createCriteria().andDataTableIdEqualTo(dataTableInfo.getId())
                    .andEnFieldEqualTo("deepsight_datetime");
            List<TableFieldMetaInfo> tableFieldMetaInfos = tableFieldMetaInfoMapper.selectByExample(criteria);
            if (CollUtil.isEmpty(tableFieldMetaInfos)) {
                addDeepSightDateField(request, tenantId, dataTableInfo, dataTableInfo.getDbType());
            }
        }
        // 建表
        String tableName = request.getEnName() + "_" + tenantId;
        DbConfService confService = dbConfServiceFactory.getDbService(getDbEnum(request.getDbType()).getClassName());
        confService.dbCollInit(request.getTableFieldInfos(), tableName);
        DataTableInfo table = getTableByEnName(tenantId, request.getEnName());
        // 创建完毕，更新状态
        table.setStatus(DataTableStatusEnum.CREATED.getStatus().byteValue());
        dataTableInfoMapper.updateByPrimaryKey(table);
        // 数据接入token生成
        accessTokenService.createTableToken(tableName, Long.valueOf(tenantId));
        // 敏感字段密钥生成
        for (TableFieldInfoDTO fieldInfo : request.getTableFieldInfos()) {
            if (TableFieldTagEnum.SENSITIVE.getCode().equals(fieldInfo.getFieldTag())) {
                FieldEncryConfig fieldEncryConfig = initAesSecret(table.getId(), table.getTableName(), fieldInfo.getEnName());
                fieldEncryConfigMapper.insert(fieldEncryConfig);
            }
        }
        response.setDataTableId(table.getId());
        response.setDataStatus(DataTableStatusEnum.CREATED.getStatus());
        return response;

    }

    /**
     * 添加洞察时间写入字段
     * 写入时间、更新时间
     *
     * @param request
     * @param tenantId
     * @param tableInfo
     */
    public void addDeepSightDateField(CreateTableSchemaRequest request, String tenantId, DataTableInfo tableInfo, String dbType) {
        TableFieldInfoDTO deepSightTime = new TableFieldInfoDTO();
        deepSightTime.setCnName("写入时间");
        if (DbTypeEnum.DORIS_TYPE.getDbType().equals(dbType)) {
            deepSightTime.setDataType("datetime");
        } else {
            deepSightTime.setDataType("date");
        }
        deepSightTime.setDescription("写入时间");
        deepSightTime.setEnName("deepsight_datetime");
        deepSightTime.setFieldTag(0);
        deepSightTime.setFieldType("time");
        deepSightTime.setIsFilterCriteria(true);
        deepSightTime.setIsRequired(false);
        deepSightTime.setValueType("time");
        TableFieldInfoDTO deepSightUpdateTime = new TableFieldInfoDTO();
        deepSightUpdateTime.setCnName("更新时间");
        if (DbTypeEnum.DORIS_TYPE.getDbType().equals(dbType)) {
            deepSightUpdateTime.setDataType("datetime");
        } else {
            deepSightUpdateTime.setDataType("date");
        }
        deepSightUpdateTime.setDescription("更新时间");
        deepSightUpdateTime.setEnName("deepsight_update_datetime");
        deepSightUpdateTime.setFieldTag(0);
        deepSightUpdateTime.setFieldType("time");
        deepSightUpdateTime.setIsFilterCriteria(true);
        deepSightUpdateTime.setIsRequired(false);
        deepSightUpdateTime.setValueType("time");
        TableFieldMetaInfo createTime = convertTableFieldMetaInfo(deepSightTime);
        createTime.setDataTableId(getTableByEnName(tenantId, request.getEnName()).getId());
        createTime.setTableEnName(tableInfo.getTableName());
        createTime.setNumber(request.getTableFieldInfos().size() + 1);
        createTime.setIsVisable(false);
        createTime.setFromBaidu(false);
        TableFieldMetaInfo updateTime = convertTableFieldMetaInfo(deepSightUpdateTime);
        updateTime.setDataTableId(getTableByEnName(tenantId, request.getEnName()).getId());
        updateTime.setTableEnName(tableInfo.getTableName());
        updateTime.setNumber(request.getTableFieldInfos().size() + 2);
        updateTime.setIsVisable(false);
        updateTime.setFromBaidu(false);
        tableFieldMetaInfoMapper.insertSelective(createTime);
        tableFieldMetaInfoMapper.insertSelective(updateTime);
        // 更新时间无默认值，可由构建SQL方法创建
        request.getTableFieldInfos().add(deepSightUpdateTime);
    }

    @Override
    @Transactional
    public void deleteTable(DeleteTableRequest request) {
        // 判断租户下是否存在此数据表
        DataTableInfo info = validDataTableByTenantId(request.getDataTableId());

        // 判断租户下若用到此表作为数据增强则抛出异常
        String tenantId = WebContextHolder.getTenantId();
        String userId = WebContextHolder.getUserId();
        PredictDataSource dataSource = dataPredictionService.getDataSourceDetail(tenantId, userId);
        dataSource.getDataSourceList().stream().forEach(data -> {
            if (data.getDatasetId().equals(request.getDataTableId())) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.FORBIDDEN_REQUEST, "该数据集存在后置依赖-数据增强，请删除依赖后再执行此操作");
            }
        });

        DataTableInfoCriteria criteria = new DataTableInfoCriteria();
        criteria.createCriteria().andIdEqualTo(request.getDataTableId());
        DataTableInfo tableInfo = new DataTableInfo();
        tableInfo.setIsDel(DeleteStatusEnum.DELETE.getStatus().byteValue());
        // 逻辑删除表信息
        dataTableInfoMapper.updateByExampleSelective(tableInfo, criteria);
        // 删除字段信息
        TableFieldMetaInfoCriteria fieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
        fieldMetaInfoCriteria.createCriteria().andDataTableIdEqualTo(info.getId());
        tableFieldMetaInfoMapper.deleteByExample(fieldMetaInfoCriteria);
        DbConfService confService = dbConfServiceFactory.getDbService(getDbEnum(info.getDbType()).getClassName());
        // 删除字段&索引/表
        confService.deleteTable(info.getTableName());
    }

    @Override
    @Transactional
    public CreateTableResponse saveTable(CreateTableSchemaRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        // 查询是否存在
        Long dataTableId = request.getDataTableId();
        DataTableInfo dataTableInfo = null;
        if (null != dataTableId) {
            dataTableInfo = validDataTableByTenantId(dataTableId);
        }
        // 表字段校验
        checkTableField(request.getTableFieldInfos());
        CreateTableResponse response = new CreateTableResponse();
        if (null == dataTableInfo) {
            // 初始化新建数据表信息
            DataTableInfo tableInfo = initCreateTableInfo(request, tenantId);
            // 检验是否重名
            if (null != getTableByEnName(tenantId, request.getEnName()) || null != getTableByCnName(tenantId, request.getTableName())) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "数据集存在重复名称");
            }
            tableInfo.setStatus(DataTableStatusEnum.NOT_SUBMIT.getStatus().byteValue());
            dataTableInfoMapper.insertSelective(tableInfo);
            // 存储表字段信息
            for (int i = 0; i < request.getTableFieldInfos().size(); i++) {
                TableFieldInfoDTO fieldInfo = request.getTableFieldInfos().get(i);
                TableFieldMetaInfo metaInfo = convertTableFieldMetaInfo(fieldInfo);
                metaInfo.setDataTableId(getTableByEnName(tenantId, request.getEnName()).getId());
                metaInfo.setTableEnName(tableInfo.getTableName());
                metaInfo.setNumber(i + 1);
                tableFieldMetaInfoMapper.insertSelective(metaInfo);
            }
        } else {
            // 允许未提交状态的数据集更新
            if (!DataTableStatusEnum.NOT_SUBMIT.getStatus().equals(dataTableInfo.getStatus().intValue())) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "数据集已创建");
            }
            // 检查是否有名称修改
            if (!request.getEnName().equals(dataTableInfo.getEnName())
                    || !request.getTableName().equals(dataTableInfo.getCnName())) {
                // 检验是否重名
                if (null != getTableByEnName(tenantId, request.getEnName()) || null != getTableByCnName(tenantId, request.getTableName())) {
                    throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "数据集存在重复名称");
                }
            }
            // 更新数据表信息
            dataTableInfo.setCnName(request.getTableName());
            dataTableInfo.setEnName(request.getEnName());
            dataTableInfo.setDataType(request.getTableType());
            dataTableInfo.setDescription(request.getDesc());
            dataTableInfo.setTenantid(tenantId);
            String tableName = request.getEnName() + "_" + tenantId;
            dataTableInfo.setTableName(tableName);
            // 创建中
            dataTableInfo.setStatus(DataTableStatusEnum.NOT_SUBMIT.getStatus().byteValue());
            dataTableInfoMapper.updateByPrimaryKey(dataTableInfo);
            // 存储表字段信息
            for (int i = 0; i < request.getTableFieldInfos().size(); i++) {
                TableFieldMetaInfoCriteria tableFieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
                TableFieldInfoDTO fieldInfo = request.getTableFieldInfos().get(i);
                tableFieldMetaInfoCriteria.createCriteria()
                        .andEnFieldEqualTo(fieldInfo.getEnName())
                        .andDataTableIdEqualTo(dataTableInfo.getId());
                TableFieldMetaInfo metaInfo = convertTableFieldMetaInfo(fieldInfo);
                metaInfo.setDataTableId(dataTableInfo.getId());
                metaInfo.setNumber(i + 1);
                metaInfo.setTableEnName(dataTableInfo.getTableName());
                tableFieldMetaInfoMapper.updateByExampleSelective(metaInfo, tableFieldMetaInfoCriteria);
            }
        }
        response.setDataTableId(getTableByEnName(tenantId, request.getEnName()).getId());
        response.setDataStatus(DataTableStatusEnum.NOT_SUBMIT.getStatus());
        return response;
    }

    @Override
    @Transactional
    public void updateTable(UpdateTableSchemaRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        // 存储数据表信息
        DataTableInfo tableInfo = new DataTableInfo();
        tableInfo.setId(request.getDataTableId());
        tableInfo.setCnName(request.getTableName());
        tableInfo.setDescription(request.getDesc());
        tableInfo.setTenantid(tenantId);
        // 创建中
        dataTableInfoMapper.updateByPrimaryKeySelective(tableInfo);
        // 存储表字段信息
        DataTableInfo info = dataTableInfoMapper.selectByPrimaryKey(request.getDataTableId());
        for (int i = 0; i < request.getTableFieldInfos().size(); i++) {
            TableFieldMetaInfoCriteria tableFieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
            TableFieldInfoDTO fieldInfo = request.getTableFieldInfos().get(i);
            tableFieldMetaInfoCriteria.createCriteria()
                    .andEnFieldEqualTo(fieldInfo.getEnName())
                    .andDataTableIdEqualTo(info.getId());
            List<TableFieldMetaInfo> fieldMetaInfos = tableFieldMetaInfoMapper.selectByExample(tableFieldMetaInfoCriteria);
            if (CollectionUtils.isEmpty(fieldMetaInfos)) {
                TableFieldMetaInfo metaInfo = convertTableFieldMetaInfo(fieldInfo);
                metaInfo.setDataTableId(info.getId());
                metaInfo.setTableEnName(info.getTableName());
                metaInfo.setNumber(i + 1);
                tableFieldMetaInfoMapper.insertSelective(metaInfo);
                // 创建完成的表，执行添加字段sql
                if (info.getStatus().equals(DataTableStatusEnum.CREATED.getStatus().byteValue())) {
                    DbConfService confService = dbConfServiceFactory.getDbService(getDbEnum(info.getDbType()).getClassName());
                    confService.dbFieldUpdate(fieldInfo, info.getTableName());
                }
            } else {
                TableFieldMetaInfo metaInfo = new TableFieldMetaInfo();
                metaInfo.setIsFilterCriteria(fieldInfo.getIsFilterCriteria());
                metaInfo.setConfigInfos(JSON.toJSONString(fieldInfo.getConfigInfos()));
                metaInfo.setFieldTag(fieldInfo.getFieldTag());
                metaInfo.setValueType(fieldInfo.getValueType());
                metaInfo.setIsShowValue(fieldInfo.getIsShowValue());
                metaInfo.setNumber(i + 1);
                tableFieldMetaInfoMapper.updateByExampleSelective(metaInfo, tableFieldMetaInfoCriteria);
            }
        }
    }


    @Override
    public BasePageResponse.Page<TableDetailResponse> getDataTableList(GetTableListRequest request) {
        DataTableInfoCriteria dataTableInfoCriteria = new DataTableInfoCriteria();
        DataTableInfoCriteria.Criteria criteria = dataTableInfoCriteria.createCriteria();
        // 租户id
        String tenantId = WebContextHolder.getTenantId();
        criteria.andTenantidEqualTo(tenantId)
                // 只查可见的
                .andIsVisableEqualTo(true)
                .andIsDelEqualTo(DelEnum.NOT_DELETED.getCode());
        // 模糊查询中英文数据集表
        if (StringUtils.isNotEmpty(request.getTableName())) {
            criteria.andCnNameLike("%" + request.getTableName() + "%");
            // 拼接英文名称查询条件
            DataTableInfoCriteria.Criteria enNameCriteria = new DataTableInfoCriteria()
                    .createCriteria()
                    .andEnNameLike("%" + request.getTableName() + "%")
                    .andTenantidEqualTo(String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId()))
                    .andIsVisableEqualTo(true)
                    .andIsDelEqualTo((byte) 0);
            if (null != request.getTableType()) {
                enNameCriteria.andDataTypeEqualTo(request.getTableType());
            }
            dataTableInfoCriteria.or(enNameCriteria);
        }
        // 维度、事实数据
        if (null != request.getTableType()) {
            criteria.andDataTypeEqualTo(request.getTableType());
        }
        long count = dataTableInfoMapper.countByExample(dataTableInfoCriteria);
        if (count == 0) {
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), 0L, Lists.newArrayList());
        }
        // 翻页组件
        PageHelper.startPage(request.getPageNo(), request.getPageSize());
        // 按创建时间降序
        dataTableInfoCriteria.setOrderByClause("create_time desc");
        List<DataTableInfo> dataTableInfos = dataTableInfoMapper.selectByExampleWithBLOBs(dataTableInfoCriteria);
        List<TableDetailResponse> tableDetailRespons = dataTableInfos.stream().map(TableDetailResponse::new).toList();
        return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), count, tableDetailRespons);
    }

    @Override
    public TableDetailResponse getDataTableDetail(GetTableDetailRequest request) {
        DataTableInfo dataTableInfo = validDataTableByTenantId(request.getDataTableId());
        return new TableDetailResponse(dataTableInfo);
    }

    @Override
    public BasePageResponse.Page<TableFieldDetailResponse> getTableFieldList(GetTableFieldRequest request,
                                                                             Boolean includeBaidu) {
        // 判断租户下是否存在此数据表
        if (null != request.getDataTableId()) {
            validDataTableByTenantId(request.getDataTableId());
        }
        TableFieldMetaInfoCriteria tableFieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
        TableFieldMetaInfoCriteria.Criteria criteria = tableFieldMetaInfoCriteria.createCriteria();
        if (null == request.getDataTableId() && ("user").equals(request.getTableType())) {
            criteria.andTableEnNameEqualTo("mock_user_" + WebContextHolder.getTenantId()).andIsVisableEqualTo(true);
        } else {
            criteria.andDataTableIdEqualTo(request.getDataTableId()).andIsVisableEqualTo(true);
        }
        if (!includeBaidu) {
            // 如果要求排除百度字段，则过滤掉表中from_baidu=true的记录
            criteria.andFromBaiduEqualTo(Boolean.FALSE);
        }
        long count = tableFieldMetaInfoMapper.countByExample(tableFieldMetaInfoCriteria);
        if (count == 0) {
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), 0L, Lists.newArrayList());
        }
        // 翻页组件
        PageHelper.startPage(request.getPageNo(), request.getPageSize());
        tableFieldMetaInfoCriteria.setOrderByClause("number ASC");
        List<TableFieldMetaInfo> tableFieldMetaInfos =
                tableFieldMetaInfoMapper.selectByExampleWithBLOBs(tableFieldMetaInfoCriteria);
        List<TableFieldDetailResponse> tableFieldDetailRespPage = tableFieldMetaInfos.stream().map(TableFieldDetailResponse::new).toList();
        return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), count, tableFieldDetailRespPage);
    }

    @Override
    public List<DatasetPropertiesResult> getTableContentFilterProperties(Long dataTableId, Boolean includeBaidu) {
        // 判断租户下是否存在此数据表
        validDataTableByTenantId(dataTableId);

        List<DatasetPropertiesResult> datasetPropertiesResult = ruleManagerService
                .getDatasetPropertiesResult(dataTableId, includeBaidu);

        // 数据集管理的筛选不支持聚合操作
        return datasetPropertiesResult.stream()
                .map(properties -> {
                    properties.setIsMeasure(Boolean.FALSE);
                    properties.setIsIdKey(Boolean.FALSE);
                    return properties;
                }).toList();
    }

    @Override
    public List<DatasetPropertiesResult> getTableProperties() {

        String tenantId = WebContextHolder.getTenantId();
        // 获取当前租户的用户表
        DataTableInfoCriteria dataTableInfoCriteria = new DataTableInfoCriteria();
        DataTableInfoCriteria.Criteria criteria = dataTableInfoCriteria.createCriteria();
        criteria.andTenantidEqualTo(tenantId).andTableNameEqualTo(String.format("%s_%s", Constants.DORIS_DEFAULT_LABEL_TABLE, tenantId))
                .andIsVisableEqualTo(true);

        // 按创建时间降序
        dataTableInfoCriteria.setOrderByClause("create_time desc");
        List<DataTableInfo> dataTableInfos = dataTableInfoMapper.selectByExampleWithBLOBs(dataTableInfoCriteria);
        if (CollectionUtils.isEmpty(dataTableInfos)) {
            return new ArrayList<>();
        }
        DataTableInfo dataTableInfo = dataTableInfos.get(0);
        Long dataTableId = dataTableInfo.getId();
        return this.getTableContentFilterProperties(dataTableId, true);
    }

    @Override
    public BasePageResponse.Page<Map<String, String>>  getTableUserInfo( Boolean includeBaidu,
                                                                         GetTableUserInfoRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        // 获取当前租户的用户表
        DataTableInfoCriteria dataTableInfoCriteria = new DataTableInfoCriteria();
        DataTableInfoCriteria.Criteria criteria = dataTableInfoCriteria.createCriteria();
        criteria.andTenantidEqualTo(tenantId).andTableNameEqualTo(String.format("%s_%s", Constants.DORIS_DEFAULT_LABEL_TABLE, tenantId))
                .andIsVisableEqualTo(true);

        // 按创建时间降序
        dataTableInfoCriteria.setOrderByClause("create_time desc");
        List<DataTableInfo> dataTableInfos = dataTableInfoMapper.selectByExampleWithBLOBs(dataTableInfoCriteria);
        if (CollectionUtils.isEmpty(dataTableInfos)) {
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), 0L, Lists.newArrayList());
        }
        DataTableInfo dataTableInfo = dataTableInfos.get(0);
        Long dataTableId = dataTableInfo.getId();

        // 获取当前用户字段Id
        TableFieldMetaInfoCriteria tableFieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
        TableFieldMetaInfoCriteria.Criteria tableCriteria = tableFieldMetaInfoCriteria.createCriteria();
        tableCriteria.andDataTableIdEqualTo(dataTableId)
                .andTableEnNameEqualTo(String.format("%s_%s",
                        Constants.DORIS_DEFAULT_LABEL_TABLE, tenantId))
                    .andIsVisableEqualTo(true).andEnFieldEqualTo(request.getUserFieldName()).andIsVisableEqualTo(true);
        // 翻页组件
        List<TableFieldMetaInfo> tableFieldMetaInfos =
                tableFieldMetaInfoMapper.selectByExampleWithBLOBs(tableFieldMetaInfoCriteria);

        if (CollectionUtils.isEmpty(tableFieldMetaInfos)){
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), 0L, Lists.newArrayList());
        }
        TableFieldMetaInfo tableFieldMetaInfo = tableFieldMetaInfos.get(0);

        GetTableContentListRequest infoRequest = GetTableContentListRequest.buildExternalRequest(tableFieldMetaInfo.getId(), request.getUserFieldValue(), dataTableId);

        DbConfService confService = dbConfServiceFactory.getDbService(getDbEnum(dataTableInfo.getDbType()).getClassName());
        BasePageResponse.Page<Map<String, String>> search = confService.search(infoRequest, includeBaidu);
        if (CollectionUtils.isEmpty(search.getResults())) {
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), 0L, Lists.newArrayList());
        }

        Map<String, String> encryptFields = tableRecordCommonService.getEncryptFields(dataTableId);
        List<Map<String, String>> resultMap = search.getResults();
        for (Map<String, String> row : resultMap) {
            // 判断是否存在 key，若存在则解密 value 替换回原 Map 中
            String encryptedMobile = row.get(Constants.TABLE_MOBILE_FIELD);
            if (StringUtils.isNotBlank(encryptedMobile)) {
                String decrypted = MobileProcessUtils.decryptMobile(encryptedMobile, encryptFields);
                row.put(Constants.TABLE_MOBILE_FIELD, decrypted);
            }
        }
        return search;
    }

    /**
     * 判断租户下是否存在此数据表
     */
    @Override
    public DataTableInfo validDataTableByTenantId(Long dataTableId) {
        String tenantId = WebContextHolder.getTenantId();
        DataTableInfoCriteria dataTableInfoCriteria = new DataTableInfoCriteria();
        DataTableInfoCriteria.Criteria criteria = dataTableInfoCriteria.createCriteria();
        criteria.andTenantidEqualTo(tenantId)
                .andIdEqualTo(dataTableId)
                .andIsVisableEqualTo(true)
                .andIsDelEqualTo(DelEnum.NOT_DELETED.getCode());
        List<DataTableInfo> res = dataTableInfoMapper.selectByExampleWithBLOBs(dataTableInfoCriteria);
        if (CollectionUtils.isEmpty(res)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND, "无此数据表");
        }
        return res.get(0);
    }

    /**
     * 判断租户下是否存在此数据表
     */
    @Override
    public DataTableInfo validDataTableByTableName(String tableEnName) {
        String tenantId = WebContextHolder.getTenantId();
        return validDataTableByTableName(tableEnName, tenantId);
    }

    @Override
    public DataTableInfo validDataTableByTableName(String tableEnName, String tenantId) {
        DataTableInfoCriteria dataTableInfoCriteria = new DataTableInfoCriteria();
        DataTableInfoCriteria.Criteria criteria = dataTableInfoCriteria.createCriteria();
        criteria.andTenantidEqualTo(tenantId)
                .andTableNameEqualTo(tableEnName)
                .andIsVisableEqualTo(true)
                .andIsDelEqualTo(DelEnum.NOT_DELETED.getCode());
        List<DataTableInfo> res = dataTableInfoMapper.selectByExampleWithBLOBs(dataTableInfoCriteria);
        if (CollectionUtils.isEmpty(res)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "无此数据表");
        }
        return res.get(0);
    }

    @Override
    public DataTableInfo getTableDetailWithTableName(String tableName) {
        DataTableInfoCriteria dataTableInfoCriteria = new DataTableInfoCriteria();
        DataTableInfoCriteria.Criteria criteria = dataTableInfoCriteria.createCriteria();
        criteria.andTableNameEqualTo(tableName)
                .andIsDelEqualTo(DelEnum.NOT_DELETED.getCode());
        List<DataTableInfo> res = dataTableInfoMapper.selectByExample(dataTableInfoCriteria);
        if (CollectionUtils.isEmpty(res)) {
            return null;
        }
        return res.get(0);
    }

    @Override
    public BasePageResponse.Page<Map<String, String>> getTableContent(GetTableContentListRequest request,
                                                                      Boolean includeBaidu) {
        // 判断租户下是否存在此数据表
        DataTableInfo dataTableInfo = validDataTableByTenantId(request.getDataTableId());
        if (!DataTableStatusEnum.CREATED.getStatus().equals(dataTableInfo.getStatus().intValue())) {
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), 0L, null);
        }
        DbConfService confService = dbConfServiceFactory.getDbService(getDbEnum(dataTableInfo.getDbType()).getClassName());
        return confService.search(request, includeBaidu);
    }


    /**
     * 查询数据集对应的加密字段名
     */
    @Override
    public List<String> getTableEncryFields(Long dataTableId) {
        List<String> fieldEncrys = new ArrayList<>();
        // 敏感字段加密返回
        FieldEncryConfigCriteria fieldEncryConfigCriteria = new FieldEncryConfigCriteria();
        FieldEncryConfigCriteria.Criteria criteria = fieldEncryConfigCriteria.createCriteria();
        criteria.andDataTableIdEqualTo(Long.toString(dataTableId));
        List<FieldEncryConfig> fieldEncryConfigs = fieldEncryConfigMapper.selectByExample(fieldEncryConfigCriteria);
        if (CollectionUtils.isNotEmpty(fieldEncryConfigs)) {
            fieldEncrys = fieldEncryConfigs.stream().map(field -> field.getEnField()).collect(Collectors.toList());
        }
        return fieldEncrys;
    }


    @Override
    @Transactional(value = "deepSightTransactionManager")
    @CacheEvict(value = "tableFieldMetaInfo", cacheManager = "caffeineCacheManager", allEntries = true)
    public void updateTableFieldConfig(UpdateTableFieldConfigRequest request) {
        // 判断租户下是否存在此数据表
        validDataTableByTenantId(request.getDataTableId());
        TableFieldMetaInfo info = queryTableFieldMetaInfo(request.getDataTableId(), request.getEnName());
        if (ObjectUtils.isEmpty(info)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "数据表无此字段");
        }
        // 更新字段标记
        if (null != request.getFieldTag()) {
            validateFieldType(info.getFieldType(), request.getFieldTag());
            info.setFieldTag(request.getFieldTag());
        }
        // 更新筛选标记
        if (null != request.getIsFilterCriteria()) {
            info.setIsFilterCriteria(request.getIsFilterCriteria());
        }
        // 更新取值类型
        if (null != request.getValueType()) {
            info.setValueType(request.getValueType());
        }
        // 更新筛选标记
        if (null != request.getIsFilterCriteria()) {
            info.setIsFilterCriteria(request.getIsFilterCriteria());
        }
        // 更新高级配置
        if (!CollectionUtils.isEmpty(request.getConfigInfos())) {
            // 检查key是否重复
            checkDuplicateKeys(request.getConfigInfos());
            info.setConfigInfos(JSON.toJSONString(request.getConfigInfos()));
        }
        if (TableFieldValueEnum.ENUM.getValueType().equals(info.getValueType())) {
            info.setIsShowValue(request.getIsShowValue());
        }
        TableFieldMetaInfoCriteria criteria = new TableFieldMetaInfoCriteria();
        criteria.createCriteria().andDataTableIdEqualTo(request.getDataTableId()).andEnFieldEqualTo(request.getEnName());
        tableFieldMetaInfoMapper.updateByExampleSelective(info, criteria);
    }

    /**
     * 判断fieldType和fieldTag合法性
     *
     * @param fieldType
     * @param fieldTag
     */
    public void validateFieldType(String fieldType, Integer fieldTag) {
        List<TableFieldTagEnum> fieldTags = TableFieldTypeEnum.getFieldTag(fieldType);
        if (CollectionUtils.isEmpty(fieldTags) || !fieldTags.contains(TableFieldTagEnum.getByCode(fieldTag))) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "数据类型与字段标记不匹配");
        }
        // 不允许主键字段变更成其他字段类型
        if (TableFieldTagEnum.PRIMARY.getCode().equals(fieldTag)
                && !TableFieldTagEnum.PRIMARY.getCode().equals(fieldTag)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "主键字段不允许变更");
        }
    }

    /**
     * 判断key是否重复
     *
     * @param configInfos
     */
    public void checkDuplicateKeys(List<Map<String, String>> configInfos) {
        Set<String> seenKeys = new HashSet<>();
        for (Map<String, String> configInfo : configInfos) {
            if (!seenKeys.add(configInfo.get("key"))) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "存在重复的key");
            }
        }
    }

    @Override
    public TableFieldMetaInfo queryTableFieldMetaInfo(Long dataTableId, String enName) {
        TableFieldMetaInfoCriteria criteria = new TableFieldMetaInfoCriteria();
        criteria.createCriteria().andDataTableIdEqualTo(dataTableId).andEnFieldEqualTo(enName);
        List<TableFieldMetaInfo> info = tableFieldMetaInfoMapper.selectByExample(criteria);
        return CollectionUtils.isEmpty(info) ? null : info.get(0);
    }

    @Override
    @Cacheable(value = "tableFieldMetaInfo", cacheManager = "caffeineCacheManager", keyGenerator = "customKeyGenerator")
    public List<TableFieldMetaInfo> queryTableFieldsMetaInfo(Long dataTableId) {
        TableFieldMetaInfoCriteria criteria = new TableFieldMetaInfoCriteria();
        criteria.createCriteria().andDataTableIdEqualTo(dataTableId);
        List<TableFieldMetaInfo> info = tableFieldMetaInfoMapper.selectByExample(criteria);
        return CollectionUtils.isEmpty(info) ? null : info;
    }

    /**
     * 查询表对应类型的字段
     */
    @Override
    public TableFieldMetaInfo queryTableFieldByTag(Long dataTableId, TableFieldTagEnum tag) {
        TableFieldMetaInfoCriteria criteria = new TableFieldMetaInfoCriteria();
        criteria.createCriteria().andDataTableIdEqualTo(dataTableId).andFieldTagEqualTo(tag.getCode());
        List<TableFieldMetaInfo> info = tableFieldMetaInfoMapper.selectByExample(criteria);
        return CollectionUtils.isEmpty(info) ? null : info.get(0);
    }

    @Override
    public FieldShowConfigResponse getFieldShowConfig(String tenantId, FieldShowConfigQueryRequest request) {
        Long dataTableId = request.getDataTableId();
        validDataTableByTenantId(dataTableId);
        FieldShowConfigResponse response = fieldShowConfigService.getByTableIdAndTenantIdAndPageId(dataTableId, tenantId);
        // 如果没有配置，取可见字段默认，展示前几个
        if (response == null) {
            List<VisibleFieldResponse> visibleFields = getVisibleFields(dataTableId, false);
            response = new FieldShowConfigResponse(dataTableId, visibleFields);
            // 显示列表做截断，主要做默认展示使用
            response.subConfigSize(defaultShowSize);
        }
        return response;
    }

    @Override
    public FieldShowConfigResponse saveOrUpdateFieldShowConfig(String tenantId, FieldShowConfigSaveRequest request, String userName) {
        Long dataTableId = request.getDataTableId();
        validDataTableByTenantId(dataTableId);
        // 校验显示列字段准确性:只能配置数据集非百度的可见字段
        List<VisibleFieldResponse> visibleFields = getVisibleFields(dataTableId, false);
        Set<String> visibleFieldsSet = visibleFields.stream().map(VisibleFieldResponse::getEnName).collect(Collectors.toSet());
        List<String> fields = Lists.newArrayList(request.getShowFields());
        fields.removeAll(visibleFieldsSet);
        Assert.isTrue(CollUtil.isEmpty(fields),
                () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "字段不存在于数据集"));
        return fieldShowConfigService.saveOrUpdateById(request, tenantId, userName);
    }


    @Override
    public void opTableData(TableRecordRequest request) {
        DataTableInfo tableInfo = validDataTableByTenantId(request.getDataTableId());
        // 查询表对应的主键名称
        TableFieldMetaInfo metaInfo = queryTableFieldByTag(request.getDataTableId(), TableFieldTagEnum.PRIMARY);
        if (null == metaInfo.getEnField()) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "请检查主键字段");
        }
        List<String> idList = request.getIds();
        DbConfService confService = dbConfServiceFactory.getDbService(getDbEnum(tableInfo.getDbType()).getClassName());
        confService.delete(tableInfo.getTableName(), metaInfo.getEnField(), idList);
    }

    @Override
    public LLMGenFieldEnumResponse llmGenFieldEnum(@NotNull GetLLMGenFieldEnumRequest request) {
        LLMGenFieldEnumResponse res = new LLMGenFieldEnumResponse();
        DataTableInfo tableInfo = dataTableInfoMapper.selectByPrimaryKey(request.getDataTableId());
        // 枚举最多提取100个
        TableFieldMetaInfo fieldMetaInfo = queryTableFieldMetaInfo(request.getDataTableId(), request.getEnName());
        if (fieldMetaInfo.getDataType().equals(Constants.CONSTANT_JSON)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "json类型不支持枚举提取");
        }
        String sql;
        if (fieldMetaInfo.getDataType().equals(Constants.CONSTANT_ARRAY)) {
            sql = String.format("SELECT array_distinct(%s) FROM %s LIMIT 20;",
                    StringEscapeUtils.escapeSql(request.getEnName()), StringEscapeUtils.escapeSql(tableInfo.getTableName()));
        } else {
            sql = String.format("SELECT DISTINCT %s FROM %s LIMIT 20;",
                    StringEscapeUtils.escapeSql(request.getEnName()), StringEscapeUtils.escapeSql(tableInfo.getTableName()));
        }
        List<Map<String, Object>> sqlRes = dorisService.selectList(sql);
        if (sqlRes.isEmpty()) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "数据字段为空，枚举提取失败");
        }
        List<String> values = sqlRes.stream()
                .flatMap(map -> map.values().stream())
                .map(Object::toString)
                .collect(Collectors.toList());
        JSONObject json = new JSONObject();
        json.put("字段取值", values);
        json.put("字段名称", request.getEnName());
        json.put("字段描述", fieldMetaInfo.getDescription());
        List<FieldEnumMappingDTO> llmRes = aiBaseService.genFieldEnum(json.toString(), "configInfos");
        res.setConfigInfos(llmRes);
        return res;
    }

    @Override
    public Flux<ServerSentEvent<String>> llmGenFieldInfo(GetLLMGenTableInfoRequest request) {
        String aiReqTemplate = null;
        try {
            aiReqTemplate = FileUtil.readFileAsString(genFieldTemplate);
        } catch (IOException e) {
            log.error("aiReqTemplate is null!", e);
        }
        Assert.notBlank(aiReqTemplate, () -> new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "AI解析异常"));
        String reqStr = String.format(aiReqTemplate, request.getPromptContent());
        FieldAiMappingRequest rrr = new FieldAiMappingRequest(new FieldAiMappingRequest.Messages(reqStr));
        rrr.setStream(true);
        AtomicReference<StringBuilder> fullContent = new AtomicReference<>(new StringBuilder());

        return webClient.post()
                .uri(qianFanUrl)
                .header("Authorization", qianFanToken)
                .bodyValue(JSON.toJSONString(rrr))
                .retrieve()
                .bodyToFlux(String.class)
                .takeUntil(line -> ("[DONE]").equals(line))
                .filter(line -> !("[DONE]").equals(line))
                .map(data -> {
                    ObjectNode json = null;
                    try {
                        json = (ObjectNode) new ObjectMapper().readTree(data);
                    } catch (JsonProcessingException e) {
                        e.printStackTrace();
                    }
                    fullContent.get().append(json.get("choices").get(0).get("delta").get("content"));              // 累加
                    return ServerSentEvent.builder(json.get("choices").get(0).get("delta").get("content").toString()).build();
                })
                .doOnComplete(() -> System.out.println(fullContent.get().toString()));
    }

    @Override
    public LLMGenFieldResponse llmGenFieldInfoV1(GetLLMGenTableInfoRequest request) {
        LLMGenFieldResponse res = new LLMGenFieldResponse();
        List<TableFieldInfoDTO> tableFieldInfoDTOS = aiBaseService.genFieldInfo(request.getPromptContent(), "tableFieldInfos");
        tableFieldInfoDTOS.forEach(tableFieldInfo -> {
            tableFieldInfo.setIsShowValue(true);
            tableFieldInfo.setIsRequired(true);
            tableFieldInfo.setFieldTag(TableFieldTagEnum.NULL.getCode());
            tableFieldInfo.setIsFilterCriteria(true);
            tableFieldInfo.setValueType("text");
            tableFieldInfo.setDataType("varchar");
        });
        res.setTableFieldInfos(tableFieldInfoDTOS);
        return res;
    }

    /**
     * 获取可见字段
     */
    @Override
    public List<VisibleFieldResponse> getVisibleFields(Long dataTableId, Boolean includeBaidu) {
        // 判断租户下是否存在此数据表
        validDataTableByTenantId(dataTableId);

        TableFieldMetaInfoCriteria tableFieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
        TableFieldMetaInfoCriteria.Criteria criteria = tableFieldMetaInfoCriteria.createCriteria();
        criteria.andDataTableIdEqualTo(dataTableId)
                .andIsVisableEqualTo(true);
        if (!includeBaidu) {
            // 如果要求排除百度字段，则过滤掉表中from_baidu=true的记录
            criteria.andFromBaiduEqualTo(Boolean.FALSE);
        }
        tableFieldMetaInfoCriteria.setOrderByClause("number ASC");
        List<TableFieldMetaInfo> tableFieldMetaInfos = tableFieldMetaInfoMapper
                .selectByExampleWithBLOBs(tableFieldMetaInfoCriteria);

        return tableFieldMetaInfos.stream().map(VisibleFieldResponse::convertFrom).toList();
    }

    @Override
    public List<VisibleFieldResponse> getVisibleFields(String tableEnName, String tenantId, Boolean includeBaidu) {
        // 优先使用传入的租户ID（注意防止越权），否则使用上下文租户ID
        if (StrUtil.isNotBlank(tenantId)) {
            validDataTableByTableName(tableEnName, tenantId);
        } else {
            validDataTableByTableName(tableEnName);
        }
        TableFieldMetaInfoCriteria tableFieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
        TableFieldMetaInfoCriteria.Criteria criteria = tableFieldMetaInfoCriteria.createCriteria();
        criteria.andTableEnNameEqualTo(tableEnName)
                .andIsVisableEqualTo(true);
        if (!includeBaidu) {
            // 如果要求排除百度字段，则过滤掉表中from_baidu=true的记录
            criteria.andFromBaiduEqualTo(Boolean.FALSE);
        }
        List<TableFieldMetaInfo> tableFieldMetaInfos = tableFieldMetaInfoMapper
                .selectByExampleWithBLOBs(tableFieldMetaInfoCriteria);

        return tableFieldMetaInfos.stream().map(VisibleFieldResponse::new).toList();
    }

    @Override
    public List<VisibleFieldResponse> getVisibleFields(String tableEnName, Boolean includeBaidu) {
        return getVisibleFields(tableEnName, null, includeBaidu);
    }

    /**
     * 根据租户&英文名称获取表
     */
    public DataTableInfo getTableByEnName(String tenantId, String enName) {
        DataTableInfoCriteria dataTableInfoCriteria = new DataTableInfoCriteria();
        dataTableInfoCriteria.createCriteria()
                .andTenantidEqualTo(tenantId)
                .andIsDelEqualTo(DeleteStatusEnum.NORMAL.getStatus().byteValue())
                .andEnNameEqualTo(enName);
        List<DataTableInfo> dataTableInfos = dataTableInfoMapper.selectByExample(dataTableInfoCriteria);
        DataTableInfo dataTableInfo = CollectionUtils.isEmpty(dataTableInfos) ? null : dataTableInfos.get(0);
        return dataTableInfo;
    }
    public TableFieldMetaInfo getTableFieldByEnName(Long dataTableId, String enName) {
        TableFieldMetaInfoCriteria tableFieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
        tableFieldMetaInfoCriteria.createCriteria()
                .andDataTableIdEqualTo(Long.valueOf(dataTableId))
                .andEnFieldEqualTo(enName);
        List<TableFieldMetaInfo> fieldMetaInfos = tableFieldMetaInfoMapper.selectByExample(tableFieldMetaInfoCriteria);
        TableFieldMetaInfo fieldMetaInfo = CollectionUtils.isEmpty(fieldMetaInfos) ? null : fieldMetaInfos.get(0);
        return fieldMetaInfo;
    }
    /**
     * 根据租户&中文名称获取表
     */
    public DataTableInfo getTableByCnName(String tenantId, String cnName) {
        DataTableInfoCriteria dataTableInfoCriteria = new DataTableInfoCriteria();
        dataTableInfoCriteria.createCriteria()
                .andTenantidEqualTo(tenantId)
                .andIsDelEqualTo(DeleteStatusEnum.NORMAL.getStatus().byteValue())
                .andCnNameEqualTo(cnName);
        List<DataTableInfo> dataTableInfos = dataTableInfoMapper.selectByExample(dataTableInfoCriteria);
        DataTableInfo dataTableInfo = CollectionUtils.isEmpty(dataTableInfos) ? null : dataTableInfos.get(0);
        return dataTableInfo;
    }

    /**
     * 表字段校验
     */
    public void checkTableField(List<TableFieldInfoDTO> fieldInfos) {
        long cnNameCount = fieldInfos.stream()
                .map(TableFieldInfoDTO::getCnName)
                .distinct()
                .count();
        long enNameCount = fieldInfos.stream()
                .map(TableFieldInfoDTO::getEnName)
                .distinct()
                .count();
        if (cnNameCount != enNameCount || cnNameCount != fieldInfos.size()) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "表字段存在重复名称");
        }
        // 表字段标记与类型匹配校验
        fieldInfos.stream().forEach(fieldInfo -> validateFieldType(fieldInfo.getFieldType(), fieldInfo.getFieldTag()));
        // 表字段重复主键校验
        long primaryKeyCnt = fieldInfos.stream()
                .filter(fieldInfo -> TableFieldTagEnum.PRIMARY.getCode().equals(fieldInfo.getFieldTag()))
                .count();
        if (primaryKeyCnt > 1) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "表字段存在重复主键");
        }
    }

    /**
     * 敏感字段密钥初始化
     */
    public FieldEncryConfig initAesSecret(Long dataTableId, String tableName, String enField) {
        FieldEncryConfig config = new FieldEncryConfig();
        String password = AESUtils.generatorAesPassword();
        config.setSecretKey(password);
        config.setDataTableId(String.valueOf(dataTableId));
        config.setTableEnName(tableName);
        config.setEnField(enField);
        config.setCreateTime(new Date());
        return config;

    }

    private TableFieldMetaInfo convertTableFieldMetaInfo(TableFieldInfoDTO fieldInfoDTO) {
        TableFieldMetaInfo fieldMetaInfo = new TableFieldMetaInfo();
        BeanUtils.copyProperties(fieldInfoDTO, fieldMetaInfo);
        fieldMetaInfo.setCreateTime(new Date());
        fieldMetaInfo.setUpdateTime(new Date());
        fieldMetaInfo.setEnField(fieldInfoDTO.getEnName());
        fieldMetaInfo.setCnField(fieldInfoDTO.getCnName());
        fieldMetaInfo.setIsRequired(fieldInfoDTO.getIsRequired());
        fieldMetaInfo.setIsFilterCriteria(fieldInfoDTO.getIsFilterCriteria());
        fieldMetaInfo.setIsVisable(true);
        fieldMetaInfo.setIsShowValue(fieldInfoDTO.getIsShowValue());
        if (TableFieldTagEnum.SENSITIVE.getCode().equals(fieldInfoDTO.getFieldTag())) {
            fieldMetaInfo.setIsSecrete(true);
        } else {
            fieldMetaInfo.setIsSecrete(false);
        }
        fieldMetaInfo.setConfigInfos(JSON.toJSONString(fieldInfoDTO.getConfigInfos()));
        return fieldMetaInfo;
    }

    private DataTableInfo initCreateTableInfo(CreateTableSchemaRequest request, String tenantId) {
        DataTableInfo tableInfo = new DataTableInfo();
        tableInfo.setCnName(request.getTableName());
        tableInfo.setEnName(request.getEnName());
        tableInfo.setDataType(request.getTableType());
        tableInfo.setDescription(request.getDesc());
        tableInfo.setDbType(request.getDbType());
        tableInfo.setTenantid(tenantId);
        String tableName = request.getEnName() + "_" + tenantId;
        tableInfo.setTableName(tableName);
        tableInfo.setIsDel(DeleteStatusEnum.NORMAL.getStatus().byteValue());
        tableInfo.setIsVisable(true);
        tableInfo.setIsPreset((byte) 0);
        tableInfo.setCreateTime(new Date());
        tableInfo.setUpdateTime(new Date());
        return tableInfo;
    }


}
