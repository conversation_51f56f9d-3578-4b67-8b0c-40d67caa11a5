# Doris JSON函数修复说明

## 问题描述

在执行小秘书识别的`checkPreconditions`方法时，出现以下错误：

```
ERROR [N/A] c.b.k.d.database.service.impl.DorisServiceImpl[selectList:114] - Error executing SQL:
java.sql.SQLException: errCode = 2, detailMessage = can not cast from origin type json to target type=double
```

## 问题原因分析

### 1. Doris JSON函数特性

根据Apache Doris官方文档，`JSON_EXTRACT`函数返回的是**JSON类型**，不能直接与其他数据类型进行比较。

**错误的用法**：
```sql
JSON_EXTRACT(nodeInfo, '$.hungUpType') = 6  -- ❌ JSON类型不能直接与数字比较
```

### 2. 正确的解决方案

Doris提供了专门的类型提取函数：

- `json_extract_string()` - 提取字符串类型
- `json_extract_int()` - 提取整数类型  
- `json_extract_double()` - 提取浮点数类型
- `json_extract_bool()` - 提取布尔类型

## 修复方案

### 修改前的SQL（错误）

```sql
SELECT nodeInfo 
FROM aiob_conversation_record_xxx 
WHERE (
    sessionId = 'xxx' 
    AND nodeInfo IS NOT NULL 
    AND nodeInfo != ''
    AND JSON_EXTRACT(nodeInfo, '$.systemEvent') = 'hungUp'     -- ❌ 类型错误
    AND JSON_EXTRACT(nodeInfo, '$.hungUpType') = 6             -- ❌ 类型错误
) 
ORDER BY createTime DESC 
LIMIT 1
```

### 修改后的SQL（正确）

```sql
SELECT nodeInfo 
FROM aiob_conversation_record_xxx 
WHERE (
    sessionId = 'xxx' 
    AND nodeInfo IS NOT NULL 
    AND nodeInfo != ''
    AND json_extract_string(nodeInfo, '$.systemEvent') = 'hungUp'  -- ✅ 使用字符串提取函数
    AND json_extract_int(nodeInfo, '$.hungUpType') = 6             -- ✅ 使用整数提取函数
) 
ORDER BY createTime DESC 
LIMIT 1
```

## 具体修改内容

### 1. ORMUtils.java

**修改位置**：`generateQueryRecordNodeInfoForHangupSQL`方法

```java
// 修改前
conditions.add(condition("JSON_EXTRACT(nodeInfo, '$.systemEvent') = 'hungUp'"));
conditions.add(condition("JSON_EXTRACT(nodeInfo, '$.hungUpType') = 6"));

// 修改后  
conditions.add(condition("json_extract_string(nodeInfo, '$.systemEvent') = 'hungUp'"));
conditions.add(condition("json_extract_int(nodeInfo, '$.hungUpType') = 6"));
```

### 2. 测试文件更新

更新了所有测试文件中的mock SQL，使用正确的函数名称。

## Doris JSON函数对比

| 函数名 | 返回类型 | 用途 | 示例 |
|--------|----------|------|------|
| `JSON_EXTRACT` | JSON | 通用提取，返回JSON类型 | `JSON_EXTRACT(j, '$.key')` |
| `json_extract_string` | STRING | 提取字符串值 | `json_extract_string(j, '$.name')` |
| `json_extract_int` | INT | 提取整数值 | `json_extract_int(j, '$.age')` |
| `json_extract_double` | DOUBLE | 提取浮点数值 | `json_extract_double(j, '$.price')` |
| `json_extract_bool` | BOOLEAN | 提取布尔值 | `json_extract_bool(j, '$.active')` |

## 验证方法

可以通过以下SQL验证修复效果：

```sql
-- 测试字符串提取
SELECT json_extract_string('{"systemEvent":"hungUp"}', '$.systemEvent') = 'hungUp';
-- 应该返回: 1 (true)

-- 测试整数提取  
SELECT json_extract_int('{"hungUpType":6}', '$.hungUpType') = 6;
-- 应该返回: 1 (true)

-- 错误示例（会报错）
SELECT JSON_EXTRACT('{"hungUpType":6}', '$.hungUpType') = 6;
-- 会报错: can not cast from origin type json to target type=double
```

## 注意事项

### 1. 数据类型匹配

- 确保JSON中的字段类型与提取函数匹配
- `systemEvent`是字符串，使用`json_extract_string`
- `hungUpType`是数字，使用`json_extract_int`

### 2. 异常处理

- 如果字段不存在，这些函数会返回NULL
- 如果类型不匹配，会尝试转换，失败则返回NULL

### 3. 性能考虑

- 专用类型提取函数比通用`JSON_EXTRACT`性能更好
- 避免了类型转换的开销

## 总结

这次修复解决了Doris JSON函数使用不当导致的类型转换错误。通过使用正确的类型专用提取函数：

- ✅ 解决了SQL执行错误
- ✅ 提高了查询性能  
- ✅ 代码更加规范和可靠
- ✅ 符合Doris最佳实践

修复后的代码现在可以正确执行小秘书识别的前置条件检查了。
