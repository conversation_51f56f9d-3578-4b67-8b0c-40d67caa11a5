package com.baidu.keyue.deepsight.open.aiob.controller;

import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.open.aiob.model.dto.OpenAutoReplayListDTO;
import com.baidu.keyue.deepsight.open.aiob.model.vo.OpenAutoReplayListVO;
import com.baidu.keyue.deepsight.open.aiob.service.OpenAutoReplayQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * 外呼场景，小秘书相关open接口
 * 小秘书定义：外呼打电话，用户设置了自动回复，称为小秘书参与
 *
 * <AUTHOR>
 * @date 2025/8/13 16:03
 */
@RestController
@RequestMapping("/open/deepsight/v1/aiob/auto-replay")
@Slf4j
public class OpenAutoReplyController {

    @Autowired
    private OpenAutoReplayQueryService openAutoReplayQueryService;

    /**
     * 小秘书接通名单查询接口
     */
    @GetMapping("/list")
    public BaseResponse<List<OpenAutoReplayListVO>> listAutoReplay(OpenAutoReplayListDTO autoReplayListDTO) {
        return BaseResponse.of(openAutoReplayQueryService.selectListByTenantId(autoReplayListDTO.getTenantId()));
    }

}
