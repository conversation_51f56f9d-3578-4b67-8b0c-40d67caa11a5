package com.baidu.keyue.deepsight.utils;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 通用的数字格式化工具类，支持指定小数位、四舍五入、向上取整、向下取整等模式
 *
 * <AUTHOR>
 * @date 2025/8/14 11:45
 */
@Slf4j
public class NumberFormatUtil {

    /**
     * 指定小数位 && 四舍五入
     */
    public static double roundToDouble(Number number, int scale) {
        if (number == null) {
            return 0;
        }
        return BigDecimal.valueOf(number.doubleValue())
                .setScale(scale, RoundingMode.HALF_UP)
                .doubleValue();
    }

    public static float roundToFloat(Number number, int scale) {
        if (number == null) {
            return 0f;
        }
        return BigDecimal.valueOf(number.doubleValue())
                .setScale(scale, RoundingMode.HALF_UP)
                .floatValue();
    }

    public static int roundToInt(Number number) {
        if (number == null) {
            return 0;
        }
        return BigDecimal.valueOf(number.doubleValue())
                .setScale(0, RoundingMode.HALF_UP)
                .intValue();
    }

    public static long roundToLong(Number number) {
        if (number == null) {
            return 0;
        }
        return BigDecimal.valueOf(number.doubleValue())
                .setScale(0, RoundingMode.HALF_UP)
                .longValue();
    }

    public static BigDecimal roundToBigDecimal(Number number, int scale) {
        if (number == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(number.doubleValue())
                .setScale(scale, RoundingMode.HALF_UP);
    }

    /**
     * 向下取整
     */
    public static double floorToDouble(Number number, int scale) {
        if (number == null) {
            return 0;
        }
        return BigDecimal.valueOf(number.doubleValue())
                .setScale(scale, RoundingMode.FLOOR)
                .doubleValue();
    }

    public static float floorToFloat(Number number, int scale) {
        if (number == null) {
            return 0f;
        }
        return BigDecimal.valueOf(number.doubleValue())
                .setScale(scale, RoundingMode.FLOOR)
                .floatValue();
    }

    public static int floorToInt(Number number) {
        if (number == null) {
            return 0;
        }
        return BigDecimal.valueOf(number.doubleValue())
                .setScale(0, RoundingMode.FLOOR)
                .intValue();
    }

    public static long floorToLong(Number number) {
        if (number == null) {
            return 0;
        }
        return BigDecimal.valueOf(number.doubleValue())
                .setScale(0, RoundingMode.FLOOR)
                .longValue();
    }

    public static BigDecimal floorToBigDecimal(Number number, int scale) {
        if (number == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(number.doubleValue())
                .setScale(scale, RoundingMode.FLOOR);
    }

    /**
     * 向上取整
     */
    public static double ceilToDouble(Number number, int scale) {
        if (number == null) {
            return 0;
        }
        return BigDecimal.valueOf(number.doubleValue())
                .setScale(scale, RoundingMode.CEILING)
                .doubleValue();
    }

    public static float ceilToFloat(Number number, int scale) {
        if (number == null) {
            return 0f;
        }
        return BigDecimal.valueOf(number.doubleValue())
                .setScale(scale, RoundingMode.CEILING)
                .floatValue();
    }

    public static int ceilToInt(Number number) {
        if (number == null) {
            return 0;
        }
        return BigDecimal.valueOf(number.doubleValue())
                .setScale(0, RoundingMode.CEILING)
                .intValue();
    }

    public static long ceilToLong(Number number) {
        if (number == null) {
            return 0;
        }
        return BigDecimal.valueOf(number.doubleValue())
                .setScale(0, RoundingMode.CEILING)
                .longValue();
    }

    public static BigDecimal ceilToBigDecimal(Number number, int scale) {
        if (number == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(number.doubleValue())
                .setScale(scale, RoundingMode.CEILING);
    }
}
