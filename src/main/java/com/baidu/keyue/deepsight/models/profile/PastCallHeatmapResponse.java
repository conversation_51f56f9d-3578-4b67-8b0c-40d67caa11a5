package com.baidu.keyue.deepsight.models.profile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @ClassName PastCallHeatmapResponse
 * @Description 用户历史通话热力图数据响应
 * <AUTHOR>
 * @Date 2025/8/12
 */
@Data
@Slf4j
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PastCallHeatmapResponse {

    /**
     * 热力图数据（7天×24小时矩阵）
     * 外层List表示星期（0=周日，6=周六）
     * 内层List表示小时（0-23）
     */
    private List<List<Object>> heatmapData;

    /**
     * 小时标签列表
     */
    private final List<String> hourLabels = IntStream.range(0, 24)
            .mapToObj(h -> String.format("%02d:00", h)).toList();

    /**
     * 星期标签列表
     */
    private final List<String> dayLabels = Arrays.asList(
            "周日", "周一", "周二", "周三", "周四", "周五", "周六");

    /**
     * 从7×24矩阵数据构造热力图响应
     * @param matrixData 7×24矩阵数据，外层7个元素代表周日到周六，内层24个元素代表0-23小时
     */
    public PastCallHeatmapResponse(List<List<Object>> matrixData) {
        if (matrixData != null && matrixData.size() == 7) {
            // 验证每一行都有24个元素
            boolean isValidMatrix = matrixData.stream()
                    .allMatch(row -> row != null && row.size() == 24);

            if (isValidMatrix) {
                this.heatmapData = matrixData;
            } else {
                // 如果数据格式不正确，初始化为默认矩阵
                log.error("PastCallHeatmapResponse matrixData size not 24");
                initializeDefaultMatrix();
            }
        } else {
            // 如果数据为空或格式不正确，初始化为默认矩阵
            log.error("PastCallHeatmapResponse matrixData is null or size not 7");
            initializeDefaultMatrix();
        }
    }

    /**
     * 初始化默认的7×24矩阵，所有值为0
     */
    private void initializeDefaultMatrix() {
        heatmapData = IntStream.range(0, 7)
                .mapToObj(i -> IntStream.range(0, 24)
                        .mapToObj(j -> (Object) 0)
                        .collect(Collectors.toList()))
                .collect(Collectors.toList());
    }
}
