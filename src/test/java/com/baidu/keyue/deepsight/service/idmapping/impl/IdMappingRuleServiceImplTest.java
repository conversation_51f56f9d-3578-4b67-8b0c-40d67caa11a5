package com.baidu.keyue.deepsight.service.idmapping.impl;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.argThat;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.enums.IdMappingRuleFiledTypeEnum;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.idmapping.request.rule.CreateIdMappingRuleRequest;
import com.baidu.keyue.deepsight.models.idmapping.response.rule.IdMappingRuleFieldsResponse;
import com.baidu.keyue.deepsight.models.idmapping.response.rule.IdMappingRuleItemResponse;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRelation;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRule;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingRelationMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingRuleMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class IdMappingRuleServiceImplTest{


    private static final String TEST_CN_FIELD = "测试字段";

    private static final Long TEST_DATA_TABLE_ID = 1L;

    @Mock
    private IdMappingRelationMapper idMappingRelMapper;

    @Mock
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;
    private static final String TEST_USER_ID = "testUser";

    private static final Long TEST_ID = 1L;

    private static final String TEST_EN_FIELD = "testField";

    @Mock
    private IdMappingRuleMapper idMappingRuleMapper;

    @InjectMocks
    private IdMappingRuleServiceImpl idMappingRuleService;

    private static final String TEST_TENANT_ID = "testTenant";

    @BeforeEach
    void setUp() {
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TEST_TENANT_ID);
            mocked.when(WebContextHolder::getUserId).thenReturn(TEST_USER_ID);
        }
    }

    @Test
    void updateIdMappingRuleShouldThrowExceptionWhenIdIsNull() {
        CreateIdMappingRuleRequest request = new CreateIdMappingRuleRequest();
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TEST_TENANT_ID);
            mocked.when(WebContextHolder::getUserId).thenReturn(TEST_USER_ID);
            DeepSightException.ParamsErrorException exception = assertThrows(
                    DeepSightException.ParamsErrorException.class,
                    () -> idMappingRuleService.updateIdMappingRule(request)
            );
            assertEquals(ErrorCode.BAD_REQUEST, exception.getErrorCode());
            assertEquals("待修改字段id为空", exception.getMessage());
        }
    }

    @Test
    void updateIdMappingRuleShouldThrowExceptionWhenRuleNotFound() {
        CreateIdMappingRuleRequest request = new CreateIdMappingRuleRequest();
        request.setId(TEST_ID);
        
        when(idMappingRuleMapper.selectByPrimaryKey(TEST_ID)).thenReturn(null);

        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TEST_TENANT_ID);
            mocked.when(WebContextHolder::getUserId).thenReturn(TEST_USER_ID);
            DeepSightException.ParamsErrorException exception = assertThrows(
                    DeepSightException.ParamsErrorException.class,
                    () -> idMappingRuleService.updateIdMappingRule(request)
            );

            assertEquals(ErrorCode.BAD_REQUEST, exception.getErrorCode());
            assertEquals("待修改字段id不存在", exception.getMessage());
        }
        
    }

    @Test
    void updateIdMappingRuleShouldThrowExceptionWhenTenantNotMatch() {
        CreateIdMappingRuleRequest request = new CreateIdMappingRuleRequest();
        request.setId(TEST_ID);
        
        IdMappingRule existingRule = new IdMappingRule();
        existingRule.setTenantId("otherTenant");
        
        when(idMappingRuleMapper.selectByPrimaryKey(TEST_ID)).thenReturn(existingRule);
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TEST_TENANT_ID);
            mocked.when(WebContextHolder::getUserId).thenReturn(TEST_USER_ID);
            DeepSightException.ParamsErrorException exception = assertThrows(
                    DeepSightException.ParamsErrorException.class,
                    () -> idMappingRuleService.updateIdMappingRule(request)
            );

            assertEquals(ErrorCode.BAD_REQUEST, exception.getErrorCode());
            assertEquals("待修改字段id不存在", exception.getMessage());
        }
        
    }

    @Test
    void updateIdMappingRuleShouldThrowExceptionWhenPriorityConflict() {
        CreateIdMappingRuleRequest request = new CreateIdMappingRuleRequest();
        request.setId(TEST_ID);
        request.setEnField(TEST_EN_FIELD);
        request.setPriority(10);
        
        IdMappingRule existingRule = new IdMappingRule();
        existingRule.setTenantId(TEST_TENANT_ID);
        existingRule.setEnField(TEST_EN_FIELD);
        
        IdMappingRule otherRule = new IdMappingRule();
        otherRule.setEnField("otherField");
        otherRule.setPriority(10);
        
        when(idMappingRuleMapper.selectByPrimaryKey(TEST_ID)).thenReturn(existingRule);
        when(idMappingRuleMapper.selectByExample(any())).thenReturn(List.of(otherRule));

        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TEST_TENANT_ID);
            mocked.when(WebContextHolder::getUserId).thenReturn(TEST_USER_ID);
            DeepSightException.ParamsErrorException exception = assertThrows(
                    DeepSightException.ParamsErrorException.class,
                    () -> idMappingRuleService.updateIdMappingRule(request)
            );

            assertEquals(ErrorCode.BAD_REQUEST, exception.getErrorCode());
            assertEquals("待更新字段优先级已存在", exception.getMessage());
        }
        
    }

    @Test
    void updateIdMappingRuleShouldUpdateSuccessfully() {
        CreateIdMappingRuleRequest request = new CreateIdMappingRuleRequest();
        request.setId(TEST_ID);
        request.setEnField(TEST_EN_FIELD);
        request.setPriority(10);
        request.setDescription("new description");
        request.setFieldType(IdMappingRuleFiledTypeEnum.SINGLE_VALUE.getInteger());
        request.setMergePolicy(1);
        
        IdMappingRule existingRule = new IdMappingRule();
        existingRule.setTenantId(TEST_TENANT_ID);
        existingRule.setEnField(TEST_EN_FIELD);
        existingRule.setPriority(5);
        
        IdMappingRule otherRule = new IdMappingRule();
        otherRule.setEnField("otherField");
        otherRule.setPriority(20);
        
        when(idMappingRuleMapper.selectByPrimaryKey(TEST_ID)).thenReturn(existingRule);
        when(idMappingRuleMapper.selectByExample(any())).thenReturn(List.of(otherRule));
        when(idMappingRuleMapper.updateByPrimaryKeySelective(any())).thenReturn(1);

        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TEST_TENANT_ID);
            mocked.when(WebContextHolder::getUserId).thenReturn(TEST_USER_ID);
            assertDoesNotThrow(() -> idMappingRuleService.updateIdMappingRule(request));
            verify(idMappingRuleMapper).updateByPrimaryKeySelective(argThat(rule ->
                    rule.getPriority() == 10 &&
                            rule.getDescription().equals("new description") &&
                            rule.getModifier().equals(TEST_USER_ID) &&
                            rule.getUpdateTime() != null
            ));
        }
        
    }

    @Test
    void listFieldShouldReturnEmptyWhenNoNewFields() {
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TEST_TENANT_ID);
            
            Map<String, String> inputMap = Map.of(TEST_EN_FIELD, TEST_CN_FIELD);
            IdMappingRule existingRule = new IdMappingRule();
            existingRule.setEnField(TEST_EN_FIELD);
            existingRule.setPriority(1);
            
            when(idMappingRuleMapper.selectByExample(any())).thenReturn(List.of(existingRule));
            
            IdMappingRuleFieldsResponse response = idMappingRuleService.listField(inputMap);
            
            assertTrue(response.getFields().isEmpty());
            assertEquals(1, response.getUsedPriorities().size());
            assertEquals(1, response.getUsedPriorities().get(0));
        }
    }

    @Test
    void listFieldShouldReturnNewFieldsWithDefaultValues() {
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class);
             MockedStatic<JsonUtils> jsonUtilsMocked = mockStatic(JsonUtils.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TEST_TENANT_ID);
            
            Map<String, String> inputMap = Map.of(TEST_EN_FIELD, TEST_CN_FIELD);
            
            // Mock existing rules (empty)
            when(idMappingRuleMapper.selectByExample(any())).thenReturn(List.of());
            
            // Mock relations
            IdMappingRelation relation = new IdMappingRelation();
            relation.setDataTableId(TEST_DATA_TABLE_ID);
            relation.setEnFields("[\"" + TEST_EN_FIELD + "\"]");
            when(idMappingRelMapper.selectByExample(any())).thenReturn(List.of(relation));
            
            // Mock JSON parsing
            jsonUtilsMocked.when(() -> JsonUtils.toListUnchecked(anyString(), eq(List.class), eq(String.class)))
                .thenReturn(List.of(TEST_EN_FIELD));
            
            // Mock field meta info
            TableFieldMetaInfo metaInfo = new TableFieldMetaInfo();
            metaInfo.setEnField(TEST_EN_FIELD);
            metaInfo.setDescription("Test Description");
            when(tableFieldMetaInfoMapper.selectByExample(any())).thenReturn(List.of(metaInfo));
            
            IdMappingRuleFieldsResponse response = idMappingRuleService.listField(inputMap);
            
            assertEquals(1, response.getFields().size());
            IdMappingRuleItemResponse item = response.getFields().get(0);
            assertEquals(TEST_EN_FIELD, item.getEnField());
            assertEquals(TEST_CN_FIELD, item.getCnField());
            assertEquals("Test Description", item.getDescription());
            assertTrue(response.getUsedPriorities().isEmpty());
        }
    }

    @Test
    void listFieldShouldSkipTablesWithAllFieldsConfigured() {
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class);
             MockedStatic<JsonUtils> jsonUtilsMocked = mockStatic(JsonUtils.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TEST_TENANT_ID);
            
            Map<String, String> inputMap = Map.of(TEST_EN_FIELD, TEST_CN_FIELD);
            
            // Mock existing rules (field already exists)
            IdMappingRule existingRule = new IdMappingRule();
            existingRule.setEnField(TEST_EN_FIELD);
            existingRule.setPriority(1);
            when(idMappingRuleMapper.selectByExample(any())).thenReturn(List.of(existingRule));
            
            // Mock relations (should be skipped)
            IdMappingRelation relation = new IdMappingRelation();
            relation.setDataTableId(TEST_DATA_TABLE_ID);
            relation.setEnFields("[\"" + TEST_EN_FIELD + "\"]");
            when(idMappingRelMapper.selectByExample(any())).thenReturn(List.of(relation));
            
            IdMappingRuleFieldsResponse response = idMappingRuleService.listField(inputMap);
            
            assertTrue(response.getFields().isEmpty());
            assertEquals(1, response.getUsedPriorities().size());
            assertEquals(1, response.getUsedPriorities().get(0));
        }
    }

    @Test
    void listFieldShouldHandleMultipleNewFields() {
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class);
             MockedStatic<JsonUtils> jsonUtilsMocked = mockStatic(JsonUtils.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TEST_TENANT_ID);
            
            String field1 = "field1";
            String field2 = "field2";
            Map<String, String> inputMap = Map.of(
                field1, "字段1",
                field2, "字段2"
            );
            
            // Mock existing rules (empty)
            when(idMappingRuleMapper.selectByExample(any())).thenReturn(List.of());
            
            // Mock relations
            IdMappingRelation relation = new IdMappingRelation();
            relation.setDataTableId(TEST_DATA_TABLE_ID);
            relation.setEnFields("[\"" + field1 + "\", \"" + field2 + "\"]");
            when(idMappingRelMapper.selectByExample(any())).thenReturn(List.of(relation));
            
            // Mock JSON parsing
            jsonUtilsMocked.when(() -> JsonUtils.toListUnchecked(anyString(), eq(List.class), eq(String.class)))
                .thenReturn(Arrays.asList(field1, field2));
            
            // Mock field meta info
            TableFieldMetaInfo metaInfo1 = new TableFieldMetaInfo();
            metaInfo1.setEnField(field1);
            metaInfo1.setDescription("Desc1");
            TableFieldMetaInfo metaInfo2 = new TableFieldMetaInfo();
            metaInfo2.setEnField(field2);
            metaInfo2.setDescription("Desc2");
            when(tableFieldMetaInfoMapper.selectByExample(any())).thenReturn(Arrays.asList(metaInfo1, metaInfo2));
            
            IdMappingRuleFieldsResponse response = idMappingRuleService.listField(inputMap);
            
            assertEquals(2, response.getFields().size());
            assertTrue(response.getUsedPriorities().isEmpty());
        }
    }

}