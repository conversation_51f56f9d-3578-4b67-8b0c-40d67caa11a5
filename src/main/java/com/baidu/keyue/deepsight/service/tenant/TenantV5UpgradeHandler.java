package com.baidu.keyue.deepsight.service.tenant;

import com.baidu.keyue.deepsight.database.doris.DorisTableTemplateConfig;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.TenantInfoMapper;
import com.baidu.keyue.deepsight.service.datamanage.impl.TableRecordCommonService;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName TenantV3UpgradeHandler
 * @Description v1.1.3租户升级
 * <AUTHOR>
 * @Date 2025/6/20 3:18 PM
 */
@Slf4j
@Service
@Order(6)
public class TenantV5UpgradeHandler extends TenantUpgradeHandler {

    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private TenantInfoMapper tenantInfoMapper;

    @Resource
    private DorisTableTemplateConfig dorisTableTemplateConfig;

    @Resource
    private TableRecordCommonService tableRecordCommonService;

    @Resource
    private DorisService dorisService;

    @Resource
    private TenantV6UpgradeHandler tenantV6UpgradeHandler;
    
    @Resource
    @Lazy
    private TenantInfoService tenantInfoService;
    
    /**
     * 外呼对话记录新增字段
     */
    private final List<String> aiobSessionField = List.of("callerNum", "dicCategory", "dicName", "is_auto_answer", 
            "lineStatus", "taskStatus", "callerCity");

    private final Map<String, TableFieldMetaInfo> aiobSessionFieldMap = new HashMap<>();


    @PostConstruct
    public void init() throws IOException {
        setVersion(6);
        initDorisSql("upgrade/doris/v1.1.3.sql");
        addNextHandler(tenantV6UpgradeHandler);
        dorisTableTemplateConfig.getAiobSessionFieldsInfo().forEach(field -> aiobSessionFieldMap.put(field.getEnField(), field));
    }

    public TenantV5UpgradeHandler(DorisService dorisService) {
        super(dorisService);
    }

    @Override
    protected boolean needUpdateTenant(TenantInfo tenant) {
        TenantInfo tenantInfo = tenantInfoService.queryTenantInfo(tenant.getTenantid());
        Integer version = tenantInfo.getVersion();
        tenant.setUserId(tenantInfo.getUserId());
        return Objects.equals(version, 5);
    }

    @Override
    protected void executeUpgrade(TenantDTO tenant) {
        transactionTemplate.execute(status -> {
            try {
                String tenantId = tenant.getTenantId();
                log.info("tenant v1.1.3 upgrade start.tenantId is {}.", tenantId);
                // 老租户升级历史数据
                if (!tenant.getIsInit()) {
                    // 历史表操作
                    oldTableUpgrade(tenantId);
                    log.info("v1.1.3 Upgrade oldTableUpgrade over");
                }
                // 更新责任链租户信息
                TenantInfo info = tenant.getTenantInfo();
                info.setVersion(version);
                // 更新数据库租户版本
                TenantInfo tenantInfo = new TenantInfo();
                tenantInfo.setVersion(version);
                tenantInfo.setId(info.getId());
                tenantInfo.setUpdateTime(new Date());
                tenantInfoMapper.updateByPrimaryKeySelective(tenantInfo);
            } catch (Exception e) {
                log.error("tenant v1.1.3 Upgrade error!, tenantId:{} ", tenant.getTenantId(), e);
                status.setRollbackOnly();
                throw new DeepSightException.InitTenantFailedException(ErrorCode.INTERNAL_ERROR, "租户v1.1.2版本升级失败");
            }
            return null;
        });
    }

    /**
     * 老租户表升级
     * 只针对老租户的升级
     * Doris、mysql字段信息修改、添加、删除
     *
     * @param tenantId 租户id
     */
    public void oldTableUpgrade(String tenantId) {
        // 执行除建表外的其他语句：历史Doris表新增字段
        for (String sql : this.sqlList) {
            if (sql.toUpperCase().contains("CREATE TABLE")) {
                continue;
            }
            // 将^&符号作为租户ID替代符号
            String execSql = sql.replaceAll("\\^&", tenantId) + ";";
            dorisService.operationSchema(execSql);
        }
        // 外呼对话记录表增加字段元信息
        String aiobSessionTableName = TenantUtils.generateAiobSessionTableName(tenantId);
        tableRecordCommonService.saveUpgradeFieldMetaInfo(aiobSessionTableName, aiobSessionField, aiobSessionFieldMap);
    }
}
