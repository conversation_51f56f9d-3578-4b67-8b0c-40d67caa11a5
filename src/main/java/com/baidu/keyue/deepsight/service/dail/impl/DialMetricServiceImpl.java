package com.baidu.keyue.deepsight.service.dail.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.AiobFailTypeEnum;
import com.baidu.keyue.deepsight.enums.AlertConfigTypeEnum;
import com.baidu.keyue.deepsight.enums.DateLineTypeEnum;
import com.baidu.keyue.deepsight.enums.DialMetricQueryTypeEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.dial.AlertSettingQueryRequest;
import com.baidu.keyue.deepsight.models.dial.AlertSettingRequest;
import com.baidu.keyue.deepsight.models.dial.BusyLocationDistributionResponse;
import com.baidu.keyue.deepsight.models.dial.CallCoreMetricsRequest;
import com.baidu.keyue.deepsight.models.dial.CallCoreMetricsResponse;
import com.baidu.keyue.deepsight.models.dial.CallResultCompositionResponse;
import com.baidu.keyue.deepsight.models.dial.ConnectionRateTrendDetailRequest;
import com.baidu.keyue.deepsight.models.dial.ConnectionRateTrendDetailResponse;
import com.baidu.keyue.deepsight.models.dial.ConnectionRateTrendRequest;
import com.baidu.keyue.deepsight.models.dial.ConnectionRateTrendResponse;
import com.baidu.keyue.deepsight.models.dial.CoreMetricsResponse;
import com.baidu.keyue.deepsight.models.dial.LineDetailResponse;
import com.baidu.keyue.deepsight.models.dial.LineInfoResponse;
import com.baidu.keyue.deepsight.models.dial.LineRejectTrendResponse;
import com.baidu.keyue.deepsight.models.dial.ThirtyDayRankingRequest;
import com.baidu.keyue.deepsight.models.dial.ThirtyDayRankingResponse;
import com.baidu.keyue.deepsight.models.dial.UnconnectedHeatmapResponse;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.mysqldb.entity.AlertConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.AlertConfigCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.AlertConfigMapper;
import com.baidu.keyue.deepsight.models.dial.RejectReasonResponse;
import com.baidu.keyue.deepsight.models.dial.RobotInfoResponse;
import com.baidu.keyue.deepsight.models.dial.TaskInfoResponse;
import com.baidu.keyue.deepsight.service.dail.DialMetricService;
import com.baidu.keyue.deepsight.utils.MathUtils;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName DialMetricServiceImpl
 * @Description 呼通分析接口实现
 * <AUTHOR>
 * @Date 2025/6/30 10:39 AM
 */
@Slf4j
@Service
public class DialMetricServiceImpl implements DialMetricService {
    @Resource
    private DorisService dorisService;

    @Resource
    private AlertConfigMapper alertConfigMapper;

    /**
     * 拨打次数阈值：10/100/1000/10000
     */
    private final Set<Integer> callCountThresholds = Set.of(10, 100, 1000, 10000);
    /**
     * 告警频率（小时）：1/12/24/72
     */
    private final Set<Integer> alertFrequencies = Set.of(1, 12, 24, 72);
    
    @Value("${dial-online-time.online-time:2025-07-31 23:59:59}")
    private String onlineTimeStr;
    
    private DateTime onlineTime;

    @PostConstruct
    public void init() {
        onlineTime = DateUtil.parse(onlineTimeStr);
    }

    @Override
    public CoreMetricsResponse getCoreMetrics(String tenantId) {
        String sessionTableName = TenantUtils.generateAiobSessionTableName(tenantId);
        // 获取昨天时间范围,统计昨日核心数据
        Date date = new Date();
        DateTime endTime = DateUtil.beginOfDay(date);
        DateTime startTime = DateUtil.offsetDay(endTime, -1);
        startTime = startTime.before(onlineTime) ? onlineTime : startTime;
        String sql = ORMUtils.generateCallAnalysisCoreMetrics(sessionTableName, startTime, endTime);
        List<Map<String, Object>> maps = dorisService.selectList(sql);
        DateTime alertStart = DateUtil.offsetDay(endTime, -30);
        alertStart = alertStart.before(onlineTime) ? onlineTime : alertStart;
        Integer alertCount = queryAlertDays(tenantId, null, null, alertStart, endTime);
        if (CollUtil.isEmpty(maps)) {
            return new CoreMetricsResponse(0L, 0L, 0, alertCount);
        }
        // 解析昨日数据
        Map<String, Object> dataMap = maps.get(0);
        Object dialCount = dataMap.get("dial_count");
        Object connectedCount = dataMap.get("connected_count");
        Object connectRate = dataMap.get("connect_rate");
        Long dialCountL = dialCount == null ? 0L : (Long) dialCount;
        Long connectedCountL = connectedCount == null ? 0L : (Long) connectedCount;
        Double connectRated = connectRate == null ? 0d : (Double) connectRate;
        Integer yesterdayConnectionRate = MathUtils.doubleToIntPercent(connectRated);
        return new CoreMetricsResponse(dialCountL, connectedCountL, yesterdayConnectionRate, alertCount);
    }

    /**
     * 统计告警天数
     *
     * @param tenantId     租户ID
     * @param configType   配置类型：LING、TASK、ROBOT
     * @param configTarget 配置目标ID：callNum\task_id\robot_id
     * @param startTime    统计起始时间
     * @param endTime      统计结束时间
     * @return 告警条数
     */
    @Valid
    public Integer queryAlertDays(@NotNull String tenantId,
                                  AlertConfigTypeEnum configType,
                                  String configTarget,
                                  @NotNull DateTime startTime,
                                  @NotNull DateTime endTime) {
        String sql = ORMUtils.generateQueryAiobAlertDaysSql(tenantId, configType, configTarget, startTime, endTime);
        return (int) dorisService.getCount(sql);
    }

    @Override
    public List<ConnectionRateTrendResponse> getConnectionRateTrend(ConnectionRateTrendRequest request, String tenantId) {
        // 默认按照24小时区间统计，按照小时统计
        DateLineTypeEnum type = request.getType();
        int offsetDay = Objects.equals(type, DateLineTypeEnum.DAY) ? -30 : -1;
        int offsetTime = Objects.equals(type, DateLineTypeEnum.DAY) ? 30 : 24;
        Date now = new Date();
        DateTime endTime = Objects.equals(type, DateLineTypeEnum.DAY) ? DateUtil.beginOfDay(now) : DateUtil.beginOfHour(now);
        // 计算时间线
        DateTime startTime = DateUtil.offsetDay(now, offsetDay);
        List<String> dateLine = generateDateLine(type, offsetTime, startTime);
        startTime = startTime.before(onlineTime) ? onlineTime : startTime;
        ConnectionRateTrendRequest trendRequest = new ConnectionRateTrendRequest(request.getType(), startTime, endTime);
        String sql = ORMUtils.generateConnectionRateTrend(trendRequest, tenantId);
        List<Map<String, Object>> selectList = dorisService.selectList(sql);
        if (CollUtil.isEmpty(selectList)) {
            return dateLine.stream().map(dateKey -> new ConnectionRateTrendResponse(dateKey, 0, 0L)).toList();
        }
        Map<String, ConnectionRateTrendResponse> dataMap = selectList.stream().map(ConnectionRateTrendResponse::new)
                .collect(Collectors.toMap(ConnectionRateTrendResponse::getTime, data -> data));
        return dateLine.stream().map(dateKey -> {
            ConnectionRateTrendResponse response = dataMap.get(dateKey);
            return response != null ? response : new ConnectionRateTrendResponse(dateKey, 0, 0L);
        }).toList();
    }

    /**
     * 获取时间线数组
     *
     * @param type       类型：天/小时
     * @param offsetTime 时间线长度
     * @param start      开始时间
     * @return 时间线数组
     */
    public List<String> generateDateLine(DateLineTypeEnum type, int offsetTime, Date start) {
        List<String> list = new ArrayList<>();
        if (Objects.equals(DateLineTypeEnum.DAY, type)) {
            for (int i = 0; i < offsetTime; i++) {
                list.add(DateUtil.format(DateUtil.offsetDay(start, i), type.getDateFormat()));
            }
        } else {
            for (int i = 0; i < offsetTime; i++) {
                list.add(DateUtil.format(DateUtil.offsetHour(start, i), type.getDateFormat()));
            }
        }
        return list;
    }

    @Override
    public Integer configAlertSetting(AlertSettingRequest request, String tenantId) {
        Integer callCountThreshold = request.getCallCountThreshold();
        Assert.isTrue(callCountThresholds.contains(callCountThreshold),
                () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "拨打次数阈值错误"));
        Integer alertFrequency = request.getAlertFrequency();
        Assert.isTrue(alertFrequencies.contains(alertFrequency),
                () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "告警频率值错误"));
        AlertSettingRequest oldData = queryAlertSetting(new AlertSettingQueryRequest(request.getConfigType(), request.getConfigId()), tenantId);
        AlertConfig alertConfig = request.toAlertConfig();
        alertConfig.setTenantId(tenantId);
        if (oldData != null) {
            alertConfig.setId(oldData.getId());
            alertConfigMapper.updateByPrimaryKeySelective(alertConfig);
        } else {
            alertConfigMapper.insertSelective(alertConfig);
        }
        return alertConfig.getId();
    }

    @Override
    public AlertSettingRequest queryAlertSetting(AlertSettingQueryRequest request, String tenantId) {
        AlertConfigCriteria criteria = new AlertConfigCriteria();
        criteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andConfigTypeEqualTo(request.getConfigType().getValue())
                .andConfigTargetEqualTo(request.getConfigId());
        List<AlertConfig> alertConfigs = alertConfigMapper.selectByExample(criteria);
        return CollUtil.isNotEmpty(alertConfigs) ? new AlertSettingRequest(alertConfigs.get(0)) : null;
    }

    @Override
    public BasePageResponse.Page<ThirtyDayRankingResponse> getThirtyDayRanking(ThirtyDayRankingRequest request, String tenantId) {
        String tableName = TenantUtils.generateAiobSessionTableName(tenantId);
        Date now = new Date();
        DateTime timeEnd = DateUtil.beginOfDay(now);
        DateTime timeStart = DateUtil.offsetDay(timeEnd, -30);
        timeStart = timeStart.before(onlineTime) ? onlineTime : timeStart;
        request.setStartTime(timeStart);
        request.setEndTime(timeEnd);
        String countSql = ORMUtils.generateAiobThirtyDayRankingCountSql(request, tableName, tenantId);
        long count = dorisService.getCount(countSql);
        if (count <= 0) {
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), 0L, Collections.emptyList());
        }
        String sql = ORMUtils.generateAiobThirtyDayRankingPageSql(request, tableName, tenantId);
        List<Map<String, Object>> dataList = dorisService.selectList(sql);
        List<String> configTargets = dataList.stream().map(data -> String.valueOf(data.get("id"))).toList();
        String simpleDetailSql = ORMUtils.generateSessionSimpleDetailSql(configTargets, tenantId, request);
        List<Map<String, Object>> simpleDetailList = dorisService.selectList(simpleDetailSql);
        Map<String, Map<String, Object>> detailMap = simpleDetailList.stream()
                .collect(Collectors.toMap(data -> String.valueOf(data.get("id")), map -> map, (k1, k2) -> k2));
        // 补充告警阈值和接通率趋势
        Date endTime = DateUtil.beginOfHour(now);
        DateTime startTime = DateUtil.offsetHour(endTime, -12);
        List<String> dateLine = generateDateLine(DateLineTypeEnum.HOUR, 12, startTime);
        ConnectionRateTrendRequest trendRequest = new ConnectionRateTrendRequest(DateLineTypeEnum.HOUR, startTime, endTime,
                request.getType().getIdFieldName(), configTargets);
        String rateTrendSql = ORMUtils.generateConnectionRateTrend(trendRequest, tenantId);
        Map<Object, List<Map<String, Object>>> connectionRateTrendMap = dorisService.selectList(rateTrendSql)
                .stream().collect(Collectors.groupingBy(data -> data.get("targetId")));
        List<AlertConfig> alertConfigs = queryAlertSettingList(tenantId, request.getType(), configTargets);
        Map<String, Integer> alertMap = alertConfigs.stream()
                .collect(Collectors.toMap(AlertConfig::getConfigTarget, data -> data.getThresholdRate().intValue(), (k1, k2) -> k2));
        List<ThirtyDayRankingResponse> list = dataList.stream()
                .map(data -> new ThirtyDayRankingResponse(data, request, detailMap, alertMap, connectionRateTrendMap, dateLine))
                .toList();
        return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), count, list);
    }

    /**
     * 查询外呼告警配置
     *
     * @param tenantId      租户ID
     * @param type          配置类型
     * @param configTargets 配置目标ids
     * @return 配置列表
     */
    public List<AlertConfig> queryAlertSettingList(String tenantId, AlertConfigTypeEnum type, List<String> configTargets) {
        if (CollUtil.isEmpty(configTargets)) {
            return Collections.emptyList();
        }
        AlertConfigCriteria criteria = new AlertConfigCriteria();
        criteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andConfigTypeEqualTo(type.getValue())
                .andIsActiveEqualTo(true)
                .andConfigTargetIn(configTargets);
        return alertConfigMapper.selectByExample(criteria);
    }

    @Override
    public CallCoreMetricsResponse getCallDetailCoreMetrics(CallCoreMetricsRequest request) {
        String tenantId = WebContextHolder.getTenantId();

        DateTime endTime = DateUtil.beginOfDay(new Date());
        Date startTime = DateUtil.offsetDay(endTime, -30);
        startTime = startTime.before(onlineTime) ? onlineTime : startTime;
        String sql = ORMUtils.generateQueryGetCallDetailCoreMetricsSql(request, tenantId, startTime, endTime);
        List<Map<String, Object>> recordList = dorisService.selectList(sql);

        AlertConfigTypeEnum configType = null;
        if (StrUtil.isNotBlank(request.getType())) {
            configType = AlertConfigTypeEnum.createByValue(request.getType());
        }
        DateTime alertStart = DateUtil.offsetDay(endTime, -30);
        alertStart = alertStart.before(onlineTime) ? onlineTime : alertStart;
        Integer alertDays = queryAlertDays(tenantId, configType, request.getId(), alertStart, endTime);

        Integer connectedRateThreshold  = 0;
        if (configType != null) {
            AlertSettingRequest alertSetting = queryAlertSetting(
                    new AlertSettingQueryRequest(configType, request.getId()), tenantId);
            connectedRateThreshold = alertSetting == null ? 0 : alertSetting.getConnectedRateThreshold();
        }

        return new CallCoreMetricsResponse(recordList.get(0), alertDays, connectedRateThreshold);
    }

    @Override
    public List<RobotInfoResponse> getRobotList(CallCoreMetricsRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        String sql = ORMUtils.generateQueryAiobRobotOrTaskOrLineListSql(request, tenantId, DialMetricQueryTypeEnum.ROBOT, onlineTime);
        List<Map<String, Object>> recordList = dorisService.selectList(sql);

        return recordList.stream().map(RobotInfoResponse::new).toList();
    }

    @Override
    public List<TaskInfoResponse> getTaskList(CallCoreMetricsRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        String sql = ORMUtils.generateQueryAiobRobotOrTaskOrLineListSql(request, tenantId, DialMetricQueryTypeEnum.TASK, onlineTime);
        List<Map<String, Object>> recordList = dorisService.selectList(sql);

        return recordList.stream().map(TaskInfoResponse::new).toList();
    }

    @Override
    public List<LineInfoResponse> getLineList(CallCoreMetricsRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        String sql = ORMUtils.generateQueryAiobRobotOrTaskOrLineListSql(request, tenantId, DialMetricQueryTypeEnum.LINE, onlineTime);
        List<Map<String, Object>> recordList = dorisService.selectList(sql);

        return recordList.stream().map(LineInfoResponse::new).toList();
    }

    @Override
    public List<ConnectionRateTrendDetailResponse> getConnectionRateTrendDetail(ConnectionRateTrendDetailRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        Date now = new Date();
        String sql = ORMUtils.generateQueryGetAiobConnectionRateTrendSql(request, tenantId, now, onlineTime);
        List<Map<String, Object>> recordList = dorisService.selectList(sql);
        return ConnectionRateTrendDetailResponse.convert(recordList, request.getTimeType(), now);
    }

    @Override
    public List<CallResultCompositionResponse> getCallResultComposition(CallCoreMetricsRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        Date now = new Date();
        String sql = ORMUtils.generateQueryGetCallResultCompositionSql(request, tenantId, now, onlineTime);
        List<Map<String, Object>> recordList = dorisService.selectList(sql);
        return CallResultCompositionResponse.convert(recordList.get(0));
    }

    @Override
    public List<RejectReasonResponse> getRejectReasons(CallCoreMetricsRequest request, AiobFailTypeEnum failType) {
        String tenantId = WebContextHolder.getTenantId();

        Date now = new Date();
        String sql = ORMUtils.generateQueryGetRejectRatioReasonsSql(request, tenantId, failType, now, onlineTime);
        List<Map<String, Object>> recordList = dorisService.selectList(sql);
        return recordList.stream().map(RejectReasonResponse::new).toList();
    }

    @Override
    public BusyLocationDistributionResponse getBusyLocationDistribution(CallCoreMetricsRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        Date now = new Date();
        String sql = ORMUtils.generateQueryGetBusyLocationDistributionSql(request, tenantId, now, onlineTime);
        List<Map<String, Object>> recordList = dorisService.selectList(sql);
        return new BusyLocationDistributionResponse(recordList.get(0));
    }

    @Override
    public UnconnectedHeatmapResponse getUnconnectedHeatmap(CallCoreMetricsRequest request) {
        String tenantId = WebContextHolder.getTenantId();

        Date now = new Date();
        String sql = ORMUtils.generateQueryGetUnconnectedHeatmapSql(request, tenantId, now, onlineTime);
        List<Map<String, Object>> recordList = dorisService.selectList(sql);
        return new UnconnectedHeatmapResponse(recordList);
    }

    @Override
    public List<LineRejectTrendResponse> getLineRejectTrend(CallCoreMetricsRequest request) {
        String tenantId = WebContextHolder.getTenantId();

        Date now = new Date();
        String sql = ORMUtils.generateQueryGetLineRejectTrendSql(request, tenantId, now, onlineTime);
        List<Map<String, Object>> rejectCountList = dorisService.selectList(sql);

        String sql2 = ORMUtils.generateQueryGetRejectRatioReasonsByDaySql(request,
                tenantId, AiobFailTypeEnum.LINE, now, onlineTime);
        List<Map<String, Object>> rejectReasonsList = dorisService.selectList(sql2);
        return LineRejectTrendResponse.convert(rejectCountList, rejectReasonsList, now);
    }

    @Override
    public LineDetailResponse getLineDetail(CallCoreMetricsRequest request) {
        String tenantId = WebContextHolder.getTenantId();

        String sql = ORMUtils.generateQueryGetLineDetailTrendSql(request, tenantId);
        List<Map<String, Object>> recordList = dorisService.selectList(sql);

        return recordList.isEmpty() ? null : new  LineDetailResponse(recordList.get(0)) ;
    }
}


