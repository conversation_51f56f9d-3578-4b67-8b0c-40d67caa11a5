package com.baidu.keyue.deepsight.service.file.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.baidu.keyue.deepsight.config.BosConfig;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.datamanage.dto.BosProperty;
import com.baidu.keyue.deepsight.models.datamanage.dto.StsItemDto;
import com.baidu.keyue.deepsight.models.datamanage.dto.StsRequestTo;
import com.baidu.keyue.deepsight.models.datamanage.request.BceRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.BosStsRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.StsSessionVo;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.service.file.FileService;
import com.baidu.keyue.deepsight.utils.BceUtils;
import com.baidu.keyue.deepsight.service.tool.BosUtils;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import com.google.common.collect.Sets;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName FileServiceImpl
 * @Description 文件接口实现
 * <AUTHOR>
 * @Date 2025/3/28 11:17 AM
 */
@Slf4j
@Service
public class FileServiceImpl implements FileService {
    
    @Resource
    private BosConfig bosConfig;

    @Resource
    private BosUtils bosUtils;
    
    @Override
    public StsSessionVo getStsSession(BosStsRequest request, UserAuthInfo userAuthInfo) {
        String bucket = request.getBucket();
        if (StringUtils.isBlank(bucket)) {
            bucket = bosConfig.getBucket().getDataSync();
        }
        BosProperty bosProperty = new BosProperty();
        BeanUtils.copyProperties(bosUtils.getBosProperty(), bosProperty, BosProperty.class);
        bosProperty.setBucket(bucket);
        StsItemDto stsItemTo = new StsItemDto();
        stsItemTo.setEffect("Allow");
        stsItemTo.setPermission(List.of("WRITE"));
        String pureDate = DatetimeUtils.pureDateFormat(new Date());
        String fileName = IdUtil.randomUUID().replaceAll("-", "");
        String objectKey = String.format("/%s/%s/%s.%s", bosConfig.getEnv(), pureDate, fileName, request.getSuffix());
        stsItemTo.setRegion("*");
        if (StringUtils.isBlank(request.getBucket())) {
            stsItemTo.setResource(Collections.singletonList(bosProperty.getBucket() + objectKey));
        } else {
            bosProperty.setBucket(request.getBucket());
            stsItemTo.setResource(Collections.singletonList(request.getBucket() + objectKey));
        }
        if (StringUtils.isNoneBlank(request.getEndPoint())) {
            bosProperty.setEndPoint(request.getEndPoint());
        }
        stsItemTo.setService("bce:bos");
        StsRequestTo requestTo = new StsRequestTo();
        requestTo.setId(IdUtil.randomUUID());
        requestTo.setAccessControlList(Collections.singletonList(stsItemTo));
        BceRequest<StsRequestTo> bceRequest = new BceRequest<>();
        bceRequest.setUrl(bosConfig.getStsUrl());
        bceRequest.setRequestDate(new Date());
        bceRequest.setHttpMethod("POST");
        bceRequest.setRequestBody(requestTo);
        bceRequest.setWaitSignHeader(Sets.newHashSet("Host", "x-bce-date"));
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-bce-date", DatetimeUtils.gmtDateFormat(DateUtils.addHours(bceRequest.getRequestDate(), -8)));
        bceRequest.setHeader(headerMap);
        try {
            StsSessionVo stsSession = BceUtils.getStsSession(bceRequest, bosProperty);
            stsSession.setObjectKey(objectKey);
            if (StringUtils.isNotBlank(request.getProtocol())) {
                if ("https".equalsIgnoreCase(request.getProtocol())) {
                    stsSession.setEndPoint(stsSession.getEndPoint().replace("http://", "https://"));
                } else {
                    stsSession.setEndPoint(stsSession.getEndPoint().replace("https://", "http://"));
                }
            }
            return stsSession;
        } catch (Exception e) {
            log.error("sts get fail! request: " + JSONUtil.toJsonStr(bceRequest), e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "sts token get fail! " + e.getMessage());
        }
    }
}
