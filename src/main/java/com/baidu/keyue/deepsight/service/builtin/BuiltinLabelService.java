package com.baidu.keyue.deepsight.service.builtin;

/**
 * 内置标签服务接口
 * 负责内置标签的创建、管理和计算
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
public interface BuiltinLabelService {

    /**
     * 为新租户创建内置标签
     * @param tenantId 租户ID
     */
    void createBuiltinLabelsForTenant(String tenantId);

    /**
     * 触发内置标签计算
     * 在定时任务中调用，更新所有租户的内置标签数据
     */
    void triggerBuiltinLabelCalculation();

    /**
     * 检查标签是否存在
     * @param tenantId 租户ID
     * @param labelName 标签名称
     * @return 是否存在
     */
    boolean isLabelExists(String tenantId, String labelName);
}
