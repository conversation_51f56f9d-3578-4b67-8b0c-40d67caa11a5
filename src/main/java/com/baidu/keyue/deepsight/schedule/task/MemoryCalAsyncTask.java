package com.baidu.keyue.deepsight.schedule.task;

import com.baidu.keyue.deepsight.database.redis.RedisConfiguration;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtractWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.service.memory.MemoryCalculateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;


@Slf4j
@Service
public class MemoryCalAsyncTask {
    @Autowired
    private RedisConfiguration redisConfiguration;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private MemoryCalculateService memoryService;

    public String generateSchedulerLockKey(String... keys) {
        return StringUtils.join(keys, "-");
    }

    @Async("memoryCalTaskExecutor")
    public CompletableFuture<Void> processPair(Pair<MemoryExtractWithBLOBs, TaskInfo> pair,
                                               TriggerModeEnum triggerMode) {
        String lockKey = generateSchedulerLockKey(redisConfiguration.getPrefix(), "memoryCalScheduler",
                triggerMode.name(), "Lock",
                String.valueOf(pair.getLeft().getId()), String.valueOf(pair.getRight().getId()));
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("memoryCalScheduler get lock failed, lockKey: {}", lockKey);
            return CompletableFuture.completedFuture(null);
        }

        try {
            memoryService.execByScheduler(pair.getLeft(), pair.getRight());
        } catch (Exception e) {
            log.error("memoryCalScheduler exec task failed, ", e);
        }
        if (lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
        return CompletableFuture.completedFuture(null);
    }

}
