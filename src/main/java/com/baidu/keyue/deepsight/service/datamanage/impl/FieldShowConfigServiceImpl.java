package com.baidu.keyue.deepsight.service.datamanage.impl;

import cn.hutool.core.collection.CollUtil;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.datamanage.request.FieldShowConfigSaveRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.FieldShowConfigResponse;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.mysqldb.entity.FieldShowConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.FieldShowConfigCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.FieldShowConfigMapper;
import com.baidu.keyue.deepsight.service.datamanage.FieldShowConfigService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName FieldShowConfigServiceImpl
 * @Description 字段显示列接口实现
 * <AUTHOR>
 * @Date 2025/5/9 4:03 PM
 */
@Slf4j
@Service
public class FieldShowConfigServiceImpl implements FieldShowConfigService {
    
    @Resource
    private FieldShowConfigMapper fieldShowConfigMapper;
    
    @Resource
    private RedissonClient redissonClient;
    
    private static final String LOCK_TEMPLATE = "SHOW_FIELD_SAVE_%s_%d";

    @Override
    public FieldShowConfigResponse getByTableIdAndTenantIdAndPageId(Long tableId, String tenantId) {
        FieldShowConfigCriteria criteria = new FieldShowConfigCriteria();
        criteria.createCriteria()
                .andDataTableIdEqualTo(tableId)
                .andTenantIdEqualTo(tenantId);
        List<FieldShowConfig> fieldShowConfigs = fieldShowConfigMapper.selectByExampleWithBLOBs(criteria);
        if (CollUtil.isEmpty(fieldShowConfigs)) {
            return null;
        }
        FieldShowConfig fieldShowConfig = fieldShowConfigs.get(0);
        return new FieldShowConfigResponse(fieldShowConfig);
    }

    @Override
    public FieldShowConfigResponse saveOrUpdateById(FieldShowConfigSaveRequest request, String tenantId, String userName) {
        Long dataTableId = request.getDataTableId();
        String lockKey = String.format(LOCK_TEMPLATE, tenantId, dataTableId);
        RLock lock = redissonClient.getLock(lockKey);
        if (!lock.tryLock()) {
            throw new DeepSightException.BusinessException(ErrorCode.BUSY_REQUEST);
        }
        FieldShowConfigResponse response = new FieldShowConfigResponse();
        try {
            FieldShowConfig config = new FieldShowConfig();
            BeanUtils.copyProperties(request, response);
            config.setShowField(CollUtil.join(request.getShowFields(), Constants.SEPARATOR));
            FieldShowConfigResponse oldData = getByTableIdAndTenantIdAndPageId(dataTableId, tenantId);
            // ID为空则添加
            if (oldData == null) {
                config.setDataTableId(dataTableId);
                config.setTenantId(tenantId);
                config.setCreator(userName);
                fieldShowConfigMapper.insertSelective(config);
                response.setId(config.getId());
            } else {
                // ID不为空则更新
                config.setId(oldData.getId());
                config.setModifier(userName);
                fieldShowConfigMapper.updateByPrimaryKeySelective(config);
            }
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return response;
    }
}
