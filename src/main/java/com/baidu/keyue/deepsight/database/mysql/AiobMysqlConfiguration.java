package com.baidu.keyue.deepsight.database.mysql;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @className aiobMysqlConfig
 * @description 外呼mysql配置
 * @date 2025/1/12 16:47
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "spring.datasource.aiob")
public class AiobMysqlConfiguration {
    private String url;
    private String username;
    private String password;
    @Value("${driver-class-name")
    private String driverClassName;
}
