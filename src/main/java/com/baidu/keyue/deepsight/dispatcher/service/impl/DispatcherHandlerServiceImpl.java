package com.baidu.keyue.deepsight.dispatcher.service.impl;

import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.dispatcher.core.ModuleTypeWrapper;
import com.baidu.keyue.deepsight.dispatcher.model.dict.DispatcherStatus;
import com.baidu.keyue.deepsight.dispatcher.model.dict.ModuleType;
import com.baidu.keyue.deepsight.dispatcher.dao.DispatcherDao;
import com.baidu.keyue.deepsight.dispatcher.model.bean.DispatcherTask;
import com.baidu.keyue.deepsight.dispatcher.model.bean.DispatcherTaskBean;
import com.baidu.keyue.deepsight.dispatcher.service.DispatcherHandlerService;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Created by IDEA
 *
 * <AUTHOR>
 * created at 2020/4/7 4:40 下午
 */
@Service
@Slf4j
public class DispatcherHandlerServiceImpl implements DispatcherHandlerService {
    private final DispatcherDao dispatcherDao;
    @Autowired
    private ModuleTypeWrapper moduleTypeWrapper;

    public DispatcherHandlerServiceImpl(DispatcherDao dispatcherDao) {
        this.dispatcherDao = dispatcherDao;
    }

    @Override
    public String create(DispatcherTask task, Date executeTime) {
        @NonNull ModuleType moduleType = task.getModuleType();

        String agentId = task.getAgentId();

        if (agentId == null) {
            // 租户导入等任务，没有agentId，但是agentId 是 主键 不能为null
            log.debug("agent id is null, reset empty string");
            agentId = "";
        }

        DispatcherTaskBean taskBean = DispatcherTaskBean.builder()
                .agentId(agentId)
                .botId(task.getBotId())
                .createUserId(task.getCreateUserId())
                .createUserName(task.getCreateUserName())
                .params(JsonUtils.toJsonWithOutException(task.getParams()))
                .module(moduleType)
                .ruleId(task.getRuleId())
                .retryCount(0)
                .retryLimit(moduleTypeWrapper.getRetryLimit(moduleType))
                .retryWaitSecond(moduleTypeWrapper.getRetryWaitSecond(moduleType))
                .executeTime(executeTime)
                .created(new Date())
                .updated(new Date())
                .status(DispatcherStatus.CREATE)
                .id(Optional.ofNullable(task.getId()).orElse(UUID.randomUUID().toString()))
                .serialKey(DispatcherTaskBean.initSerialKey(task.getAgentId(), task.getSerialKey(), moduleType))
                .tenantId(task.getTenantId())
                .build();

        log.debug("insert dispatcher task: {}", JsonUtils.toJsonWithOutException(taskBean));

        dispatcherDao.insert(taskBean);
        return taskBean.getId();
    }

    @Override
    public DispatcherTaskBean findTaskById(String id, String agentId, ModuleType module) {
        return dispatcherDao.findById(id, agentId, module);
    }

    @Override
    public void update(DispatcherTaskBean taskBean) {
        dispatcherDao.update(taskBean);
    }

    @Override
    public boolean tryAcquireTask(String taskId) {
        return dispatcherDao.tryAcquireTask(taskId) == 1;
    }

    @Override
    public DispatcherTaskBean getOldestTask(ModuleType module) {
        return dispatcherDao.findOlderTaskByStatus(DispatcherStatus.CREATE, module);
    }

    @Override
    public DispatcherTaskBean getOldestTaskForSerialAgent(ModuleType module) {
        return dispatcherDao.findOlderTaskByStatusAndUniqueSerialKey(module);
    }

    @Override
    public List<DispatcherTaskBean> checkExpire(int expire) {
        Date lastDate = DateUtils.addSeconds(new Date(), -1 * expire);
        return dispatcherDao.findExpiredTask(lastDate, DispatcherStatus.RUNNING);
    }

    @Override
    public void refreshTask(String taskId, String agentId, ModuleType module) {
        updateTaskStatus(taskId, agentId, module, null);
    }

    @Override
    public void success(String id, String agentId, ModuleType module) {
        DispatcherTaskBean taskBean = findTaskById(id, agentId, module);
        // 内部任务取消的时候只是调用了回调
        if (taskBean.getStatus().equals(DispatcherStatus.ABORT)) {
            log.info("dispatcher success cancel, cause status is {}", taskBean.getStatus());
            return;
        }
        updateTaskStatus(id, agentId, module, DispatcherStatus.SUCCESS);
    }

    @Override
    public void fail(String id, String agentId, @NonNull ModuleType module) {
        // 失败重试策略：1、超过重试限制结束；2、叠加式延迟重试
        DispatcherTaskBean taskBean = findTaskById(id, agentId, module);
        if (taskBean.getStatus().equals(DispatcherStatus.ABORT)) {
            log.info("dispatcher fail retry cancel, cause status is {}", taskBean.getStatus());
            return;
        }
        if (taskBean.getRetryLimit() > taskBean.getRetryCount()) {
            // 允许重试, 重试会增加延迟时间：retryWaitSecond * retryCount
            taskBean.setRetryCount(taskBean.getRetryCount() + 1);
            int waitSeconds = taskBean.getRetryCount() * taskBean.getRetryWaitSecond();
            Date executeTime = new Date(new Date().getTime() + waitSeconds * 1000);
            retryTask(taskBean, executeTime);
        } else {
            // 放弃重试，标记为失败
            updateTaskStatus(id, agentId, module, DispatcherStatus.FAIL);
        }
    }

    @Override
    public void retry(String id, String agentId, ModuleType moduleType) {
        DispatcherTaskBean taskBean = findTaskById(id, agentId, moduleType);
        if (Objects.isNull(taskBean)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "DISPATCHER_EXECUTOR_RETRY_FAIL");
        }
        retryTask(taskBean, new Date());
    }

    private void retryTask(DispatcherTaskBean taskBean, Date executeTime) {
        taskBean.setExecuteTime(executeTime);
        // 重置为未执行状态
        taskBean.setStatus(DispatcherStatus.CREATE);
        update(taskBean);
    }

    @Override
    public void cancel(String id, String agentId, ModuleType module) {
        updateTaskStatus(id, agentId, module, DispatcherStatus.ABORT);
    }

    private void updateTaskStatus(String id, String agentId, ModuleType module,
                                  DispatcherStatus status) {
        DispatcherTaskBean taskBean = DispatcherTaskBean.builder()
                .id(id)
                .agentId(agentId)
                .module(module)
                .status(status)
                .build();
        dispatcherDao.update(taskBean);
    }

    @Override
    public boolean isRunningAndCreateByAgentId(ModuleType module, String agentId) {
        agentId = StringUtils.isBlank(agentId) ? null : agentId;
        return dispatcherDao.countTaskByStatusAndAgentId(module, agentId, DispatcherStatus.RUNNING) > 0
                || dispatcherDao.countTaskByStatusAndAgentId(module, agentId, DispatcherStatus.CREATE) > 0;
    }

    @Override
    public boolean isRunningByAgentId(ModuleType module, String agentId) {
        agentId = StringUtils.isBlank(agentId) ? null : agentId;
        return dispatcherDao.countTaskByStatusAndAgentId(module, agentId, DispatcherStatus.RUNNING) > 0;
    }

    @Override
    public void deleteCreateTaskByAgentId(String agentId) {
        dispatcherDao.deleteCreateTaskByAgentId(agentId);
    }

    @Override
    public List<DispatcherTaskBean> findByAgentIdAndModuleWithStatus(String agentId, ModuleType module,
                                                                     List<DispatcherStatus> statusList) {
        return dispatcherDao.findByAgentIdAndModuleWithStatus(agentId, module,
                CollectionUtils.isEmpty(statusList) ? null : statusList);
    }

    @Override
    public void batchCreate(List<DispatcherTask> taskList, Date executeTime) {
        List<DispatcherTaskBean> taskBeanList = taskList.stream().map(task -> {
            ModuleType moduleType = task.getModuleType();
            Date created = new Date();
            return DispatcherTaskBean.builder()
                    .agentId(task.getAgentId())
                    .botId(task.getBotId())
                    .createUserId(task.getCreateUserId())
                    .createUserName(task.getCreateUserName())
                    .params(JsonUtils.toJsonWithOutException(task.getParams()))
                    .module(moduleType)
                    .ruleId(task.getRuleId())
                    .retryCount(0)
                    .retryLimit(moduleTypeWrapper.getRetryLimit(moduleType))
                    .retryWaitSecond(moduleTypeWrapper.getRetryWaitSecond(moduleType))
                    .executeTime(executeTime)
                    .created(created)
                    .updated(created)
                    .status(DispatcherStatus.CREATE)
                    .id(Optional.ofNullable(task.getId()).orElse(UUID.randomUUID().toString()))
                    .serialKey(DispatcherTaskBean.initSerialKey(task.getAgentId(), task.getSerialKey(), moduleType))
                    .tenantId(task.getTenantId())
                    .build();
        }).collect(Collectors.toList());
        log.debug("batch insert dispatcher task, size: {}, list: {}", taskBeanList.size(),
                JsonUtils.toJsonWithOutException(taskBeanList));
        dispatcherDao.batchInsert(taskBeanList);
    }
}
