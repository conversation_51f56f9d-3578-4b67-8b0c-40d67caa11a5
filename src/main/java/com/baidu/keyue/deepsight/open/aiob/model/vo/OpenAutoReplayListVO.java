package com.baidu.keyue.deepsight.open.aiob.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 小秘书查询返回对象
 *
 * <AUTHOR>
 * @date 2025/8/13 16:38
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class OpenAutoReplayListVO {

    /**
     * oneId
     */
    private String oneId;

    /**
     * 手机号
     */
    private String phoneNumber;

}
