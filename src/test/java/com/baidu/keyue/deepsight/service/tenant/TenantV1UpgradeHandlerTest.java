package com.baidu.keyue.deepsight.service.tenant;

import com.baidu.keyue.deepsight.database.doris.DorisTableTemplateConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TenantV1UpgradeHandlerTest{

    @InjectMocks
    private TenantV1UpgradeHandler tenantV1UpgradeHandler;

    @Mock
    private DorisTableTemplateConfig dorisTableTemplateConfig;

    @Mock
    private TenantV2UpgradeHandler tenantV2UpgradeHandler;

    @Test
    void testInit() throws IOException {
        ReflectionTestUtils.setField(tenantV1UpgradeHandler, "dorisTableTemplateConfig", dorisTableTemplateConfig);
        ReflectionTestUtils.setField(tenantV1UpgradeHandler, "tenantV2UpgradeHandler", tenantV2UpgradeHandler);

        // Setup mock data
        TableFieldMetaInfo mockUserField1 = createFieldMetaInfo("mobile_list");
        TableFieldMetaInfo mockUserField2 = createFieldMetaInfo("email_list");
        List<TableFieldMetaInfo> mockUserFields = List.of(mockUserField1, mockUserField2);
    
        TableFieldMetaInfo aiobRecordField = createFieldMetaInfo("deepsight_datetime");
        List<TableFieldMetaInfo> aiobRecordFields = List.of(aiobRecordField);
    
        TableFieldMetaInfo aiobSessionField = createFieldMetaInfo("BAIDUID");
        List<TableFieldMetaInfo> aiobSessionFields = List.of(aiobSessionField);
    
        TableFieldMetaInfo keyueSessionField = createFieldMetaInfo("UNIONID");
        List<TableFieldMetaInfo> keyueSessionFields = List.of(keyueSessionField);
    
        TableFieldMetaInfo memoryTableField = createFieldMetaInfo("deepsight_update_datetime");
        List<TableFieldMetaInfo> memoryTableFields = List.of(memoryTableField);
    
        // Mock behavior
        when(dorisTableTemplateConfig.getUserTableFieldsInfo()).thenReturn(mockUserFields);
        when(dorisTableTemplateConfig.getAiobRecordFieldsInfo()).thenReturn(aiobRecordFields);
        when(dorisTableTemplateConfig.getAiobSessionFieldsInfo()).thenReturn(aiobSessionFields);
        when(dorisTableTemplateConfig.getKeyueSessionFieldsInfo()).thenReturn(keyueSessionFields);
        when(dorisTableTemplateConfig.getUserMemoryTableFieldsInfo()).thenReturn(memoryTableFields);
    
        // Execute the method
        tenantV1UpgradeHandler.init();
        // Verify results
        assertEquals(2, tenantV1UpgradeHandler.version);
        assertNotNull(tenantV1UpgradeHandler.sqlList);
        assertNotNull(tenantV1UpgradeHandler.nextHandlers);
        
    }

    private TableFieldMetaInfo createFieldMetaInfo(String enField) {
        TableFieldMetaInfo field = new TableFieldMetaInfo();
        field.setEnField(enField);
        return field;
    }

}