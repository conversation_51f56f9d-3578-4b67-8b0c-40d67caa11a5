package com.baidu.keyue.deepsight.models.rules.dto;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.enums.LogicEnum;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * @className: DqlParseResult
 * @description:
 * @author: wangzhongcheng
 * @date: 2024/12/21 16:45
 */
@Data
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class DqlParseResult {

    /**
     * 查询字段
     */
    private String select = "*";
    /**
     * from 需要查询的表 比如 user
     */
    private String from = "";
    /**
     * join 需要 join 的表 usery, order, city
     */
    private List<String> innerJoin = new ArrayList<>();
    /**
     * joinOn 条件和 join 对应 比如 user.id = u_order.user_id, city.id = u_order.city_id
     */
    private List<String> joinOn = new ArrayList<>();
    /**
     * where 条件 比如 user.id = 1, user.name like '%张%'
     */
    private List<String> where = new ArrayList<>();
    /**
     * groupBy 分组字段 比如 u_order.city_id, user.id
     */
    private List<String> groupBy = new ArrayList<>();
    /**
     * having 条件 avg(u_order.price) > 100, user_id in (1,2)
     */
    private List<String> having = new ArrayList<>();

    private LogicEnum relation = LogicEnum.AND;

    /**
     * orderBy 排序 比如 u_order.price desc, user.id asc
     */
    private String orderBy = "";
    private boolean isAsc = true;

    /**
     * limit 1, 2
     */
    private Integer offset;
    private Integer size;

    /**
     * 规则中涉及到的字段表结构信息 比如:
     *{
     *  "`deep_sight_dev`.`table`": { // 库名.表名
     *      "`field`": "dataType"   // 字段名: 数据类型
     *  }
     *}
     */
    private Map<String, Map<String, String>> tableSchemaMap = new HashMap<>();

    /**
     * 表别名映射
     * `deep_sight_dev`.`uuid_表名`: `deep_sight_dev`.`table`
     */
    private Map<String, String> aliasTableMap = new HashMap<>();

    /**
     * 解析成 doris sql - 查询数据
     */
    public String parseDorisSql() {
        String relationSplit = getRelationSplit();
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotEmpty(select)) {
            sb.append("SELECT ").append(select).append("\n");
        }
        if (StringUtils.isNotEmpty(from)) {
            sb.append("FROM ").append(from).append("\n");
        }
        for (int i = 0; i < innerJoin.size(); i++) {
            sb.append("INNER JOIN ").append(innerJoin.get(i));
            sb.append(" ON ").append(joinOn.get(i)).append("\n");
        }
        if (CollectionUtils.isNotEmpty(where)) {
            sb.append("WHERE ").append(String.join(relationSplit, where)).append("\n");
        }
        if (CollectionUtils.isNotEmpty(groupBy)) {
            sb.append("GROUP BY ").append(String.join(Constants.SEPARATOR, groupBy)).append("\n");
        }
        if (CollectionUtils.isNotEmpty(having)) {
            sb.append("HAVING ").append(String.join(relationSplit, having)).append("\n");
        }
        if (StringUtils.isNotEmpty(orderBy)) {
            sb.append("ORDER BY ").append(orderBy).append(getOrder()).append("\n");
        }
        if (Objects.nonNull(offset) && Objects.nonNull(size)) {
            sb.append("LIMIT ").append(String.format("%d, %d", offset, size)).append("\n");
        }
        return sb.toString();
    }

    /**
     * 解析成 doris sql - 查询数据记录总数
     */
    public String parseCountSql() {
        String relationSplit = getRelationSplit();
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT COUNT(*)").append("\n");
        String sqlFrom = StringUtils.contains(from, "`") ? from : String.format("`%s`", from);
        sb.append("FROM ").append(sqlFrom).append("\n");
        for (int i = 0; i < innerJoin.size(); i++) {
            sb.append("INNER JOIN ").append(innerJoin.get(i));
            sb.append(" ON ").append(joinOn.get(i)).append("\n");
        }
        if (CollectionUtils.isNotEmpty(where)) {
            sb.append("WHERE ").append(String.join(relationSplit, where)).append("\n");
        }
        if (CollectionUtils.isNotEmpty(groupBy)) {
            sb.append("GROUP BY ").append(String.join(Constants.SEPARATOR, groupBy)).append("\n");
        }
        if (CollectionUtils.isNotEmpty(having)) {
            sb.append("HAVING ").append(String.join(relationSplit, having)).append("\n");
        }
        return sb.toString();
    }

    /**
     * 解析成抽样统计的sql
     * 案例：从基础用户表中随机抽取500条数据，然后统计这部分数据的数量
     * WITH A AS (
     *      SELECT *
     *      FROM `deep_sight_dev`.`mock_user_36957887057482752`
     *      WHERE oneId IN (
     *          SELECT oneId
     *          FROM `deep_sight_dev`.`mock_user_36957887057482752` TABLESAMPLE(500 rows) LIMIT 500))
     * SELECT COUNT(*)
     * FROM `A`
     * WHERE ((A.`gender` IN ('1')) AND (A.`city` IN ('北京')))
     * 
     * @param sampleNumber 抽样数量
     * @param userTableName 基础用户表名
     * @return 抽样统计的sql
     */
    public String parseSampleStatisticsSql(int sampleNumber, String userTableName) {
        StringBuilder sb = new StringBuilder();
        // 获取主查询
        String countSql = parseCountSql();

        // 构建 with 语句 + 主查询
        String withClause = buildWithClause(sampleNumber, userTableName, countSql);
        sb.append(withClause).append(Constants.ENTER_KEY);

        for (Map.Entry<String, String> alisEntry : aliasTableMap.entrySet()) {
            countSql = countSql.replaceAll(alisEntry.getValue(), alisEntry.getKey());
        }
        sb.append(countSql);

        return sb.toString();
    }

    public String getRelationSplit() {
        return relation == LogicEnum.AND ? " AND " : " OR ";
    }

    private String getOrder() {
        return isAsc ? " ASC" : " DESC";
    }

    /**
     * 添加字段类型信息
     */
    public void addFieldSchema(DqlParseResult parseResult) {
        aliasTableMap.putAll(parseResult.getAliasTableMap());
        tableSchemaMap.putAll(parseResult.getTableSchemaMap());
    }

    /**
     * 设置表别名
     * @param alias
     * @param table
     */
    public void aliasTable(String alias, String table) {
        aliasTableMap.put(alias, table);
    }

    /**
     * 添加字段类型信息
     * @param field `deep_sight_dev`.`table`.`field`
     * @param dataType int、varchar
     */
    public void addFieldSchema(String field, String dataType) {
        // 0-库名 1-表名 2-字段名
        String[] split = StringUtils.split(field, Constants.POINT);
        if (split.length != 3) {
            log.error("请检查field格式: field {}, dataType {}", field, dataType);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "请检查field格式");
        }
        String key = String.format("%s.%s", split[0], split[1]);
        if (!tableSchemaMap.containsKey(key)) {
            tableSchemaMap.put(key, new HashMap<>());
        }

        tableSchemaMap.get(key).put(split[2], dataType);
    }

    /**
     * 根据构建 WITH 子句
     * @param sampleNumber 抽样数量
     * @param userTableName 基础用户表名
     * @return WITH 子句的字符串sql
     */
    private String buildWithClause(int sampleNumber, String userTableName, String countSql) {
        StringBuilder sqlQuery = new StringBuilder("WITH ");

        // 遍历 aliasTableMap 中的每个键值对并构建相应的查询子句
        int index = 0;
        for (Map.Entry<String, String> entry : aliasTableMap.entrySet()) {
            if (index > 0) {
                sqlQuery.append(", ");
            }

            // 表的别名
            String tableAlias = entry.getKey();
            // 表的名字
            String tableName = entry.getValue();
            // 主sql涉及到的字段
            String tableFields = tableSchemaMap.get(tableName).keySet()
                    .stream().filter(field -> StringUtils.contains(countSql, field))
                    .collect(Collectors.joining(Constants.SEPARATOR));

            // 添加每个 WITH 子句
            sqlQuery.append(tableAlias).append(" AS (")
                    .append(" SELECT ").append(tableFields)
                    .append(" FROM ").append(tableName)
                    .append(" WHERE ").append(Constants.TABLE_USER_ONE_ID)
                    .append(" IN (SELECT ").append(Constants.TABLE_USER_ONE_ID)
                    .append(" FROM ").append(userTableName)
                    .append(String.format(" TABLESAMPLE(%d rows) LIMIT %d))", sampleNumber, sampleNumber));

            index++;
        }

        return sqlQuery.toString();
    }
}
