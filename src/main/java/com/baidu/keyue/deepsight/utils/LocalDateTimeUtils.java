package com.baidu.keyue.deepsight.utils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.ZoneId;
import java.util.Date;
import java.time.temporal.ChronoUnit;

/**
 * LocalDateTime 工具类
 * JDK1.8+ 推荐
 *
 * <AUTHOR>
 * @date 2025/8/13 10:46
 */
public class LocalDateTimeUtils {

    // 常用格式
    public static final String DEFAULT_DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String DEFAULT_DATE_PATTERN = "yyyy-MM-dd";
    public static final String DEFAULT_TIME_PATTERN = "HH:mm:ss";

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(DEFAULT_DATE_TIME_PATTERN);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DEFAULT_DATE_PATTERN);
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern(DEFAULT_TIME_PATTERN);

    /**
     * 获取当前时间
     */
    public static LocalDateTime now() {
        return LocalDateTime.now();
    }

    /**
     * 获取当前日期
     */
    public static LocalDate today() {
        return LocalDate.now();
    }

    /**
     * 格式化 LocalDateTime -> String
     */
    public static String format(LocalDateTime dateTime) {
        return dateTime.format(DATE_TIME_FORMATTER);
    }

    public static String format(LocalDate date) {
        return date.format(DATE_FORMATTER);
    }

    public static String format(LocalTime time) {
        return time.format(TIME_FORMATTER);
    }

    /**
     * 解析 String -> LocalDateTime
     */
    public static LocalDateTime parseDateTime(String dateTimeStr) {
        return LocalDateTime.parse(dateTimeStr, DATE_TIME_FORMATTER);
    }

    public static LocalDate parseDate(String dateStr) {
        return LocalDate.parse(dateStr, DATE_FORMATTER);
    }

    public static LocalTime parseTime(String timeStr) {
        return LocalTime.parse(timeStr, TIME_FORMATTER);
    }

    /**
     * 预编译的日期格式化器，避免重复创建
     */
    private static final DateTimeFormatter DATETIME_FORMATTER_WITH_SLASH = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
    private static final DateTimeFormatter DATE_FORMATTER_WITH_SLASH = DateTimeFormatter.ofPattern("yyyy/MM/dd");

    /**
     * 灵活解析日期字符串，支持多种格式
     * 支持格式：yyyy-MM-dd HH:mm:ss, yyyy-MM-dd, yyyy/MM/dd HH:mm:ss, yyyy/MM/dd
     *
     * @param dateStr 日期字符串
     * @return LocalDate对象
     * @throws IllegalArgumentException 解析失败时抛出异常
     * <AUTHOR>
     */
    public static LocalDate parseFlexibleDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            throw new IllegalArgumentException("日期字符串不能为空");
        }

        String trimmedDateStr = dateStr.trim();

        // 按照最常见的格式优先尝试，提高性能
        try {
            // 1. 尝试标准日期时间格式 yyyy-MM-dd HH:mm:ss
            return LocalDateTime.parse(trimmedDateStr, DATE_TIME_FORMATTER).toLocalDate();
        } catch (Exception e) {
            // 继续尝试其他格式
        }

        try {
            // 2. 尝试标准日期格式 yyyy-MM-dd
            return LocalDate.parse(trimmedDateStr, DATE_FORMATTER);
        } catch (Exception e) {
            // 继续尝试其他格式
        }

        try {
            // 3. 尝试斜杠日期时间格式 yyyy/MM/dd HH:mm:ss
            return LocalDateTime.parse(trimmedDateStr, DATETIME_FORMATTER_WITH_SLASH).toLocalDate();
        } catch (Exception e) {
            // 继续尝试其他格式
        }

        try {
            // 4. 尝试斜杠日期格式 yyyy/MM/dd
            return LocalDate.parse(trimmedDateStr, DATE_FORMATTER_WITH_SLASH);
        } catch (Exception e) {
            // 所有格式都失败
        }

        throw new IllegalArgumentException("无法解析日期格式: " + dateStr +
            "，支持的格式: yyyy-MM-dd HH:mm:ss, yyyy-MM-dd, yyyy/MM/dd HH:mm:ss, yyyy/MM/dd");
    }

    /**
     * LocalDateTime 加减
     */
    public static LocalDateTime plusDays(LocalDateTime dateTime, long days) {
        return dateTime.plusDays(days);
    }

    public static LocalDateTime minusDays(LocalDateTime dateTime, long days) {
        return dateTime.minusDays(days);
    }

    public static LocalDateTime plusHours(LocalDateTime dateTime, long hours) {
        return dateTime.plusHours(hours);
    }

    public static LocalDateTime minusHours(LocalDateTime dateTime, long hours) {
        return dateTime.minusHours(hours);
    }

    /**
     * 比较两个 LocalDateTime
     */
    public static boolean isBefore(LocalDateTime t1, LocalDateTime t2) {
        return t1.isBefore(t2);
    }

    public static boolean isAfter(LocalDateTime t1, LocalDateTime t2) {
        return t1.isAfter(t2);
    }

    public static boolean isEqual(LocalDateTime t1, LocalDateTime t2) {
        return t1.isEqual(t2);
    }

    /**
     * LocalDateTime 与 Date 互转
     */
    public static Date toDate(LocalDateTime dateTime) {
        return Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static LocalDateTime toLocalDateTime(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    /**
     * 获取当天开始和结束时间
     */
    public static LocalDateTime startOfDay(LocalDateTime dateTime) {
        return dateTime.with(LocalTime.MIN);
    }

    public static LocalDateTime endOfDay(LocalDateTime dateTime) {
        return dateTime.with(LocalTime.MAX);
    }

    /**
     * 计算两个 LocalDate 之间相差的天数
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 相差天数，如果 endDate 在 startDate 之前返回负数
     */
    public static long daysBetween(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            return 0;
        }
        return ChronoUnit.DAYS.between(startDate, endDate);
    }

    /**
     * doris Date 转 LocalDate
     */
    public static LocalDate dorisDate2LocalDate(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof java.sql.Date) {
            return ((java.sql.Date) value).toLocalDate();
        }
        if (value instanceof java.time.LocalDate) {
            return (LocalDate) value;
        }
        throw new IllegalArgumentException("Unsupported date type: " + value.getClass());
    }
}