package com.baidu.keyue.deepsight.database.doris;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName DorisTableTemplateConfig
 * @Description doris模板包配置
 * <AUTHOR>
 * @Date 2025/3/18 3:04 PM
 */
@Slf4j
@Component
public class DorisTableTemplateConfig {

    @Value("${aiob_session_field_info:classpath:datamanage/aiob_session_service_field_info.json}")
    private String sessionFieldsInfo;
    @Value("${usern_field_info:classpath:datamanage/user_field_info.json}")
    private String userFieldsInfo;
    @Value("${aiob_talk_field_info:classpath:datamanage/aiob_record_field_info.json}")
    private String aiobTalkFieldsInfo;
    @Value("${customer_talk_field_info:classpath:datamanage/keyue_record_field_info.json}")
    private String customerTalkFieldsInfo;
    @Value("${user_memory:classpath:datamanage/memory_extract_field_info.json}")
    private String userMemoryFieldsInfo;
    @Autowired
    private ApplicationContext context;

    private List<TableFieldMetaInfo> userTableFieldsInfo = new ArrayList<>();
    private List<TableFieldMetaInfo> aiobSessionFieldsInfo = new ArrayList<>();
    private List<TableFieldMetaInfo> aiobRecordFieldsInfo = new ArrayList<>();
    private List<TableFieldMetaInfo> keyueSessionFieldsInfo = new ArrayList<>();
    private List<TableFieldMetaInfo> userMemoryTableFieldsInfo = new ArrayList<>();

    @PostConstruct
    private void initTableFieldsInfo() {
        try {
            InputStream userFields = context.getResource(userFieldsInfo).getInputStream();
            InputStream aiobFields = context.getResource(sessionFieldsInfo).getInputStream();
            InputStream aiobTalkFields = context.getResource(aiobTalkFieldsInfo).getInputStream();
            InputStream customerTalkFields = context.getResource(customerTalkFieldsInfo).getInputStream();
            InputStream userMemoryFields = context.getResource(userMemoryFieldsInfo).getInputStream();
            List<JSONObject> userFieldJson = JSON.parseObject(userFields, List.class);
            List<JSONObject> sessionFieldJson = JSON.parseObject(aiobFields, List.class);
            List<JSONObject> aiobTalkFieldsJson = JSON.parseObject(aiobTalkFields, List.class);
            List<JSONObject> customerTalkFieldsJson = JSON.parseObject(customerTalkFields, List.class);
            List<JSONObject> userMemoryFieldsJson = JSON.parseObject(userMemoryFields, List.class);
            userFieldJson.forEach(json -> {
                json.remove("id");
                userTableFieldsInfo.add(JSON.parseObject(JSON.toJSONString(json), TableFieldMetaInfo.class));
            });
            sessionFieldJson.forEach(json -> {
                json.remove("id");
                aiobSessionFieldsInfo.add(JSON.parseObject(JSON.toJSONString(json), TableFieldMetaInfo.class));
            });
            aiobTalkFieldsJson.forEach(json -> aiobRecordFieldsInfo.add(JSON.parseObject(JSON.toJSONString(json), TableFieldMetaInfo.class)));
            customerTalkFieldsJson.forEach(json -> keyueSessionFieldsInfo.add(JSON.parseObject(JSON.toJSONString(json), TableFieldMetaInfo.class)));
            userMemoryFieldsJson.forEach(json -> userMemoryTableFieldsInfo.add(JSON.parseObject(JSON.toJSONString(json), TableFieldMetaInfo.class)));
        } catch (Exception e) {
            log.error("read init table fields info error");
        }
    }

    public List<TableFieldMetaInfo> getUserTableFieldsInfo() {
        return userTableFieldsInfo;
    }

    public List<TableFieldMetaInfo> getAiobSessionFieldsInfo() {
        return aiobSessionFieldsInfo;
    }

    public List<TableFieldMetaInfo> getAiobRecordFieldsInfo() {
        return aiobRecordFieldsInfo;
    }

    public List<TableFieldMetaInfo> getKeyueSessionFieldsInfo() {
        return keyueSessionFieldsInfo;
    }

    public List<TableFieldMetaInfo> getUserMemoryTableFieldsInfo() {
        return userMemoryTableFieldsInfo;
    }
}
