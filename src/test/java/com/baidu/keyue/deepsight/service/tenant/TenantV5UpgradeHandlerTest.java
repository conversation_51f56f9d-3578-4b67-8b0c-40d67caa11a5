package com.baidu.keyue.deepsight.service.tenant;

import com.baidu.keyue.deepsight.database.doris.DorisTableTemplateConfig;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.TenantInfoMapper;
import com.baidu.keyue.deepsight.service.datamanage.impl.TableRecordCommonService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class TenantV5UpgradeHandlerTest {


    @Mock
    private TenantInfoMapper tenantInfoMapper;

    @Mock
    private TransactionTemplate transactionTemplate;

    private TenantDTO tenantDTO;

    private TenantInfo tenantInfo;
    @Mock
    private DorisService dorisService;

    @Mock
    private TableRecordCommonService tableRecordCommonService;

    @Mock
    private DorisTableTemplateConfig dorisTableTemplateConfig;

    @InjectMocks
    private TenantV5UpgradeHandler tenantV5UpgradeHandler;

    @Mock
    private TableFieldMetaInfo tableFieldMetaInfo;
    @Mock
    private TenantInfoService tenantInfoService;
    

    @BeforeEach
    void setup() {
        tenantInfo = new TenantInfo();
        tenantInfo.setId(1L);
        tenantInfo.setVersion(6);
    
        tenantDTO = new TenantDTO();
        tenantDTO.setTenantId("1");
        tenantDTO.setIsInit(false);
        tenantDTO.setTenantInfo(tenantInfo);
        ReflectionTestUtils.setField(tenantV5UpgradeHandler, "dorisService", dorisService);
        ReflectionTestUtils.setField(tenantV5UpgradeHandler, "transactionTemplate", transactionTemplate);
        ReflectionTestUtils.setField(tenantV5UpgradeHandler, "tableRecordCommonService", tableRecordCommonService);
        ReflectionTestUtils.setField(tenantV5UpgradeHandler, "dorisTableTemplateConfig", dorisTableTemplateConfig);
        ReflectionTestUtils.setField(tenantV5UpgradeHandler, "tenantInfoService", tenantInfoService);
    }

    @Test
    void oldTableUpgradeShouldSkipCreateTableStatements() {
        tenantV5UpgradeHandler.sqlList = List.of(
            "CREATE TABLE test_table (id INT)",
            "ALTER TABLE test_table ADD COLUMN new_col VARCHAR(255)"
        );
        tenantV5UpgradeHandler.oldTableUpgrade("1");
    
        verify(dorisService, times(1)).operationSchema(anyString());
        verify(tableRecordCommonService).saveUpgradeFieldMetaInfo(anyString(), anyList(), anyMap());
    }

    @Test
    void oldTableUpgradeShouldHandleMultipleSqlStatements() {
        tenantV5UpgradeHandler.sqlList = List.of(
            "ALTER TABLE ^& ADD COLUMN col1 VARCHAR(255)",
            "ALTER TABLE ^& ADD COLUMN col2 INT"
        );
        tenantV5UpgradeHandler.oldTableUpgrade("1");
        verify(dorisService, times(2)).operationSchema(anyString());
    }@Test
    void oldTableUpgradeWhenSqlListIsEmpty() {
        tenantV5UpgradeHandler.sqlList = List.of();
        tenantV5UpgradeHandler.oldTableUpgrade("1");
        verify(dorisService, times(0)).operationSchema(anyString());
    }

    @Test
    void executeUpgradeShouldHandleNonInitTenant() {
        tenantInfo = new TenantInfo();
        tenantInfo.setId(1L);
        tenantInfo.setVersion(4);
    
        tenantDTO = new TenantDTO();
        tenantDTO.setTenantId("1");
        tenantDTO.setIsInit(false);
        tenantDTO.setTenantInfo(tenantInfo);
        ReflectionTestUtils.setField(tenantV5UpgradeHandler, "dorisService", dorisService);
        ReflectionTestUtils.setField(tenantV5UpgradeHandler, "transactionTemplate", transactionTemplate);
        ReflectionTestUtils.setField(tenantV5UpgradeHandler, "tableRecordCommonService", tableRecordCommonService);
        ReflectionTestUtils.setField(tenantV5UpgradeHandler, "tenantInfoMapper", tenantInfoMapper);
        tenantV5UpgradeHandler.sqlList = List.of(
                "ALTER TABLE ^& ADD COLUMN col1 VARCHAR(255)",
                "ALTER TABLE ^& ADD COLUMN col2 INT"
        );
        when(transactionTemplate.execute(any())).thenAnswer(invocation -> {
            ((TransactionCallback<?>) invocation.getArgument(0)).doInTransaction(null);
            return null;
        });
    
        tenantV5UpgradeHandler.executeUpgrade(tenantDTO);
    
        verify(tenantInfoMapper).updateByPrimaryKeySelective(any(TenantInfo.class));
        verify(dorisService, atLeastOnce()).operationSchema(anyString());
        verify(tableRecordCommonService).saveUpgradeFieldMetaInfo(anyString(), anyList(), anyMap());
    }

    @Test
    void executeUpgradeShouldHandleInitTenant() {
        tenantInfo = new TenantInfo();
        tenantInfo.setId(1L);
        tenantInfo.setVersion(4);
    
        tenantDTO = new TenantDTO();
        tenantDTO.setTenantId("1");
        tenantDTO.setIsInit(false);
        tenantDTO.setTenantInfo(tenantInfo);
        ReflectionTestUtils.setField(tenantV5UpgradeHandler, "dorisService", dorisService);
        ReflectionTestUtils.setField(tenantV5UpgradeHandler, "transactionTemplate", transactionTemplate);
        ReflectionTestUtils.setField(tenantV5UpgradeHandler, "tableRecordCommonService", tableRecordCommonService);
        ReflectionTestUtils.setField(tenantV5UpgradeHandler, "tenantInfoMapper", tenantInfoMapper);
    
        tenantDTO.setIsInit(true);
        tenantV5UpgradeHandler.sqlList = List.of(
                "ALTER TABLE ^& ADD COLUMN col1 VARCHAR(255)",
                "ALTER TABLE ^& ADD COLUMN col2 INT"
        );
        when(transactionTemplate.execute(any())).thenAnswer(invocation -> {
            ((TransactionCallback<?>) invocation.getArgument(0)).doInTransaction(null);
            return null;
        });
    
        tenantV5UpgradeHandler.executeUpgrade(tenantDTO);
    
        verify(tenantInfoMapper).updateByPrimaryKeySelective(any(TenantInfo.class));
        verify(dorisService, never()).operationSchema(anyString());
        verify(tableRecordCommonService, never()).saveUpgradeFieldMetaInfo(anyString(), anyList(), anyMap());
    }

    @Test
    void executeUpgradeShouldRollbackOnException() {
        tenantInfo = new TenantInfo();
        tenantInfo.setId(1L);
        tenantInfo.setVersion(4);
    
        tenantDTO = new TenantDTO();
        tenantDTO.setTenantId("1");
        tenantDTO.setIsInit(false);
        tenantDTO.setTenantInfo(tenantInfo);
        ReflectionTestUtils.setField(tenantV5UpgradeHandler, "dorisService", dorisService);
        ReflectionTestUtils.setField(tenantV5UpgradeHandler, "transactionTemplate", transactionTemplate);
        ReflectionTestUtils.setField(tenantV5UpgradeHandler, "tableRecordCommonService", tableRecordCommonService);
        ReflectionTestUtils.setField(tenantV5UpgradeHandler, "tenantInfoMapper", tenantInfoMapper);
        tenantV5UpgradeHandler.sqlList = List.of(
                "ALTER TABLE ^& ADD COLUMN col1 VARCHAR(255)",
                "ALTER TABLE ^& ADD COLUMN col2 INT"
        );
        when(transactionTemplate.execute(any())).thenThrow(new RuntimeException("Test exception"));
    
        assertThrows(RuntimeException.class, () -> {
            tenantV5UpgradeHandler.executeUpgrade(tenantDTO);
        });
    
        verify(tenantInfoMapper, never()).updateByPrimaryKeySelective(any(TenantInfo.class));
    }

    @Test
    void needUpdateTenantShouldReturnTrueWhenVersionIs6() {
        TenantInfo tenant = new TenantInfo();
        tenant.setTenantid("1");
        tenant.setVersion(5);
        when(tenantInfoService.queryTenantInfo(anyString())).thenReturn(tenant);
        assertTrue(tenantV5UpgradeHandler.needUpdateTenant(tenant));
    }

    @Test
    void needUpdateTenantShouldReturnFalseWhenVersionIsNot4() {
        TenantInfo tenant = new TenantInfo();
        tenant.setVersion(3);
        tenant.setTenantid("1");
        when(tenantInfoService.queryTenantInfo(anyString())).thenReturn(tenant);
        assertFalse(tenantV5UpgradeHandler.needUpdateTenant(tenant));
    }

    @Test
    void initShouldSetVersionAndInitializeFields() throws IOException {
        // Prepare test data
        TableFieldMetaInfo field1 = new TableFieldMetaInfo();
        field1.setEnField("callerNum");
        TableFieldMetaInfo field2 = new TableFieldMetaInfo();
        field2.setEnField("dicCategory");
        List<TableFieldMetaInfo> mockFields = List.of(field1, field2);
    
        // Mock behavior
        when(dorisTableTemplateConfig.getAiobSessionFieldsInfo()).thenReturn(Collections.singletonList(tableFieldMetaInfo));

        // Execute the method
        tenantV5UpgradeHandler.init();
    
        // Verify the results
        verify(dorisTableTemplateConfig).getAiobSessionFieldsInfo();
        // Verify version is set through parent class method
        assert(tenantV5UpgradeHandler.version == 6);
    }
}