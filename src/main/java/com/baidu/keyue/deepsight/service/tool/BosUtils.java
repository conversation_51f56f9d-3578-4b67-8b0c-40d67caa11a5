package com.baidu.keyue.deepsight.service.tool;

import com.baidu.keyue.deepsight.config.BosConfig;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.datamanage.dto.BosProperty;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidubce.auth.DefaultBceCredentials;
import com.baidubce.services.bos.BosClient;
import com.baidubce.services.bos.BosClientConfiguration;
import com.baidubce.services.bos.model.BosObject;
import com.baidubce.services.bos.model.BosObjectSummary;
import com.baidubce.services.bos.model.ListObjectsResponse;
import com.baidubce.services.bos.model.PutObjectResponse;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.net.URL;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @ClassName BosUtils
 * @Description Bos工具类
 * <AUTHOR>
 * @Date 2025/2/17 2:24 PM
 */
@Slf4j
@Service
public class BosUtils {

    @Resource
    private BosConfig bosConfig;

    private BosClient client;
    private BosProperty bosProperty;

    /**
     * 文件名合法性校验正则（允许字母、数字、下划线、短横线、点）
     */
    private static final Pattern FILE_NAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_.-]+$");


    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        String accessKeyId = bosConfig.getAccessKeyId();
        String secretAccessKey = bosConfig.getSecretAccessKey();
        String endpoint = bosConfig.getEndpoint();
        BosClientConfiguration config = new BosClientConfiguration();
        config.setCredentials(new DefaultBceCredentials(accessKeyId, secretAccessKey));
        config.setEndpoint(endpoint);
        // 不确定客户端是否会自动关闭，官方文档未见说明
        client = new BosClient(config);
        BosProperty property = new BosProperty();
        property.setAccessKey(accessKeyId);
        property.setSecret(secretAccessKey);
        property.setEndPoint(endpoint);
        bosProperty = property;
    }

    public BosClient getClient() {
        return this.client;
    }

    public BosProperty getBosProperty() {
        return this.bosProperty;
    }

    /**
     * 普通文件上传
     *
     * @param bucket      桶名
     * @param key         文件名KEY
     * @param inputStream 字节流
     * @return
     */
    public PutObjectResponse putObject(String bucket, String key, InputStream inputStream) {
        return this.client.putObject(bucket, key, inputStream);
    }

    /**
     * 普通文件删除
     *
     * @param bucket 桶名
     * @param key    文件名KEY
     * @return
     */
    public void deleteObject(String bucket, String key) {
        if (doesObjectExist(bucket, key)) {
            this.client.deleteObject(bucket, key);
        }
    }

    /**
     * 文件是否存在
     *
     * @param bucket 桶名
     * @param key    文件名KEY
     * @return 是否存在
     */
    public boolean doesObjectExist(String bucket, String key) {
        checkObjectKey(key);
        return this.client.doesObjectExist(bucket, key);
    }

    /**
     * 普通文件获取
     * 如果文件不存在，则返回null
     *
     * @param bucket 桶名
     * @param key    文件名KEY
     * @return 是否存在
     */
    public BosObject getObject(String bucket, String key) {
        return doesObjectExist(bucket, key) ? this.client.getObject(bucket, key) : null;
    }

    /**
     * 生成 bos 文件签名地址
     *
     * @param bucket
     * @param objectKey
     * @return
     */
    public String generatePreSignedUrl(String bucket, String objectKey) {
        URL u = this.client.generatePresignedUrl(bucket, objectKey, 86400 * 7 - 1);
        return u.toString();
    }

    /**
     * 根据 bos 路径前缀查询 Objects
     *
     * @param bucket
     * @param prefix
     * @return
     */
    public List<String> listObjectKeys(String bucket, String prefix) {
        ListObjectsResponse response = this.client.listObjects(bucket, prefix);
        return response.getContents().stream()
                .map(BosObjectSummary::getKey)
                .collect(Collectors.toList());
    }

    /**
     * 检查文件KEY是否合法
     *
     * @param objectKey 文件唯一标识
     */
    public void checkObjectKey(String objectKey) {
        if (!isSafeObjectKey(objectKey)) {
            log.error("objectKey不合法:{}", objectKey);
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "objectKey不合法");
        }
    }


    /**
     * 检查文件KEY是否合法
     *
     * @param objectKey 文件唯一标识
     * @return 是否合法
     */
    public boolean isSafeObjectKey(String objectKey) {
        if (objectKey == null || objectKey.trim().isEmpty()) {
            return false;
        }
        // 2. 防止路径穿越
        if (objectKey.contains("..")) {
            return false;
        }
        try {
            Path path = Paths.get(objectKey).normalize();
            // 3. 防止路径穿越（检查是否被normalize后跳目录了）
            if (!Objects.equals(path.toString(), objectKey)) {
                return false;
            }
            // 3. 获取文件名并校验
            String fileName = path.getFileName().toString();
            return !fileName.startsWith(".") && FILE_NAME_PATTERN.matcher(fileName).matches();
        } catch (Exception e) {
            // 非法路径（可能包含非法字符等）
            return false;
        }
    }
}
