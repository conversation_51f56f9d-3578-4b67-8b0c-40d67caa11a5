package com.baidu.keyue.deepsight.models.sop;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@Data
public class SopTaskCfgRequestBase {

    /**
     * 机器人版本列表
     */
    @NotEmpty(message = "机器人版本列表不能为空")
    private List<String> robotVersion;

    /**
     * 是否自动应答：0-否，1-是
     */
    @Range(min = 0, max = 1, message = "是否过滤小秘书参数不正确")
    private Byte isAutoAnswer;
    /**
     * 标签列表
     */
    @Size(min = 1, message = "至少选择一个标签")
    private List<String> tags;

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String taskName;

    /**
     * 机器人名称
     */
    @NotBlank(message = "机器人名称不能为空")
    private String robotName;


    /**
     * 任务ID
     */
    @NotBlank(message = "任务ID不能为空")
    private String taskId;

    /**
     * 机器人ID
     */
    @NotBlank(message = "机器人ID不能为空")
    private String robotId;

    /**
     * 机器人场景
     */
    @NotNull(message = "机器人场景不能为空")
    private Byte robotScene;

    /**
     *
     * 用户配置信息
     */
    private SopUserConfigUpdateRequest userConfig;
}
