package com.baidu.keyue.deepsight.service.rules.impl;

import com.baidu.keyue.deepsight.database.doris.DorisConfiguration;
import com.baidu.keyue.deepsight.enums.FilterTypeEnum;
import com.baidu.keyue.deepsight.enums.FuncEnum;
import com.baidu.keyue.deepsight.enums.RuleTypeEnum;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.rules.dto.RuleFilter;
import com.baidu.keyue.deepsight.models.rules.dto.RuleGroup;
import com.baidu.keyue.deepsight.models.rules.dto.RuleNode;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelField;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.LabelFieldMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.context.TestPropertySource;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
@TestPropertySource(properties = {
        "kafka.topics.dataSync=deep_sight_data_sync",
        "file.upload.suffixNames=.csv,.xlsx"
})
public class RuleParseServiceImplTest{


    @Mock
    private DorisConfiguration dorisConfiguration;
    @Mock
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @InjectMocks
    private RuleParseServiceImpl ruleParseService;

    private final String tenantId = "test_tenant";

    @Mock
    private LabelFieldMapper labelFieldMapper;

    @Test
    void testCheckRuleGroupTenantPermissionDatasetTypeInvalid() {
        RuleNode ruleNode = new RuleNode();
        ruleNode.setType(RuleTypeEnum.DATASET);
        RuleFilter filter = new RuleFilter();
        filter.setFieldId(1L);
        ruleNode.setFilters(List.of(filter));
    
        RuleGroup ruleGroup = new RuleGroup();
        ruleGroup.setRuleGroups(Collections.emptyList());
        ruleGroup.setRuleNodes(List.of(ruleNode));
    
        TableFieldMetaInfo fieldMetaInfo = new TableFieldMetaInfo();
        fieldMetaInfo.setTableEnName("dataset_other_tenant_table");
    
        when(tableFieldMetaInfoMapper.selectByExample(any())).thenReturn(List.of(fieldMetaInfo));
    
        assertThrows(DeepSightException.ParamsErrorException.class,
                () -> ruleParseService.checkRuleGroupTenantPermission(ruleGroup, tenantId));
    }

    @Test
    void testCheckRuleGroupTenantPermissionMixedTypes() {
        // Label node
        RuleNode labelNode = new RuleNode();
        labelNode.setType(RuleTypeEnum.LABEL);
        RuleFilter labelFilter = new RuleFilter();
        labelFilter.setFieldId(1L);
        labelNode.setFilters(List.of(labelFilter));
    
        // User node
        RuleNode userNode = new RuleNode();
        userNode.setType(RuleTypeEnum.USER);
        RuleFilter userFilter = new RuleFilter();
        userFilter.setFieldId(2L);
        userNode.setFilters(List.of(userFilter));
    
        RuleGroup ruleGroup = new RuleGroup();
        ruleGroup.setRuleGroups(Collections.emptyList());
        ruleGroup.setRuleNodes(List.of(labelNode, userNode));
    
        // Mock label field
        LabelField labelField = new LabelField();
        labelField.setLabelTable("label_" + tenantId + "_table");
    
        // Mock table field
        TableFieldMetaInfo fieldMetaInfo = new TableFieldMetaInfo();
        fieldMetaInfo.setTableEnName("user_" + tenantId + "_table");
    
        when(labelFieldMapper.selectByExample(any())).thenReturn(List.of(labelField));
        when(tableFieldMetaInfoMapper.selectByExample(any())).thenReturn(List.of(fieldMetaInfo));
    
        assertDoesNotThrow(() -> ruleParseService.checkRuleGroupTenantPermission(ruleGroup, tenantId));
    }

    @Test
    void testCheckRuleGroupTenantPermissionEmptyRuleGroup() {
        RuleGroup ruleGroup = new RuleGroup();
        ruleGroup.setRuleGroups(Collections.emptyList());
        ruleGroup.setRuleNodes(Collections.emptyList());
    
        assertDoesNotThrow(() -> ruleParseService.checkRuleGroupTenantPermission(ruleGroup, tenantId));
    }

    @Test
    void testCheckRuleGroupTenantPermissionNestedRuleGroups() {
        RuleGroup childGroup = new RuleGroup();
        childGroup.setRuleGroups(Collections.emptyList());
        childGroup.setRuleNodes(Collections.emptyList());
    
        RuleGroup parentGroup = new RuleGroup();
        parentGroup.setRuleGroups(List.of(childGroup));
        parentGroup.setRuleNodes(Collections.emptyList());
    
        assertDoesNotThrow(() -> ruleParseService.checkRuleGroupTenantPermission(parentGroup, tenantId));
    }

    @Test
    void testCheckRuleGroupTenantPermissionLabelTypeValid() {
        RuleNode ruleNode = new RuleNode();
        ruleNode.setType(RuleTypeEnum.LABEL);
        RuleFilter filter = new RuleFilter();
        filter.setFieldId(1L);
        ruleNode.setFilters(List.of(filter));
    
        RuleGroup ruleGroup = new RuleGroup();
        ruleGroup.setRuleGroups(Collections.emptyList());
        ruleGroup.setRuleNodes(List.of(ruleNode));
    
        LabelField labelField = new LabelField();
        labelField.setLabelTable("prefix_" + tenantId + "_suffix");
    
        when(labelFieldMapper.selectByExample(any())).thenReturn(List.of(labelField));
    
        assertDoesNotThrow(() -> ruleParseService.checkRuleGroupTenantPermission(ruleGroup, tenantId));
    }

    @Test
    public void testCheckRuleGroupTenantPermissionLabelTypeInvalid() {
        RuleNode ruleNode = new RuleNode();
        ruleNode.setType(RuleTypeEnum.LABEL);
        RuleFilter filter = new RuleFilter();
        filter.setFieldId(1L);
        ruleNode.setFilters(List.of(filter));
    
        RuleGroup ruleGroup = new RuleGroup();
        ruleGroup.setRuleGroups(Collections.emptyList());
        ruleGroup.setRuleNodes(List.of(ruleNode));
    
        LabelField labelField = new LabelField();
        labelField.setLabelTable("other_tenant_table");
    
        when(labelFieldMapper.selectByExample(any())).thenReturn(List.of(labelField));
    
        assertThrows(DeepSightException.ParamsErrorException.class,
                () -> ruleParseService.checkRuleGroupTenantPermission(ruleGroup, tenantId));
    }

    @Test
    public void testCheckRuleGroupTenantPermissionUserTypeValid() {
        RuleNode ruleNode = new RuleNode();
        ruleNode.setType(RuleTypeEnum.USER);
        RuleFilter filter = new RuleFilter();
        filter.setFieldId(1L);
        ruleNode.setFilters(List.of(filter));
    
        RuleGroup ruleGroup = new RuleGroup();
        ruleGroup.setRuleGroups(Collections.emptyList());
        ruleGroup.setRuleNodes(List.of(ruleNode));
    
        TableFieldMetaInfo fieldMetaInfo = new TableFieldMetaInfo();
        fieldMetaInfo.setTableEnName("user_" + tenantId + "_table");
    
        when(tableFieldMetaInfoMapper.selectByExample(any())).thenReturn(List.of(fieldMetaInfo));
    
        assertDoesNotThrow(() -> ruleParseService.checkRuleGroupTenantPermission(ruleGroup, tenantId));
    }

    @Test
    public void testCheckRuleNodeNullType() {
        RuleNode ruleNode = new RuleNode();
        assertThrows(DeepSightException.ParamsErrorException.class,
                () -> ruleParseService.checkRuleNode(ruleNode));
    }

    @Test
    public void testCheckRuleNodeEmptyFilters() {
        RuleNode ruleNode = new RuleNode();
        ruleNode.setType(RuleTypeEnum.LABEL);
        ruleNode.setFilters(Collections.emptyList());
        
        ruleParseService.checkRuleNode(ruleNode);
        
        assertNotNull(ruleNode.getFilters());
        assertTrue(ruleNode.getFilters().isEmpty());
    }

    @Test
    public void testCheckRuleNodeLabelType() {
        RuleNode ruleNode = new RuleNode();
        ruleNode.setType(RuleTypeEnum.LABEL);
        RuleFilter filter = new RuleFilter();
        filter.setFieldId(1L);
        filter.setType(FilterTypeEnum.STRING);
        filter.setFunction(FuncEnum.EQUALS);
        filter.setParams(List.of("value"));
        ruleNode.setFilters(List.of(filter));
    
        LabelField labelField = new LabelField();
        labelField.setId(1L);
        labelField.setLabelTable("label_table");
        labelField.setFieldType("string");
    
        when(labelFieldMapper.selectByExample(any())).thenReturn(List.of(labelField));
        when(dorisConfiguration.getDb()).thenReturn("test_db");
    
        ruleParseService.checkRuleNode(ruleNode);
    
        assertEquals("`test_db`.`label_table`.`process_label_1`", filter.getFiled());
        assertEquals("string", filter.getFieldDataType());
        assertEquals("label_table", ruleNode.getDorisTableName());
    }

    @Test
    public void testCheckRuleNodeDatasetType() {
        RuleNode ruleNode = new RuleNode();
        ruleNode.setType(RuleTypeEnum.DATASET);
        RuleFilter filter = new RuleFilter();
        filter.setFieldId(1L);
        filter.setType(FilterTypeEnum.STRING);
        filter.setFunction(FuncEnum.EQUALS);
        filter.setParams(List.of("value"));
        ruleNode.setFilters(List.of(filter));
    
        TableFieldMetaInfo fieldMetaInfo = new TableFieldMetaInfo();
        fieldMetaInfo.setId(1L);
        fieldMetaInfo.setTableEnName("dataset_table");
        fieldMetaInfo.setEnField("field_name");
        fieldMetaInfo.setDataType("int");
        fieldMetaInfo.setDataTableId(100L);
    
        when(tableFieldMetaInfoMapper.selectByExample(any())).thenReturn(List.of(fieldMetaInfo));
        when(dorisConfiguration.getDb()).thenReturn("test_db");
    
        ruleParseService.checkRuleNode(ruleNode);
    
        assertEquals("`test_db`.`dataset_table`.`field_name`", filter.getFiled());
        assertEquals("int", filter.getFieldDataType());
        assertEquals(100L, ruleNode.getDataTableId());
        assertEquals("dataset_table", ruleNode.getDorisTableName());
    }

    @Test
    public void testCheckRuleNodeEmptyParamsForNonSpecialFunctions() {
        RuleNode ruleNode = new RuleNode();
        ruleNode.setType(RuleTypeEnum.LABEL);
        RuleFilter filter = new RuleFilter();
        filter.setType(FilterTypeEnum.NUMBER);
        filter.setFieldId(1L);
        filter.setFunction(FuncEnum.EQUALS);
        filter.setParams(Collections.emptyList());
        ruleNode.setFilters(List.of(filter));
    
        LabelField labelField = new LabelField();
        labelField.setId(1L);
        labelField.setLabelTable("label_table");
        labelField.setFieldType("string");
    
        when(labelFieldMapper.selectByExample(any())).thenReturn(List.of(labelField));
        when(dorisConfiguration.getDb()).thenReturn("test_db");
    
        assertThrows(DeepSightException.ParamsErrorException.class,
                () -> ruleParseService.checkRuleNode(ruleNode));
    }

    @Test
    public void testCheckRuleNodeIsNullFunctionWithEmptyParams() {
        RuleNode ruleNode = new RuleNode();
        ruleNode.setType(RuleTypeEnum.LABEL);
        RuleFilter filter = new RuleFilter();
        filter.setFieldId(1L);
        filter.setFunction(FuncEnum.IS_NULL);
        filter.setParams(Collections.emptyList());
        ruleNode.setFilters(List.of(filter));
    
        LabelField labelField = new LabelField();
        labelField.setId(1L);
        labelField.setLabelTable("label_table");
        labelField.setFieldType("string");
    
        when(labelFieldMapper.selectByExample(any())).thenReturn(List.of(labelField));
        when(dorisConfiguration.getDb()).thenReturn("test_db");
    
        ruleParseService.checkRuleNode(ruleNode);
    
        assertEquals("`test_db`.`label_table`.`process_label_1`", filter.getFiled());
        assertEquals("string", filter.getFieldDataType());
    }

    @Test
    public void testCheckRuleNodeNullRuleNode() {
        assertThrows(DeepSightException.ParamsErrorException.class,
                () -> ruleParseService.checkRuleNode(null));
    }

}