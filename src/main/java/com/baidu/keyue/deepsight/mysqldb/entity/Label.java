package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

public class Label implements Serializable, TaskInfoObj {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.user_id
     *
     * @mbg.generated
     */
    private String userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.catalog_id
     *
     * @mbg.generated
     */
    private Long catalogId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.label_name
     *
     * @mbg.generated
     */
    private String labelName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.label_value_update_mod
     *
     * @mbg.generated
     */
    private Byte labelValueUpdateMod;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.label_value_save_mod
     *
     * @mbg.generated
     */
    private Byte labelValueSaveMod;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.trigger_mod
     *
     * @mbg.generated
     */
    private Byte triggerMod;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.trigger_frequency
     *
     * @mbg.generated
     */
    private Byte triggerFrequency;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.trigger_frequency_value
     *
     * @mbg.generated
     */
    private String triggerFrequencyValue;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.exec_mod
     *
     * @mbg.generated
     */
    private Byte execMod;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.field
     *
     * @mbg.generated
     */
    private Long field;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.label_cal_status
     *
     * @mbg.generated
     */
    private Byte labelCalStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.last_cal_date
     *
     * @mbg.generated
     */
    private Date lastCalDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.del
     *
     * @mbg.generated
     */
    private Boolean del;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.creator
     *
     * @mbg.generated
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.modifier
     *
     * @mbg.generated
     */
    private String modifier;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.task
     *
     * @mbg.generated
     */
    private Long task;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.tenant_id
     *
     * @mbg.generated
     */
    private String tenantId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.recalculate
     *
     * @mbg.generated
     */
    private Boolean recalculate;

    /**
     * 是否为内置标签: 0-用户标签, 1-内置标签
     * This field corresponds to the database column label.is_builtin
     */
    private Boolean isBuiltin;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table label
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.id
     *
     * @return the value of label.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.id
     *
     * @param id the value for label.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.user_id
     *
     * @return the value of label.user_id
     *
     * @mbg.generated
     */
    public String getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.user_id
     *
     * @param userId the value for label.user_id
     *
     * @mbg.generated
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.catalog_id
     *
     * @return the value of label.catalog_id
     *
     * @mbg.generated
     */
    public Long getCatalogId() {
        return catalogId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.catalog_id
     *
     * @param catalogId the value for label.catalog_id
     *
     * @mbg.generated
     */
    public void setCatalogId(Long catalogId) {
        this.catalogId = catalogId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.label_name
     *
     * @return the value of label.label_name
     *
     * @mbg.generated
     */
    public String getLabelName() {
        return labelName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.label_name
     *
     * @param labelName the value for label.label_name
     *
     * @mbg.generated
     */
    public void setLabelName(String labelName) {
        this.labelName = labelName == null ? null : labelName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.label_value_update_mod
     *
     * @return the value of label.label_value_update_mod
     *
     * @mbg.generated
     */
    public Byte getLabelValueUpdateMod() {
        return labelValueUpdateMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.label_value_update_mod
     *
     * @param labelValueUpdateMod the value for label.label_value_update_mod
     *
     * @mbg.generated
     */
    public void setLabelValueUpdateMod(Byte labelValueUpdateMod) {
        this.labelValueUpdateMod = labelValueUpdateMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.label_value_save_mod
     *
     * @return the value of label.label_value_save_mod
     *
     * @mbg.generated
     */
    public Byte getLabelValueSaveMod() {
        return labelValueSaveMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.label_value_save_mod
     *
     * @param labelValueSaveMod the value for label.label_value_save_mod
     *
     * @mbg.generated
     */
    public void setLabelValueSaveMod(Byte labelValueSaveMod) {
        this.labelValueSaveMod = labelValueSaveMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.trigger_mod
     *
     * @return the value of label.trigger_mod
     *
     * @mbg.generated
     */
    public Byte getTriggerMod() {
        return triggerMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.trigger_mod
     *
     * @param triggerMod the value for label.trigger_mod
     *
     * @mbg.generated
     */
    public void setTriggerMod(Byte triggerMod) {
        this.triggerMod = triggerMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.trigger_frequency
     *
     * @return the value of label.trigger_frequency
     *
     * @mbg.generated
     */
    public Byte getTriggerFrequency() {
        return triggerFrequency;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.trigger_frequency
     *
     * @param triggerFrequency the value for label.trigger_frequency
     *
     * @mbg.generated
     */
    public void setTriggerFrequency(Byte triggerFrequency) {
        this.triggerFrequency = triggerFrequency;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.trigger_frequency_value
     *
     * @return the value of label.trigger_frequency_value
     *
     * @mbg.generated
     */
    public String getTriggerFrequencyValue() {
        return triggerFrequencyValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.trigger_frequency_value
     *
     * @param triggerFrequencyValue the value for label.trigger_frequency_value
     *
     * @mbg.generated
     */
    public void setTriggerFrequencyValue(String triggerFrequencyValue) {
        this.triggerFrequencyValue = triggerFrequencyValue == null ? null : triggerFrequencyValue.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.exec_mod
     *
     * @return the value of label.exec_mod
     *
     * @mbg.generated
     */
    public Byte getExecMod() {
        return execMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.exec_mod
     *
     * @param execMod the value for label.exec_mod
     *
     * @mbg.generated
     */
    public void setExecMod(Byte execMod) {
        this.execMod = execMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.field
     *
     * @return the value of label.field
     *
     * @mbg.generated
     */
    public Long getField() {
        return field;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.field
     *
     * @param field the value for label.field
     *
     * @mbg.generated
     */
    public void setField(Long field) {
        this.field = field;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.label_cal_status
     *
     * @return the value of label.label_cal_status
     *
     * @mbg.generated
     */
    public Byte getLabelCalStatus() {
        return labelCalStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.label_cal_status
     *
     * @param labelCalStatus the value for label.label_cal_status
     *
     * @mbg.generated
     */
    public void setLabelCalStatus(Byte labelCalStatus) {
        this.labelCalStatus = labelCalStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.last_cal_date
     *
     * @return the value of label.last_cal_date
     *
     * @mbg.generated
     */
    public Date getLastCalDate() {
        return lastCalDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.last_cal_date
     *
     * @param lastCalDate the value for label.last_cal_date
     *
     * @mbg.generated
     */
    public void setLastCalDate(Date lastCalDate) {
        this.lastCalDate = lastCalDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.del
     *
     * @return the value of label.del
     *
     * @mbg.generated
     */
    public Boolean getDel() {
        return del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.del
     *
     * @param del the value for label.del
     *
     * @mbg.generated
     */
    public void setDel(Boolean del) {
        this.del = del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.creator
     *
     * @return the value of label.creator
     *
     * @mbg.generated
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.creator
     *
     * @param creator the value for label.creator
     *
     * @mbg.generated
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.modifier
     *
     * @return the value of label.modifier
     *
     * @mbg.generated
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.modifier
     *
     * @param modifier the value for label.modifier
     *
     * @mbg.generated
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.create_time
     *
     * @return the value of label.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.create_time
     *
     * @param createTime the value for label.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.update_time
     *
     * @return the value of label.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.update_time
     *
     * @param updateTime the value for label.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.task
     *
     * @return the value of label.task
     *
     * @mbg.generated
     */
    public Long getTask() {
        return task;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.task
     *
     * @param task the value for label.task
     *
     * @mbg.generated
     */
    public void setTask(Long task) {
        this.task = task;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.tenant_id
     *
     * @return the value of label.tenant_id
     *
     * @mbg.generated
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.tenant_id
     *
     * @param tenantId the value for label.tenant_id
     *
     * @mbg.generated
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.recalculate
     *
     * @return the value of label.recalculate
     *
     * @mbg.generated
     */
    public Boolean getRecalculate() {
        return recalculate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.recalculate
     *
     * @param recalculate the value for label.recalculate
     *
     * @mbg.generated
     */
    public void setRecalculate(Boolean recalculate) {
        this.recalculate = recalculate;
    }

    /**
     * 获取是否为内置标签
     * @return 是否为内置标签
     */
    public Boolean getIsBuiltin() {
        return isBuiltin;
    }

    /**
     * 设置是否为内置标签
     * @param isBuiltin 是否为内置标签
     */
    public void setIsBuiltin(Boolean isBuiltin) {
        this.isBuiltin = isBuiltin;
    }

    @Override
    public Long getTaskInfoId() {
        return this.getTask();
    }

    @Override
    public Byte getCalStatus() {
        return this.getLabelCalStatus();
    }
}