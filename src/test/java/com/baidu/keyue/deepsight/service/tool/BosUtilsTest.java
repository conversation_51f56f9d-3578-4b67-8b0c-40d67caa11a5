package com.baidu.keyue.deepsight.service.tool;

import com.baidu.keyue.deepsight.config.BosConfig;
import com.baidu.keyue.deepsight.models.datamanage.dto.BosProperty;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidubce.services.bos.BosClient;
import com.baidubce.services.bos.model.BosObject;
import com.baidubce.services.bos.model.BosObjectSummary;
import com.baidubce.services.bos.model.ListObjectsResponse;
import com.baidubce.services.bos.model.PutObjectResponse;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.io.InputStream;
import java.net.URL;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
@RunWith(MockitoJUnitRunner.class)
public class BosUtilsTest {


    private final String validKey = "/dev/20250101/valid-key.mp3";

    private final String invalidKey = "invalid/../key";

    @Mock
    private URL url;

    private final String testEndpoint = "test-endpoint";

    @Mock
    private BosObject bosObject;

    @Mock
    private PutObjectResponse putObjectResponse;

    private final String testSecretKey = "test-secret-key";

    private final String testKey = "/test-key/123.mp3";

    private final String testAccessKey = "test-access-key";

    private final String testUrl = "http://test-url";

    @Mock
    private BosConfig bosConfig;

    @Mock
    private InputStream inputStream;

    private final String testObjectKey = "test-object-key";
    private final String testBucket = "test-bucket";

    private final String testPrefix = "test-prefix";

    @Mock
    private BosClient client;

    @InjectMocks
    private BosUtils bosUtils;

    @Test
    public void testListObjectKeysReturnsExpectedKeys() {
        // Setup mock response
        ListObjectsResponse mockResponse = mock(ListObjectsResponse.class);
        BosObjectSummary summary1 = mock(BosObjectSummary.class);
        BosObjectSummary summary2 = mock(BosObjectSummary.class);

        when(summary1.getKey()).thenReturn("key1");
        when(summary2.getKey()).thenReturn("key2");
        when(mockResponse.getContents()).thenReturn(Arrays.asList(summary1, summary2));
        when(client.listObjects(testBucket, testPrefix)).thenReturn(mockResponse);

        // Execute
        List<String> result = bosUtils.listObjectKeys(testBucket, testPrefix);

        // Verify
        assertEquals(2, result.size());
        assertEquals("key1", result.get(0));
        assertEquals("key2", result.get(1));
        verify(client).listObjects(testBucket, testPrefix);
    }

    @Test
    public void testListObjectKeysReturnsEmptyListWhenNoObjects() {
        // Setup mock response
        ListObjectsResponse mockResponse = mock(ListObjectsResponse.class);
        when(mockResponse.getContents()).thenReturn(List.of());
        when(client.listObjects(testBucket, testPrefix)).thenReturn(mockResponse);

        // Execute
        List<String> result = bosUtils.listObjectKeys(testBucket, testPrefix);

        // Verify
        assertEquals(0, result.size());
        verify(client).listObjects(testBucket, testPrefix);
    }

    @Test
    public void testGeneratePreSignedUrlReturnsExpectedUrl() throws Exception {
        // Setup mock response
        URL mockUrl = new URL("https://test-url.com");
        when(client.generatePresignedUrl(testBucket, testObjectKey, 86400 * 7 - 1))
                .thenReturn(mockUrl);

        // Execute
        String result = bosUtils.generatePreSignedUrl(testBucket, testObjectKey);

        // Verify
        assertEquals("https://test-url.com", result);
        verify(client).generatePresignedUrl(testBucket, testObjectKey, 86400 * 7 - 1);
    }

    @Test(expected = Exception.class)
    public void testGeneratePreSignedUrlThrowsException() throws Exception {
        // Setup mock to throw exception
        when(client.generatePresignedUrl(testBucket, testObjectKey, 86400 * 7 - 1))
                .thenThrow(new RuntimeException("Test exception"));

        // Execute
        bosUtils.generatePreSignedUrl(testBucket, testObjectKey);
    }

    @Test
    public void testInit() {
        when(bosConfig.getAccessKeyId()).thenReturn(testAccessKey);
        when(bosConfig.getSecretAccessKey()).thenReturn(testSecretKey);
        when(bosConfig.getEndpoint()).thenReturn(testEndpoint);

        bosUtils.init();

        verify(bosConfig).getAccessKeyId();
        verify(bosConfig).getSecretAccessKey();
        verify(bosConfig).getEndpoint();
        assertNotNull(bosUtils.getClient());
        assertNotNull(bosUtils.getBosProperty());
        assertEquals(testAccessKey, bosUtils.getBosProperty().getAccessKey());
        assertEquals(testSecretKey, bosUtils.getBosProperty().getSecret());
        assertEquals(testEndpoint, bosUtils.getBosProperty().getEndPoint());
    }

    @Test
    public void testGetClient() {
        BosClient mockClient = mock(BosClient.class);

        BosClient result = bosUtils.getClient();

        assertEquals(client, result);
    }

    @Test
    public void testDeleteObjectWhenExists() {
        when(bosUtils.doesObjectExist(testBucket, testKey)).thenReturn(true);
        doNothing().when(client).deleteObject(testBucket, testKey);

        bosUtils.deleteObject(testBucket, testKey);

        verify(client).deleteObject(testBucket, testKey);
    }

    @Test
    public void testGetObjectWhenExists() {
        when(bosUtils.doesObjectExist(testBucket, testKey)).thenReturn(true);
        when(client.getObject(testBucket, testKey)).thenReturn(bosObject);

        BosObject result = bosUtils.getObject(testBucket, testKey);

        assertEquals(bosObject, result);
        verify(client).getObject(testBucket, testKey);
    }

    @Test
    public void testGetObjectWhenNotExists() {
        when(bosUtils.doesObjectExist(testBucket, testKey)).thenReturn(false);

        BosObject result = bosUtils.getObject(testBucket, testKey);

        assertNull(result);
        verify(client, never()).getObject(testBucket, testKey);
    }

    @Test
    public void testGeneratePreSignedUrl() {
        when(client.generatePresignedUrl(testBucket, testKey, 86400 * 7 - 1)).thenReturn(url);
        when(url.toString()).thenReturn(testUrl);

        String result = bosUtils.generatePreSignedUrl(testBucket, testKey);

        assertEquals(testUrl, result);
        verify(client).generatePresignedUrl(testBucket, testKey, 86400 * 7 - 1);
    }

    @Test
    public void testGetBosProperty() {
        when(bosConfig.getAccessKeyId()).thenReturn(testAccessKey);
        when(bosConfig.getSecretAccessKey()).thenReturn(testSecretKey);
        when(bosConfig.getEndpoint()).thenReturn(testEndpoint);

        bosUtils.init();

        BosProperty mockProperty = new BosProperty();
        mockProperty.setAccessKey(testAccessKey);
        mockProperty.setSecret(testSecretKey);
        mockProperty.setEndPoint(testEndpoint);
        BosProperty result = bosUtils.getBosProperty();

        assertEquals(mockProperty, result);
        assertEquals(testAccessKey, result.getAccessKey());
    }

    @Test
    public void testPutObject() {
        when(client.putObject(testBucket, testKey, inputStream)).thenReturn(putObjectResponse);

        PutObjectResponse result = bosUtils.putObject(testBucket, testKey, inputStream);

        assertEquals(putObjectResponse, result);
        verify(client).putObject(testBucket, testKey, inputStream);
    }

    @Test
    public void testDeleteObjectWhenNotExists() {
        when(bosUtils.doesObjectExist(testBucket, testKey)).thenReturn(false);

        bosUtils.deleteObject(testBucket, testKey);

        verify(client, never()).deleteObject(testBucket, testKey);
    }

    @Test
    public void testDoesObjectExist() {
        when(client.doesObjectExist(testBucket, testKey)).thenReturn(true);

        boolean result = bosUtils.doesObjectExist(testBucket, testKey);

        assertTrue(result);
        verify(client).doesObjectExist(testBucket, testKey);
    }

    @Test
    public void testListObjectKeysReturnsExpectedKeys1() {
        ListObjectsResponse mockResponse = mock(ListObjectsResponse.class);
        BosObjectSummary summary1 = mock(BosObjectSummary.class);
        BosObjectSummary summary2 = mock(BosObjectSummary.class);

        when(summary1.getKey()).thenReturn("key1");
        when(summary2.getKey()).thenReturn("key2");
        when(mockResponse.getContents()).thenReturn(Arrays.asList(summary1, summary2));
        when(client.listObjects(testBucket, testPrefix)).thenReturn(mockResponse);

        List<String> result = bosUtils.listObjectKeys(testBucket, testPrefix);

        assertEquals(2, result.size());
        assertEquals("key1", result.get(0));
        assertEquals("key2", result.get(1));
        verify(client).listObjects(testBucket, testPrefix);
    }



    @Test
    public void testIsSafeObjectKeyEdgeCases() {
        assertFalse(bosUtils.isSafeObjectKey("/"));
        assertTrue(bosUtils.isSafeObjectKey("/a"));
        assertTrue(bosUtils.isSafeObjectKey("/a.txt"));
        assertFalse(bosUtils.isSafeObjectKey("/a b.txt"));
    }

    @Test
    public void testIsSafeObjectKeyNullOrEmpty() {
        assertFalse(bosUtils.isSafeObjectKey(null));
        assertFalse(bosUtils.isSafeObjectKey(""));
        assertFalse(bosUtils.isSafeObjectKey("   "));
    }

    @Test
    public void testIsSafeObjectKeyNotAbsolutePath() {
        assertFalse(bosUtils.isSafeObjectKey("relative/path"));
        assertFalse(bosUtils.isSafeObjectKey("file.txt"));
    }

    @Test
    public void testIsSafeObjectKeyContainsPathTraversal() {
        assertFalse(bosUtils.isSafeObjectKey("/path/../to/file"));
        assertFalse(bosUtils.isSafeObjectKey("/../file.txt"));
    }

    @Test
    public void testIsSafeObjectKeyInvalidAfterNormalization() {
        assertFalse(bosUtils.isSafeObjectKey("/path/./to/../file"));
        assertFalse(bosUtils.isSafeObjectKey("/path//to/file"));
    }

    @Test
    public void testIsSafeObjectKeyHiddenFile() {
        assertFalse(bosUtils.isSafeObjectKey("/path/to/.hidden"));
        assertFalse(bosUtils.isSafeObjectKey("/.config"));
    }

    @Test
    public void testIsSafeObjectKeyInvalidFileNameChars() {
        assertFalse(bosUtils.isSafeObjectKey("/path/to/file*"));
        assertFalse(bosUtils.isSafeObjectKey("/path/to/file?"));
        assertFalse(bosUtils.isSafeObjectKey("/path/to/file#"));
    }

    @Test
    public void testIsSafeObjectKeyValidFiles() {
        assertTrue(bosUtils.isSafeObjectKey("/valid-file.txt"));
        assertTrue(bosUtils.isSafeObjectKey("/path/to/normal_file-123.ext"));
        assertTrue(bosUtils.isSafeObjectKey("/singlefile"));
    }

    @Test
    public void testDoesObjectExistWhenObjectExists() {
        when(client.doesObjectExist(testBucket, validKey)).thenReturn(true);

        boolean result = bosUtils.doesObjectExist(testBucket, validKey);

        assertTrue(result);
        verify(client).doesObjectExist(testBucket, validKey);
    }

    @Test
    public void testDoesObjectExistWhenObjectNotExists() {
        when(client.doesObjectExist(testBucket, validKey)).thenReturn(false);

        boolean result = bosUtils.doesObjectExist(testBucket, validKey);

        assertFalse(result);
        verify(client).doesObjectExist(testBucket, validKey);
    }

    @Test(expected = DeepSightException.ParamsErrorException.class)
    public void testDoesObjectExistWhenKeyIsInvalid() {
        bosUtils.doesObjectExist(testBucket, invalidKey);
    }
}