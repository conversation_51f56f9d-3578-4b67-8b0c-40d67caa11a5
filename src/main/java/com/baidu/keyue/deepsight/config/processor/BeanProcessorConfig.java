package com.baidu.keyue.deepsight.config.processor;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

/**
 * Description: 控制Bean的注入
 * <br>
 *
 * <AUTHOR>
 */
@Configuration
public class BeanProcessorConfig {

    @Bean
    public CustomBeanExclusionProcessor customBeanExclusionProcessor(Environment environment) {
        return new CustomBeanExclusionProcessor(environment);
    }
}
