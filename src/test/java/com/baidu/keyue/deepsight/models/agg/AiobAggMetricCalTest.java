package com.baidu.keyue.deepsight.models.agg;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.List;

/**
 * AiobAggMetricCal 测试类
 */
public class AiobAggMetricCalTest {

    @Test
    public void testWeeklyHourlyDataCollection() {
        AiobAggMetricCal metricCal = new AiobAggMetricCal();
        
        // 测试数据：周一9-10点，总呼叫10次，总接通8次，小秘书接通6次
        metricCal.appendWeeklyHourlyData("2025-08-18", "9-10", 10L, 8L, 6L); // 周一

        // 测试数据：周二14-15点，总呼叫5次，总接通3次，小秘书接通2次
        metricCal.appendWeeklyHourlyData("2025-08-19", "14-15", 5L, 3L, 2L); // 周二

        // 测试数据：周日23-24点，总呼叫2次，总接通1次，小秘书接通1次
        metricCal.appendWeeklyHourlyData("2025-08-17", "23-24", 2L, 1L, 1L); // 周日
        
        // 执行统计
        metricCal.statistics();
        
        // 验证结果
        List<List<Long>> autoAnswerCalls = metricCal.getWeeklyHourlyAutoAnswerCalls();
        List<List<Float>> lostCallsRate = metricCal.getWeeklyHourlyLostCallsRate();
        
        assertNotNull(autoAnswerCalls);
        assertNotNull(lostCallsRate);
        assertEquals(7, autoAnswerCalls.size()); // 7天
        assertEquals(7, lostCallsRate.size()); // 7天
        
        // 验证每天都有24小时的数据
        for (int day = 0; day < 7; day++) {
            assertEquals(24, autoAnswerCalls.get(day).size());
            assertEquals(24, lostCallsRate.get(day).size());
        }
        
        // 验证具体数据
        // 周日(0) 23点的小秘书接通数应该是1
        assertEquals(1L, autoAnswerCalls.get(0).get(23));
        // 周日(0) 23点的未接通率应该是0.5 (1/2)
        assertEquals(0.5f, lostCallsRate.get(0).get(23), 0.001f);

        // 周一(1) 9点的小秘书接通数应该是6
        assertEquals(6L, autoAnswerCalls.get(1).get(9));
        // 周一(1) 9点的未接通率应该是0.2 (2/10)
        assertEquals(0.2f, lostCallsRate.get(1).get(9), 0.001f);

        // 周二(2) 14点的小秘书接通数应该是2
        assertEquals(2L, autoAnswerCalls.get(2).get(14));
        // 周二(2) 14点的未接通率应该是0.4 (2/5)
        assertEquals(0.4f, lostCallsRate.get(2).get(14), 0.001f);
        
        // 验证其他时间段都是0
        assertEquals(0L, autoAnswerCalls.get(0).get(0)); // 周日0点
        assertEquals(0.0f, lostCallsRate.get(0).get(0), 0.001f); // 周日0点
    }
    
    @Test
    public void testInvalidData() {
        AiobAggMetricCal metricCal = new AiobAggMetricCal();
        
        // 测试无效日期
        metricCal.appendWeeklyHourlyData("invalid-date", "9-10", 10L, 8L, 6L);

        // 测试无效时间桶
        metricCal.appendWeeklyHourlyData("2025-08-18", "invalid-time", 10L, 8L, 6L);

        // 测试null值
        metricCal.appendWeeklyHourlyData(null, "9-10", 10L, 8L, 6L);
        metricCal.appendWeeklyHourlyData("2025-08-18", null, 10L, 8L, 6L);
        
        // 执行统计
        metricCal.statistics();
        
        // 验证结果 - 应该都是0
        List<List<Long>> autoAnswerCalls = metricCal.getWeeklyHourlyAutoAnswerCalls();
        List<List<Float>> lostCallsRate = metricCal.getWeeklyHourlyLostCallsRate();
        
        for (int day = 0; day < 7; day++) {
            for (int hour = 0; hour < 24; hour++) {
                assertEquals(0L, autoAnswerCalls.get(day).get(hour));
                assertEquals(0.0f, lostCallsRate.get(day).get(hour), 0.001f);
            }
        }
    }
    
    @Test
    public void testAccumulation() {
        AiobAggMetricCal metricCal = new AiobAggMetricCal();
        
        // 同一时间段多次添加数据
        metricCal.appendWeeklyHourlyData("2025-08-18", "9-10", 5L, 3L, 2L); // 周一9点
        metricCal.appendWeeklyHourlyData("2025-08-18", "9-10", 3L, 2L, 1L); // 周一9点
        
        metricCal.statistics();
        
        List<List<Long>> autoAnswerCalls = metricCal.getWeeklyHourlyAutoAnswerCalls();
        List<List<Float>> lostCallsRate = metricCal.getWeeklyHourlyLostCallsRate();
        
        // 验证累加结果：总呼叫8次，总接通5次，小秘书接通3次
        assertEquals(3L, autoAnswerCalls.get(1).get(9)); // 小秘书接通数：2+1=3
        assertEquals(0.38f, lostCallsRate.get(1).get(9), 0.001f); // 未接通率：3/8=0.375，四舍五入到0.38
    }

    @Test
    public void testLostCallsRatePrecision() {
        AiobAggMetricCal metricCal = new AiobAggMetricCal();

        // 测试各种精度情况
        metricCal.appendWeeklyHourlyData("2025-08-18", "9-10", 3L, 1L, 1L);   // 未接通率：2/3 = 0.6666... -> 0.67
        metricCal.appendWeeklyHourlyData("2025-08-18", "10-11", 7L, 5L, 3L);  // 未接通率：2/7 = 0.2857... -> 0.29
        metricCal.appendWeeklyHourlyData("2025-08-18", "11-12", 9L, 6L, 4L);  // 未接通率：3/9 = 0.3333... -> 0.33

        metricCal.statistics();

        List<List<Long>> autoAnswerCalls = metricCal.getWeeklyHourlyAutoAnswerCalls();
        List<List<Float>> lostCallsRate = metricCal.getWeeklyHourlyLostCallsRate();

        // 验证精度
        assertEquals(1L, autoAnswerCalls.get(1).get(9));   // 小秘书接通数
        assertEquals(3L, autoAnswerCalls.get(1).get(10));  // 小秘书接通数
        assertEquals(4L, autoAnswerCalls.get(1).get(11));  // 小秘书接通数
        assertEquals(0.67f, lostCallsRate.get(1).get(9), 0.001f);   // 2/3 四舍五入到0.67
        assertEquals(0.29f, lostCallsRate.get(1).get(10), 0.001f);  // 2/7 四舍五入到0.29
        assertEquals(0.33f, lostCallsRate.get(1).get(11), 0.001f);  // 3/9 四舍五入到0.33
    }
}
