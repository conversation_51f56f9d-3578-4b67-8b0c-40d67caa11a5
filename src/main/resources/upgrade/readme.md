

# 2025-08-07

1. @since v1.2.2 开始承接洞察平台
2. upgrade 包的结构说明：
   - 1）doris 包
     - init 包：存放各个版本对应的全量初始化SQL，用于0-1部署或创建新租户
       - init/v1.1.3_init_with_tenantId.sql     与租户强相关的数据库表，创建新租户的时候需要对表名拼接tenantId
       - init/v1.1.3_init_without_tenantId.sql  与租户无关的数据库表，0-1部署时仅执行一次
        
     - v1.1.3.sql 租户级别的增量 SQL，每个租户进行更新
     - v1.1.3_global.sql  全局SQL

   - 2）mysql 包
     - init 包：存放各个版本对应的全量初始化SQL，用于0-1部署
       - v1.1.3_init.sql  初始化SQL，用于0-1部署
   
     - v1.2.2.sql  各个版本的增量SQL，用于升级（之前版本没有记录到代码库）


3. 原始升级方案，更新 doris 数据库有严重的性能问题，1000+ 租户，执行 v1.1.3.sql 时，耗时 1h 左右！需要技术优化

