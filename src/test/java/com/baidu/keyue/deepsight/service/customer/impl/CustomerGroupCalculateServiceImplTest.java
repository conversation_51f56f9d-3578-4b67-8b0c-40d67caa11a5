package com.baidu.keyue.deepsight.service.customer.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.enums.GroupingTypeEnum;
import com.baidu.keyue.deepsight.models.customer.request.GetCustomerGroupRequest;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup;
import com.baidu.keyue.deepsight.service.customer.CustomerGroupService;
import com.baidu.keyue.deepsight.service.tasks.TaskInfoService;
import com.baidu.keyue.deepsight.web.WebContextHolder;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class CustomerGroupCalculateServiceImplTest{


    @Mock
    private TaskInfoService taskInfoService;

    private static final String USER_ID = "9527";

    private static final Long CUSTOMER_GROUP_ID = 123L;

    @Mock
    private CustomerGroupService customerGroupService;

    @InjectMocks
    private CustomerGroupCalculateServiceImpl service;

    private GetCustomerGroupRequest request;

    private CustomerGroup customerGroup;

    private static final String TENANT_ID = "1";

    @BeforeEach
    void setUp() {
        request = new GetCustomerGroupRequest();
        request.setCustomerGroupId(CUSTOMER_GROUP_ID);

        customerGroup = new CustomerGroup();
        customerGroup.setTenantId(TENANT_ID);
    }

    @Test
    void execByManualShouldThrowExceptionWhenGroupingTypeIsFileImport() {
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TENANT_ID);
            mocked.when(WebContextHolder::getUserId).thenReturn(USER_ID);

            customerGroup.setGroupingType(GroupingTypeEnum.FILE_IMPORT.getCode());
            when(customerGroupService.getCustomerGroupByTenantIdAndCustomerGroupId(TENANT_ID, CUSTOMER_GROUP_ID))
                    .thenReturn(customerGroup);

            DeepSightException.ParamsErrorException exception = assertThrows(
                    DeepSightException.ParamsErrorException.class,
                    () -> service.execByManual(request)
            );

            assertEquals(ErrorCode.BAD_REQUEST, exception.getErrorCode());
            assertEquals("导入人群不能再次更新", exception.getMessage());
            verify(customerGroupService).getCustomerGroupByTenantIdAndCustomerGroupId(TENANT_ID, CUSTOMER_GROUP_ID);
        }
    }

    @Test
    void execByManualShouldSuccessWhenGroupingTypeIsNotFileImport() {
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            CustomerGroupCalculateServiceImpl calculateService = spy(CustomerGroupCalculateServiceImpl.class);
            calculateService.setTaskInfoService(taskInfoService);
            ReflectionTestUtils.setField(calculateService, "taskInfoService", taskInfoService);
            ReflectionTestUtils.setField(calculateService, "customerGroupService", customerGroupService);
            mocked.when(WebContextHolder::getTenantId).thenReturn(TENANT_ID);
            mocked.when(WebContextHolder::getUserId).thenReturn(USER_ID);
            customerGroup.setGroupingType(GroupingTypeEnum.RULE_CIRCLE.getCode());
            when(customerGroupService.getCustomerGroupByTenantIdAndCustomerGroupId(TENANT_ID, CUSTOMER_GROUP_ID))
                    .thenReturn(customerGroup);
            doNothing().when(calculateService).execByManual(customerGroup, USER_ID);
            calculateService.execByManual(request);
        }
    }
}