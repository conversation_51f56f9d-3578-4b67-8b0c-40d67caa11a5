package com.baidu.keyue.deepsight.enums;

import com.baidu.keyue.deepsight.constants.BuiltinLabelConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 内置标签目录枚举
 * 定义内置标签所需的目录结构
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
@Getter
@AllArgsConstructor
public enum BuiltinLabelCatalogEnum {

    // 系统默认目录（租户初始化时创建）
    BASIC_LABEL("基础标签", null, BuiltinLabelConstants.CatalogHierarchy.PRIMARY_LEVEL, "系统基础标签目录"),
    PREFERENCE_LABEL("偏好标签", null, BuiltinLabelConstants.CatalogHierarchy.PRIMARY_LEVEL, "用户偏好标签目录"),

    // 内置标签专用目录 - 一级目录
    OUTBOUND_BEHAVIOR("外呼行为", null, BuiltinLabelConstants.CatalogHierarchy.PRIMARY_LEVEL, "外呼相关行为分析"),
    RISK_PERCEPTION("风险感知", null, BuiltinLabelConstants.CatalogHierarchy.PRIMARY_LEVEL, "风险识别和评估"),

    // 内置标签专用目录 - 二级目录（外呼行为下的子目录）
    SALES_PREFERENCE("电销偏好", OUTBOUND_BEHAVIOR, BuiltinLabelConstants.CatalogHierarchy.SECONDARY_LEVEL, "电话销售偏好分析"),
    INTENTION_TAG("意向标签", OUTBOUND_BEHAVIOR, BuiltinLabelConstants.CatalogHierarchy.SECONDARY_LEVEL, "客户意向标签"),
    CONVERSATION_MINING("对话挖掘", OUTBOUND_BEHAVIOR, BuiltinLabelConstants.CatalogHierarchy.SECONDARY_LEVEL, "对话内容挖掘分析"),

    // 内置标签专用目录 - 二级目录（风险感知下的子目录）
    COMPLAINT_RISK("投诉风险", RISK_PERCEPTION, BuiltinLabelConstants.CatalogHierarchy.SECONDARY_LEVEL, "客户投诉风险评估");

    /**
     * 目录名称
     */
    private final String catalogName;
    
    /**
     * 父目录枚举，null表示一级目录
     */
    private final BuiltinLabelCatalogEnum parent;
    
    /**
     * 目录层级：1-一级目录，2-二级目录
     */
    private final int level;
    
    /**
     * 目录描述
     */
    private final String description;

    /**
     * 获取内置标签专用的一级目录
     * @return 内置标签一级目录列表
     */
    public static List<BuiltinLabelCatalogEnum> getBuiltinPrimaryCatalogs() {
        return Arrays.asList(OUTBOUND_BEHAVIOR, RISK_PERCEPTION);
    }

    /**
     * 获取所有二级目录
     * @return 二级目录列表
     */
    public static List<BuiltinLabelCatalogEnum> getSecondaryCatalogs() {
        return Arrays.stream(values())
                .filter(catalog -> catalog.level == BuiltinLabelConstants.CatalogHierarchy.SECONDARY_LEVEL)
                .collect(Collectors.toList());
    }

    /**
     * 根据目录名称查找枚举
     * @param catalogName 目录名称
     * @return 对应的枚举，如果不存在返回null
     */
    public static BuiltinLabelCatalogEnum findByCatalogName(String catalogName) {
        return Arrays.stream(values())
                .filter(catalog -> catalog.catalogName.equals(catalogName))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取完整的目录路径
     * @return 目录路径，如 "外呼行为/电销偏好"
     */
    public String getFullPath() {
        if (parent == null) {
            return catalogName;
        }
        return parent.getCatalogName() + "/" + catalogName;
    }

    /**
     * 判断是否为一级目录
     * @return true表示一级目录
     */
    public boolean isPrimary() {
        return level == BuiltinLabelConstants.CatalogHierarchy.PRIMARY_LEVEL;
    }

    /**
     * 判断是否为系统默认目录
     * @return true表示系统默认目录
     */
    public boolean isDefaultCatalog() {
        return this == BASIC_LABEL || this == PREFERENCE_LABEL;
    }

    /**
     * 判断是否为内置标签专用目录
     * @return true表示内置标签专用目录
     */
    public boolean isBuiltinCatalog() {
        return !isDefaultCatalog();
    }

    /**
     * 根据目录名称获取对应的枚举（包括默认目录）
     * @param catalogName 目录名称
     * @return 对应的枚举，如果不存在返回null
     */
    public static BuiltinLabelCatalogEnum getByName(String catalogName) {
        return findByCatalogName(catalogName);
    }

    /**
     * 获取所有目录名称列表
     * @return 目录名称列表
     */
    public static List<String> getAllCatalogNames() {
        return Arrays.stream(values())
                .map(BuiltinLabelCatalogEnum::getCatalogName)
                .collect(Collectors.toList());
    }
}
