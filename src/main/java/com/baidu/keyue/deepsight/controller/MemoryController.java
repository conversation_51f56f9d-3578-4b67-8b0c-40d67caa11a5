package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.memory.DeleteUserMemoryDataRequest;
import com.baidu.keyue.deepsight.models.memory.MemoryDataSourceDetail;
import com.baidu.keyue.deepsight.models.memory.MemoryDataSourceIDRequest;
import com.baidu.keyue.deepsight.models.memory.MemoryDataSourceSwitchUpdateRequest;
import com.baidu.keyue.deepsight.models.memory.NewMemoryDataSourceRequest;
import com.baidu.keyue.deepsight.models.memory.UpdateMemoryDataSourceRequest;
import com.baidu.keyue.deepsight.service.memory.MemoryCalculateService;
import com.baidu.keyue.deepsight.service.memory.MemoryService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 长期记忆管理API
 * @className: MemoryController
 * @description: 长期记忆管理API
 * @author: lvtao03
 * @date: 2025/02/10 14:24
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/deepsight/v1/memory")
public class MemoryController {

    @Autowired
    private final MemoryService memoryService;

    @Autowired
    private MemoryCalculateService memoryCalculateService;

    /**
     * 添加记忆提取数据集
     *
     * @param request 添加数据集请求
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/newDataSource", method = RequestMethod.POST)
    public BaseResponse<Void> create(@RequestBody @Valid NewMemoryDataSourceRequest request) {
        memoryService.createMemoryDataSource(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 列表查询记忆提取数据集
     *
     * @param request 分页查询请求
     * @return BasePageResponse 分页结果
     */
    @RequestMapping(value = "/listDataSource", method = RequestMethod.POST)
    public BasePageResponse<MemoryDataSourceDetail> list(@RequestBody BasePageRequest request) {
        return BasePageResponse.of(memoryService.listDataSource(request));
    }

    /**
     * 更新记忆提取数据集启用状态
     *
     * @param request 更新请求
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/updateSwitch", method = RequestMethod.POST)
    public BaseResponse<Void> updateSwitch(@RequestBody MemoryDataSourceSwitchUpdateRequest request) {
        memoryService.updateSwitch(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 删除记忆提取数据集
     * @param request 记忆提取数据集 ID
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/deleteDataSource", method = RequestMethod.POST)
    public BaseResponse<Void> delete(@RequestBody MemoryDataSourceIDRequest request) {
        memoryService.deleteMemoryDataSource(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 编辑记忆提取数据集
     * @param request 记忆提取数据集更新请求
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/updateDataSource", method = RequestMethod.POST)
    public BaseResponse<Void> update(@RequestBody @Valid UpdateMemoryDataSourceRequest request) {
        memoryService.updateMemoryConfig(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 删除用户记忆提取结果
     * @param request 用户 id/mobile
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/deleteMemoryResult", method = RequestMethod.POST)
    public BaseResponse<Void> deleteMemoryResult(@RequestBody DeleteUserMemoryDataRequest request) {
        memoryService.deleteMemoryResult(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }
}
