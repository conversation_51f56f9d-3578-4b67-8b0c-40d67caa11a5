package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.idmapping.request.datatable.CreateIdMappingDataTableRequest;
import com.baidu.keyue.deepsight.models.idmapping.request.datatable.DeleteIdMappingDataTableRequest;
import com.baidu.keyue.deepsight.models.idmapping.response.datatable.ListIdMappingDataTableResponse;
import com.baidu.keyue.deepsight.service.idmapping.IdMappingDateTableService;
import com.baidu.keyue.deepsight.service.idmapping.IdMappingManagerService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * id mapping 数据表配置API
 *
 * @className: IdMappingDataTableController
 * @description: id mapping 数据表配置API
 * @author: chenwenyu03
 * @date: 2025/3/11 10:50
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/deepsight/v1/idMappingDataTable")
public class IdMappingDataTableController {
    @Autowired
    private IdMappingDateTableService idMappingDateTableService;

    @Autowired
    private IdMappingManagerService idMappingManagerService;

    /**
     * 创建 id mapping 数据表规则
     *
     * @param request 创建 id mapping 数据表规则 请求
     */
    @PostMapping("/create")
    public BaseResponse<Void> createIdMappingDataTable(@Valid @RequestBody CreateIdMappingDataTableRequest request) {
        Boolean switchStatus = idMappingManagerService.getSwitchStatus();
        if (Objects.nonNull(switchStatus) && switchStatus) {
            log.error("创建待刷新数据集失败，ID-Mapping启动中");
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "ID-Mapping启动中，待刷新数据集不能创建");
        }
        idMappingDateTableService.createIdMappingDataTable(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 删除 id mapping 数据表规则
     *
     */
    @PostMapping("/delete")
    public BaseResponse<Void> deleteIdMappingDataTable(@Valid @RequestBody DeleteIdMappingDataTableRequest request) {
        Boolean switchStatus = idMappingManagerService.getSwitchStatus();
        if (Objects.nonNull(switchStatus) && switchStatus) {
            log.error("删除待刷新数据集失败，ID-Mapping启动中");
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "ID-Mapping启动中，待刷新数据集不能删除");
        }
        idMappingDateTableService.deleteIdMappingDataTable(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * id mapping 数据表规则分页查询
     * @return id mapping 数据表规则查询结果
     */
    @PostMapping("/list")
    public BasePageResponse<ListIdMappingDataTableResponse> listIdMappingRule(@Valid @RequestBody BasePageRequest request) {
        return BasePageResponse.of(idMappingDateTableService.listIdMappingDataTable(request));
    }

    /**
     * id mapping 数据表规则可选数据表查询
     * @return id mapping 数据表规则可选数据表
     */
    @PostMapping("/dataset/list")
    public BaseResponse<List<ListIdMappingDataTableResponse>> listDataTable() {
        return BaseResponse.of(idMappingDateTableService.listDataTable());
    }
}
