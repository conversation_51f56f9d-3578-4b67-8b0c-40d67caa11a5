package com.baidu.keyue.deepsight.models.sop;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * SOP离线分析任务响应
 */
@Data
public class SopTaskResponse {

    /**
     * 主键
     */
    private Long id;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 机器人ID
     */
    private String robotId;

    /**
     * 机器人场景：5-快捷场景，6-灵活画布
     */
    private Byte robotScene;

    /**
     * 机器人场景描述
     */
    private String robotSceneDesc;

    /**
     * 机器人版本列表
     */
    private List<String> robotVersion;

    /**
     * 是否自动应答：0-否，1-是
     */
    private Byte isAutoAnswer;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 标签列表
     */
    private List<String> tags;

    /**
     * 任务状态：1-执行中，2-计算失败，3-成功
     */
    private Byte status;

    /**
     * 任务状态描述
     */
    private String statusDesc;

    /**
     * 任务执行信息描述
     */
    private String taskMsg;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 机器人名称
     */
    private String robotName;

    /**
     * SOP用户配置信息
     */
    private SopUserConfigUpdateRequest userConfig;
}