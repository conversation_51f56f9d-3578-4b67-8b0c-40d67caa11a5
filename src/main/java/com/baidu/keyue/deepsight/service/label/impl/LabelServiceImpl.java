package com.baidu.keyue.deepsight.service.label.impl;

import cn.hutool.core.lang.Assert;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.BuiltinLabelEnum;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.ExecModEnum;
import com.baidu.keyue.deepsight.enums.LabelValueSaveModEnum;
import com.baidu.keyue.deepsight.enums.SortOrderEnum;
import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.enums.UpdateModEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseRecordResponse;
import com.baidu.keyue.deepsight.models.catalog.ListCatalogRequest;
import com.baidu.keyue.deepsight.models.catalog.ListCatalogResponse;
import com.baidu.keyue.deepsight.models.datamanage.request.FieldShowConfigQueryRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.FieldShowConfigResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.label.GetLabelDetailRequest;
import com.baidu.keyue.deepsight.models.label.GetLabelDistributeRequest;
import com.baidu.keyue.deepsight.models.label.LabelDetail;
import com.baidu.keyue.deepsight.models.label.LabelDistribute;
import com.baidu.keyue.deepsight.models.label.LabelDistributeDetail;
import com.baidu.keyue.deepsight.models.label.LabelOriginalDetail;
import com.baidu.keyue.deepsight.models.label.LabelRule;
import com.baidu.keyue.deepsight.models.label.LabelUserListRequest;
import com.baidu.keyue.deepsight.models.label.LabelValueRule;
import com.baidu.keyue.deepsight.models.label.ListLabelBriefRequest;
import com.baidu.keyue.deepsight.models.label.NewLabelRequest;
import com.baidu.keyue.deepsight.models.label.TriggerFrequencyValue;
import com.baidu.keyue.deepsight.models.label.UpdateLabelRequest;
import com.baidu.keyue.deepsight.models.rules.dto.DqlParseResult;
import com.baidu.keyue.deepsight.models.rules.dto.RuleGroup;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.Label;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskScheduler;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendLabelMapper;
import com.baidu.keyue.deepsight.service.catalog.LabelCatalogService;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.datamanage.impl.DorisConfServiceImpl;
import com.baidu.keyue.deepsight.service.label.LabelService;
import com.baidu.keyue.deepsight.service.rules.RuleManagerService;
import com.baidu.keyue.deepsight.service.rules.RuleParseService;
import com.baidu.keyue.deepsight.service.tasks.TaskInfoService;
import com.baidu.keyue.deepsight.service.tasks.TaskSchedulerService;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LabelServiceImpl implements LabelService {

    @Autowired
    private RedissonClient redisson;
    @Autowired
    private ExtendLabelMapper labelMapper;

    @Autowired
    private LabelFieldServiceImpl labelFieldService;
    @Autowired
    private TaskSchedulerService taskSchedulerService;
    @Autowired
    private LabelCatalogService labelCatalogService;
    @Autowired
    private TaskInfoService taskInfoService;
    @Autowired
    private RuleParseService ruleParseService;

    @Resource
    private DorisService dorisService;
    @Resource
    private DataTableManageService dataTableManageService;
    @Resource
    private RuleManagerService ruleManagerService;
    
    @Resource
    private DorisConfServiceImpl dorisConfService;

    private String labelNameLock(String... keys) {
        return StringUtils.join(keys, "-");
    }

    @Transactional
    public void createLabel(NewLabelRequest request) {
        String userId = WebContextHolder.getUserId();
        String tenantId = WebContextHolder.getTenantId();
        for (LabelValueRule labelValueRule : request.getLabelRule().getLabelValueRules()) {
            ruleParseService.checkRuleGroupTenantPermission(labelValueRule.getRuleGroup(), tenantId);
        }
        if (!BuiltinLabelEnum.UN_RULEABLE_NAMES.contains(request.getLabelName())) {
            request.getLabelRule().checkLabelRule(ruleParseService);
        }
        // 标签值租户全局唯一
        String key = labelNameLock("createLabel", tenantId, request.getLabelName());
        RLock lock = redisson.getLock(key);
        if (!lock.tryLock()) {
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST);
        }
        try {
            // 校验目录是否存在
            labelCatalogService.getCatalogDetail(request.getCatalogId(), tenantId);
            // 检查标签名称是否重复
            if (isDupLabelName(tenantId, request.getCatalogId(), request.getLabelName())) {
                throw new DeepSightException.LabelOperatorFailedException(ErrorCode.BAD_REQUEST, "标签已存在");
            }
            // insert label
            LabelWithBLOBs l = new LabelWithBLOBs();
            l.setUserId(userId);
            l.setTenantId(tenantId);
            l.setCatalogId(request.getCatalogId());
            l.setLabelName(request.getLabelName());
            l.setLabelValueUpdateMod(request.getUpdateMod().getCode());
            l.setLabelValueSaveMod(request.getLabelValueSaveMod().getCode());
            l.setTriggerMod(request.getTriggerMod().getCode());
            if (Objects.nonNull(request.getTriggerFrequency())) {
                l.setTriggerFrequency(request.getTriggerFrequency().getCode());
            }
            if (Objects.nonNull(request.getTriggerFrequencyValue())) {
                l.setTriggerFrequencyValue(JsonUtils.toJson(request.getTriggerFrequencyValue()));
            }
            l.setLabelRule(JsonUtils.toJson(request.getLabelRule()));
            l.setExecMod(ExecModEnum.RULE.getCode());
            l.setLabelCalStatus(TaskExecStatusEnum.PENDING.getCode());
            l.setDistribution(null);
            l.setRecalculate(false);
            // 标签计算任务
            Long taskId = taskInfoService.createNewLabelCalTask(
                    request.getTriggerMod(),
                    request.getTriggerFrequency(),
                    request.getTriggerFrequencyValue());
            l.setTask(taskId);
            // 标签字段
            long fieldId = labelFieldService.createNewLabelField(tenantId, request.getLabelName());
            l.setField(fieldId);
            l.setDel(DelEnum.NOT_DELETED.getBoolean());
            l.setCreator(WebContextHolder.getUserAuthInfo().getUserName());
            l.setModifier(WebContextHolder.getUserAuthInfo().getUserName());
            Date now = new Date();
            l.setCreateTime(now);
            l.setUpdateTime(now);
            labelMapper.insert(l);
        } catch (DeepSightException.LabelOperatorFailedException |
                 DeepSightException.CatalogOperatorFailedException e) {
            log.error("LabelService.newLabel error : ", e);
            throw new DeepSightException.LabelOperatorFailedException(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            log.error("LabelService.newLabel INTERNAL_ERROR : ", e);
            throw new DeepSightException.LabelOperatorFailedException(ErrorCode.INTERNAL_ERROR, "标签创建失败");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public BasePageResponse.Page<LabelDetail> listLabel(ListLabelBriefRequest request) {
        String tenantId = WebContextHolder.getTenantId();

        List<Long> catalogIds = labelCatalogService.retrieveCatalogIds(request.getCatalogId());
        // 标签列表查询
        int offset = (request.getPageNo() - 1) * request.getPageSize();
        Integer limit = request.getPageSize();
        LabelCriteria labelCriteria = new LabelCriteria();
        LabelCriteria.Criteria criteria = labelCriteria.createCriteria();
        criteria.andTenantIdEqualTo(tenantId).andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        if (StringUtils.isNotBlank(request.getLabelName())) {
            criteria.andLabelNameLike("%" + request.getLabelName() + "%");
        }
        if (CollectionUtils.isNotEmpty(catalogIds)) {
            criteria.andCatalogIdIn(catalogIds);
        }
        long count = labelMapper.countByExample(labelCriteria);
        if (count == 0) {
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), 0L, Lists.newArrayList());
        }
        if (offset > count) {
            throw new DeepSightException.LabelOperatorFailedException(ErrorCode.BAD_REQUEST, "分页大小超过限制");
        }
        labelCriteria.setOrderByClause(String.format("id desc limit %d offset %d", limit, offset));
        // 分页查询
        List<Label> labelPage = labelMapper.selectByExample(labelCriteria);
        // 查询 task 对应的最新执行记录
        List<Long> taskIds = labelPage.stream().map(Label::getTask).distinct().toList();
        Map<Long, TaskSchedulerWithBLOBs> taskSchedulerRecordMap = taskSchedulerService.queryTaskScheduler(taskIds);
        List<LabelDetail> labelBriefs = labelPage.stream()
                .map(label -> LabelDetail.convertFrom(label, taskSchedulerRecordMap)).toList();
        return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), count, labelBriefs);
    }

    @Override
    public Label getLabelByTenantAndLabelId(Long labelId) {
        String tenantId = WebContextHolder.getTenantId();
        return getLabelByTenantAndLabelId(tenantId, labelId);
    }

    @Override
    public LabelWithBLOBs getLabelByTenantAndLabelId(String tenantId, Long labelId) {
        LabelCriteria labelCriteria = new LabelCriteria();
        LabelCriteria.Criteria criteria = labelCriteria.createCriteria();
        criteria.andIdEqualTo(labelId).andTenantIdEqualTo(tenantId).andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<LabelWithBLOBs> label = labelMapper.selectByExampleWithBLOBs(labelCriteria);
        if (CollectionUtils.isEmpty(label)) {
            throw new DeepSightException.LabelOperatorFailedException(ErrorCode.NOT_FOUND, "标签不存在");
        }
        return label.get(0);
    }

    @Override
    @Transactional
    public void deleteLabel(Label l) {
        LabelWithBLOBs label = new LabelWithBLOBs();
        label.setId(l.getId());
        label.setDel(true);
        labelMapper.updateByPrimaryKeySelective(label);
        labelFieldService.deleteFieldById(l.getField());
        taskInfoService.deleteFieldById(l.getTask());
    }

    @Override
    public LabelOriginalDetail getLabelDetail(GetLabelDetailRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        LabelWithBLOBs label = getLabelByTenantAndLabelId(tenantId, request.getLabelId());
        LabelOriginalDetail detail = new LabelOriginalDetail();
        detail.setLabelId(label.getId());
        detail.setLabelName(label.getLabelName());
        detail.setCatalogId(label.getCatalogId());
        detail.setExecMod(ExecModEnum.getByCode(label.getExecMod()));
        detail.setTriggerMod(TriggerModeEnum.getByCode(label.getTriggerMod()));
        detail.setCreator(label.getCreator());
        detail.setCreateTime(DatetimeUtils.formatDate(label.getCreateTime()));
        detail.setStatus(TaskExecStatusEnum.getByCode(label.getLabelCalStatus()));
        detail.setUpdateMod(UpdateModEnum.getByCode(label.getLabelValueUpdateMod()));
        detail.setLabelValueSaveMod(LabelValueSaveModEnum.getByCode(label.getLabelValueSaveMod()));
        detail.setTriggerFrequency(TriggerFrequencyEnum.getByCode(label.getTriggerFrequency()));
        if (StringUtils.isNotBlank(label.getTriggerFrequencyValue())) {
            detail.setTriggerFrequencyValue(JsonUtils.toObjectWithoutException(label.getTriggerFrequencyValue(), TriggerFrequencyValue.class));
        }
        if (StringUtils.isNotBlank(label.getLabelRule())) {
            detail.setLabelRule(JsonUtils.toObjectWithoutException(label.getLabelRule(), LabelRule.class));
        }
        // 查询最近一条执行成功的记录
        TaskScheduler execRecords = taskSchedulerService.queryLatestFinishedRecord(label.getTask());
        if (Objects.nonNull(execRecords)) {
            detail.setLastTaskDate(DatetimeUtils.formatDate(execRecords.getUpdateTime()));
        }
        return detail;
    }

    @Override
    public LabelDistribute labelDistribution(GetLabelDistributeRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        LabelWithBLOBs label = getLabelByTenantAndLabelId(tenantId, request.getLabelId());
        LabelDistribute labelDistribute;
        if (StringUtils.isNotBlank(label.getDistribution())) {
            labelDistribute = JsonUtils.toObjectWithoutException(label.getDistribution(), LabelDistribute.class);
        } else {
            labelDistribute = LabelDistribute.buildEmpty(label);
        }
        if (Objects.nonNull(labelDistribute)
                && CollectionUtils.isNotEmpty(labelDistribute.getLabels())
                && Objects.nonNull(request.getSort())) {
            if (SortOrderEnum.DESC.equals(request.getSort())) {
                labelDistribute.setLabels(
                        labelDistribute.getLabels().stream()
                                .sorted(Comparator.comparingLong(LabelDistributeDetail::getCount).reversed()).toList());
            } else {
                labelDistribute.setLabels(
                        labelDistribute.getLabels().stream()
                                .sorted(Comparator.comparingLong(LabelDistributeDetail::getCount)).toList());
            }
        }

        return labelDistribute;
    }

    public void updateLabel(UpdateLabelRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        for (LabelValueRule labelValueRule : request.getLabelRule().getLabelValueRules()) {
            ruleParseService.checkRuleGroupTenantPermission(labelValueRule.getRuleGroup(), tenantId);
        }
        request.getLabelRule().checkLabelRule(ruleParseService);
        LabelWithBLOBs label = getLabelByTenantAndLabelId(tenantId, request.getLabelId());
        if (label.getLabelCalStatus().equals(TaskExecStatusEnum.RUNNING.getCode())) {
            throw new DeepSightException.LabelOperatorFailedException(ErrorCode.BAD_REQUEST, "标签正在计算中，不能修改");
        }
        // 标签值租户全局唯一
        String key = labelNameLock("updateLabelDistribution", tenantId, request.getLabelName());
        RLock lock = redisson.getLock(key);
        if (!lock.tryLock()) {
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST);
        }
        try {
            if (!label.getLabelName().equals(request.getLabelName()) || !label.getCatalogId().equals(request.getCatalogId())) {
                if (isDupLabelName(tenantId, request.getCatalogId(), request.getLabelName())) {
                    throw new DeepSightException.LabelOperatorFailedException(ErrorCode.BAD_REQUEST, "标签已存在");
                }
                label.setLabelName(request.getLabelName());
            }
            label.setLabelCalStatus(TaskExecStatusEnum.PENDING.getCode());
            label.setCatalogId(request.getCatalogId());
            label.setLabelName(request.getLabelName());
            label.setLabelValueUpdateMod(request.getUpdateMod().getCode());
            label.setLabelValueSaveMod(request.getLabelValueSaveMod().getCode());
            label.setTriggerMod(request.getTriggerMod().getCode());
            if (Objects.nonNull(request.getTriggerFrequency())) {
                label.setTriggerFrequency(request.getTriggerFrequency().getCode());
            } else {
                label.setTriggerFrequency(null);
            }
            if (Objects.nonNull(request.getTriggerFrequencyValue())) {
                label.setTriggerFrequencyValue(JsonUtils.toJson(request.getTriggerFrequencyValue()));
            } else {
                label.setTriggerFrequencyValue(null);
            }
            label.setLabelRule(JsonUtils.toJson(request.getLabelRule()));
            if (Objects.nonNull(request.getRecalculate())) {
                label.setRecalculate(request.getRecalculate());
            }
            labelMapper.labelUpdate(
                    request.getLabelId(),
                    request.getCatalogId(),
                    request.getLabelName(),
                    label.getLabelCalStatus(),
                    label.getLabelValueUpdateMod(),
                    label.getLabelValueSaveMod(),
                    label.getTriggerMod(),
                    label.getTriggerFrequency(),
                    label.getTriggerFrequencyValue(),
                    label.getLabelRule(),
                    WebContextHolder.getUserAuthInfo().getUserName(),
                    label.getRecalculate(),
                    new Date()
            );
            // task更新
            taskInfoService.updateLabelCalTaskTrigger(
                    label.getTask(), request.getTriggerMod(), request.getTriggerFrequency(), request.getTriggerFrequencyValue()
            );
        } catch (DeepSightException.LabelOperatorFailedException e) {
            log.error("LabelService.newLabel error : ", e);
            throw new DeepSightException.LabelOperatorFailedException(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            log.error("LabelService.newLabel INTERNAL_ERROR : ", e);
            throw new DeepSightException.LabelOperatorFailedException(ErrorCode.INTERNAL_ERROR, "标签更新失败");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private List<LabelWithBLOBs> catalogLabelComplete(List<Long> catalogIds) {
        String tenantId = WebContextHolder.getTenantId();
        if (CollectionUtils.isEmpty(catalogIds)) {
            return null;
        }
        // query label by catalogId
        LabelCriteria labelCriteria = new LabelCriteria();
        LabelCriteria.Criteria criteria = labelCriteria.createCriteria();
        criteria.andTenantIdEqualTo(tenantId).andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        criteria.andCatalogIdIn(catalogIds);
        return labelMapper.selectByExampleWithBLOBs(labelCriteria);
    }

    @Override
    public ListCatalogResponse listLabelTree(ListCatalogRequest request, Boolean onlyCount) {
        ListCatalogResponse catalogResult = labelCatalogService.list(request);
        List<Long> catalogIds = catalogResult.flattenCatalogIds();
        List<LabelWithBLOBs> labels = catalogLabelComplete(catalogIds);
        Map<Long, List<LabelWithBLOBs>> labelMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(labels)) {
            labelMap = labels.stream()
                    .filter(label -> {
                        if (StringUtils.isNotBlank(request.getLabelName())) {
                            return label.getLabelName().contains(request.getLabelName());
                        }
                        return true;
                    })
                    .collect(Collectors.groupingBy(LabelWithBLOBs::getCatalogId));
        }
        if (onlyCount) {
            catalogResult.fillLabelAndCount(labelMap);
        } else {
            catalogResult.fillLabelWithFilter(labelMap);
        }
        return catalogResult;
    }

    @Override
    public List<LabelWithBLOBs> getWaitExecLabel(Set<Long> taskIds) {
        // retrieve labelIds
        LabelCriteria labelCriteria = new LabelCriteria();
        LabelCriteria.Criteria criteria = labelCriteria.createCriteria();
        criteria.andLabelCalStatusNotEqualTo(TaskExecStatusEnum.RUNNING.getCode());
        criteria.andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        criteria.andTaskIn(Lists.newArrayList(taskIds));
        return labelMapper.selectByExampleWithBLOBs(labelCriteria);
    }

    @Override
    public List<LabelWithBLOBs> queryRunningLabel() {
        LabelCriteria labelCriteria = new LabelCriteria();
        LabelCriteria.Criteria criteria = labelCriteria.createCriteria();
        criteria.andLabelCalStatusEqualTo(TaskExecStatusEnum.RUNNING.getCode());
        criteria.andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        return labelMapper.selectByExampleWithBLOBs(labelCriteria);
    }

    @Override
    public List<Label> queryDeletedLabel(Integer seconds) {
        Date d = new Date(System.currentTimeMillis() - (seconds * 1000));
        LabelCriteria labelCriteria = new LabelCriteria();
        LabelCriteria.Criteria criteria = labelCriteria.createCriteria();
        criteria.andDelEqualTo(DelEnum.DELETED.getBoolean())
                .andUpdateTimeGreaterThan(d);
        return labelMapper.selectByExample(labelCriteria);
    }

    /**
     * 校验标签名在该目录下是否重复 true 重复 false 不重复
     */
    private boolean isDupLabelName(String tenantId, long catalogId, String labelName) {
        // 校验标签名是否重复
        LabelCriteria criteria = new LabelCriteria();
        criteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andCatalogIdEqualTo(catalogId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean())
                .andLabelNameEqualTo(labelName);
        return labelMapper.countByExample(criteria) > 0;
    }

    @Override
    public List<Label> retrieveLabelWithFieldIds(List<Long> labelFields, String tenantId) {
        if (CollectionUtils.isEmpty(labelFields)) {
            return Collections.emptyList();
        }
        LabelCriteria labelCriteria = new LabelCriteria();
        LabelCriteria.Criteria criteria = labelCriteria.createCriteria();
        criteria.andDelEqualTo(DelEnum.NOT_DELETED.getBoolean())
                .andTenantIdEqualTo(tenantId)
                .andFieldIn(labelFields);
        return labelMapper.selectByExample(labelCriteria);
    }

    @Override
    public BaseRecordResponse labelUserList(LabelUserListRequest request, String tenantId) {
        // 检查标签，获取标签对应的字段
        LabelWithBLOBs label = getLabelByTenantAndLabelId(tenantId, request.getLabelId());
        Assert.notNull(label, () -> new DeepSightException.BusinessException(ErrorCode.BAD_REQUEST,"标签不存在"));
        request.setLabelFieldId(label.getField());
        // 获取doris表名，查询获取内容
        String mockUserTableName = TenantUtils.generateMockUserTableName(tenantId);
        DataTableInfo tableInfo = dataTableManageService.getTableDetailWithTableName(mockUserTableName);
        Assert.notNull(tableInfo, () -> new DeepSightException.BusinessException(ErrorCode.BAD_REQUEST,"数据集不存在"));
        Long tableId = tableInfo.getId();
        // 获取可见字段
        List<VisibleFieldResponse> visibleFieldResponse = dataTableManageService.getVisibleFields(tableId, false);
        if (CollectionUtils.isEmpty(visibleFieldResponse)) {
            throw new DeepSightException.ParamsErrorException(
                    ErrorCode.NOT_FOUND, "数据表不存在可见字段");
        }
        FieldShowConfigResponse fieldShowConfig = dataTableManageService.getFieldShowConfig(tenantId, new FieldShowConfigQueryRequest(tableId));
        String labelFieldName = labelFieldService.genFieldName(request.getLabelId());
        RuleGroup ruleGroup = request.buildRuleGroup(tableId, labelFieldName);
        // 解析规则节点，生成doris查询语句，设置查询字段
        DqlParseResult dqlParseResult = ruleManagerService.parseRuleGroup(ruleGroup, new AtomicInteger(0));
        long count = dorisService.getCount(dqlParseResult.parseCountSql());
        if (count <= 0) {
            BasePageResponse.Page<Map<String, String>> page = BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), count, List.of());
            return new BaseRecordResponse(visibleFieldResponse, page, fieldShowConfig);
        }
        List<String> visibleFields = visibleFieldResponse.stream()
                .map(field -> String.format("`%s`", field.getEnName())).toList();
        String primaryKey = dataTableManageService.queryTableFieldByTag(tableId, TableFieldTagEnum.PRIMARY).getEnField();
        dqlParseResult.setSelect(String.join(Constants.SEPARATOR, visibleFields));
        dqlParseResult.setOrderBy(String.format("`%s`", primaryKey));
        dqlParseResult.setOffset(request.getOffset());
        dqlParseResult.setSize(request.getPageSize());
        List<Map<String, Object>> tableContentList = dorisService.selectList(dqlParseResult.parseDorisSql());
        // datetime格式转换，加密字段***输出等
        List<Map<String, String>> results = tableContentList.stream().map(dorisData ->
                dorisConfService.dorisDataConvertToShowData(tableId, dorisData, visibleFieldResponse)).toList();
        BasePageResponse.Page<Map<String, String>> page = BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), count, results);
        return new BaseRecordResponse(visibleFieldResponse, page, fieldShowConfig);
    }
}
