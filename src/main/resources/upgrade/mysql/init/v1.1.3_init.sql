/*
 Navicat Premium Dump SQL

 Source Server         : 洞察dev-mysql-************
 Source Server Type    : MySQL
 Source Server Version : 50744 (5.7.44-*******-log)
 Source Host           : ************:8037
 Source Schema         : deep_sight_sandbox

 Target Server Type    : MySQL
 Target Server Version : 50744 (5.7.44-*******-log)
 File Encoding         : 65001

 Date: 07/08/2025 11:04:59
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for DATABASECHANGELOG
-- ----------------------------
DROP TABLE IF EXISTS `DATABASECHANGELOG`;
CREATE TABLE `DATABASECHANGELOG` (
  `ID` varchar(255) NOT NULL,
  `AUTHOR` varchar(255) NOT NULL,
  `FILENAME` varchar(255) NOT NULL,
  `DATEEXECUTED` datetime NOT NULL,
  `ORDEREXECUTED` int(11) NOT NULL,
  `EXECTYPE` varchar(10) NOT NULL,
  `MD5SUM` varchar(35) DEFAULT NULL,
  `DESCRIPTION` varchar(255) DEFAULT NULL,
  `COMMENTS` varchar(255) DEFAULT NULL,
  `TAG` varchar(255) DEFAULT NULL,
  `LIQUIBASE` varchar(20) DEFAULT NULL,
  `CONTEXTS` varchar(255) DEFAULT NULL,
  `LABELS` varchar(255) DEFAULT NULL,
  `DEPLOYMENT_ID` varchar(10) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for DATABASECHANGELOGLOCK
-- ----------------------------
DROP TABLE IF EXISTS `DATABASECHANGELOGLOCK`;
CREATE TABLE `DATABASECHANGELOGLOCK` (
  `ID` int(11) NOT NULL,
  `LOCKED` bit(1) NOT NULL,
  `LOCKGRANTED` datetime DEFAULT NULL,
  `LOCKEDBY` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for aiob_robot_version
-- ----------------------------
DROP TABLE IF EXISTS `aiob_robot_version`;
CREATE TABLE `aiob_robot_version` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'primary key',
  `tenant_id` varchar(128) NOT NULL COMMENT '租户 ID',
  `robot_id` varchar(128) NOT NULL COMMENT '机器人ID',
  `robot_version` varchar(128) NOT NULL COMMENT '机器人版本',
  `robot_version_name` varchar(128) NOT NULL COMMENT '机器人版本名称',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_robot_id` (`robot_id`),
  KEY `idx_robot_version` (`robot_version`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB AUTO_INCREMENT=335 DEFAULT CHARSET=utf8mb4 COMMENT='外呼机器人版本信息';

-- ----------------------------
-- Table structure for aiob_sop_meta
-- ----------------------------
DROP TABLE IF EXISTS `aiob_sop_meta`;
CREATE TABLE `aiob_sop_meta` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'primary key',
  `tenant_id` varchar(128) NOT NULL COMMENT '租户 ID',
  `task_id` varchar(128) NOT NULL COMMENT '外呼任务ID',
  `version` varchar(128) DEFAULT '' COMMENT '版本',
  `step_id` varchar(128) NOT NULL COMMENT '步骤 id',
  `step_name` varchar(128) NOT NULL COMMENT '步骤名称',
  `node_id` varchar(128) NOT NULL COMMENT '节点 id',
  `node_name` varchar(128) NOT NULL COMMENT '节点名称',
  `manual_check` tinyint(1) NOT NULL COMMENT '人工确认标识,0:未确认,1:已确认',
  `task_rule` text NOT NULL COMMENT '对话规则',
  `del` tinyint(1) NOT NULL COMMENT '删除标识,0:未删除,1:已删除',
  `creator` varchar(128) NOT NULL COMMENT '创建者',
  `modifier` varchar(128) NOT NULL COMMENT '修改者',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4802 DEFAULT CHARSET=utf8mb4 COMMENT='SOP规则节点';

-- ----------------------------
-- Table structure for alert_config
-- ----------------------------
DROP TABLE IF EXISTS `alert_config`;
CREATE TABLE `alert_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_type` varchar(20) NOT NULL COMMENT '配置类型(task/caller/robot)',
  `config_target` varchar(50) NOT NULL COMMENT '配置目标(task_id/caller_num/robot_id)',
  `threshold_rate` decimal(5,2) NOT NULL COMMENT '阈值接通率(%)',
  `dial_count` int(11) NOT NULL COMMENT '拨打次数',
  `alert_freq` int(11) NOT NULL COMMENT '告警频率：1小时、12小时、24小时、72小时',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` varchar(255) NOT NULL COMMENT '租户ID',
  `alert_time` varchar(20) NOT NULL COMMENT '告警时间类型：24H(近24小时)/7D(近7天)/30D(近30天)',
  `next_check_time` datetime NOT NULL COMMENT '下次检查告警时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type_target` (`config_type`,`config_target`,`tenant_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COMMENT='告警配置表';

-- ----------------------------
-- Table structure for customer_diffusion_task
-- ----------------------------
DROP TABLE IF EXISTS `customer_diffusion_task`;
CREATE TABLE `customer_diffusion_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '预测扩散id',
  `task_name` varchar(128) NOT NULL COMMENT '预测任务名称',
  `seed_group` bigint(20) NOT NULL COMMENT '种子人群id',
  `predict_group` varchar(255) NOT NULL COMMENT '预测人群id，逗号分隔',
  `filter_rule` tinyint(4) NOT NULL COMMENT '过滤规则:0不过滤 1剔除种子人群',
  `feature_select` tinyint(4) NOT NULL COMMENT '特征筛选:0系统推荐 1覆盖率自定义',
  `threshold` float NOT NULL COMMENT '覆盖率阈值',
  `judge_criteria` tinyint(4) NOT NULL COMMENT '判定标准:0根据相似度 1取前几个',
  `similarity` float DEFAULT '0' COMMENT '判定标准：相似度',
  `ranking` int(11) DEFAULT '0' COMMENT '判定标准：取前几个',
  `trigger_mod` tinyint(4) NOT NULL COMMENT '更新触发类型:0:定时触发,1:手动触发',
  `trigger_frequency` tinyint(4) DEFAULT NULL COMMENT '执行频率:0:每天,1:每周,2:每月',
  `trigger_frequency_value` varchar(256) DEFAULT NULL COMMENT '执行频率json',
  `cal_status` tinyint(4) DEFAULT '0' COMMENT '计算状态: 0:待预测,1:预测中,2:预测成功,3:预测失败,4:计算取消',
  `last_cal_date` timestamp NULL DEFAULT NULL COMMENT '上一次执行时间',
  `task_id` bigint(20) DEFAULT NULL COMMENT '任务ID',
  `tenant_id` varchar(128) NOT NULL COMMENT '租户id',
  `group_package` tinyint(1) DEFAULT '0' COMMENT '打包到客群标识,0:未进行,1:已进行',
  `del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识,0:未删除,1:已删除',
  `creator` varchar(128) NOT NULL COMMENT '创建者',
  `modifier` varchar(128) NOT NULL COMMENT '修改者',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `customer_group_id` bigint(20) DEFAULT '0' COMMENT '人群扩散客群 id',
  `creator_name` varchar(128) DEFAULT NULL COMMENT '创建人姓名',
  `modifier_name` varchar(128) DEFAULT NULL COMMENT '更新人姓名',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=81 DEFAULT CHARSET=utf8mb4 COMMENT='人群扩散任务表';

-- ----------------------------
-- Table structure for customer_group
-- ----------------------------
DROP TABLE IF EXISTS `customer_group`;
CREATE TABLE `customer_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '客群id',
  `user_id` varchar(128) NOT NULL COMMENT '用户 ID',
  `customer_group_name` varchar(128) NOT NULL COMMENT '客群名称',
  `customer_group_description` varchar(500) DEFAULT NULL COMMENT '客群描述',
  `customer_group_value_update_mod` tinyint(4) NOT NULL COMMENT '更新取值逻辑:0:每次重新计算,1:合并历史值',
  `trigger_mod` tinyint(4) NOT NULL COMMENT '更新触发类型:0:定时触发,1:手动触发',
  `trigger_frequency` tinyint(4) DEFAULT NULL COMMENT '执行频率:0:每天,1:每周,2:每月',
  `trigger_frequency_value` varchar(256) DEFAULT NULL COMMENT '执行频率json',
  `customer_group_rule` mediumtext NOT NULL COMMENT '客群值规则json',
  `cal_status` tinyint(4) DEFAULT '0' COMMENT '计算状态: 0:待计算,1:计算中,2:计算成功,3:计算失败,4:计算取消',
  `del` tinyint(1) NOT NULL COMMENT '删除标识,0:未删除,1:已删除',
  `creator` varchar(128) NOT NULL COMMENT '创建者',
  `modifier` varchar(128) NOT NULL COMMENT '修改者',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_cal_date` timestamp NULL DEFAULT NULL COMMENT '上一次执行时间',
  `task` bigint(20) DEFAULT NULL COMMENT '任务ID',
  `tenant_id` varchar(128) DEFAULT NULL COMMENT '租户id',
  `preset` tinyint(1) DEFAULT '0' COMMENT '0: 非预置 1：预置',
  `grouping_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '分群方式：0:规则圈选 1:文件导入 2:模型预测',
  `config_tag` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否可配置标记：0：禁用 1：启用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=458 DEFAULT CHARSET=utf8mb4 COMMENT='客群表';

-- ----------------------------
-- Table structure for data_prediction_config
-- ----------------------------
DROP TABLE IF EXISTS `data_prediction_config`;
CREATE TABLE `data_prediction_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(128) NOT NULL COMMENT '租户 ID',
  `prediction_type` int(11) NOT NULL COMMENT '预测内容类型',
  `status` tinyint(1) NOT NULL COMMENT '状态, 0:启用, 1:关闭',
  `description` varchar(255) DEFAULT '' COMMENT '描述',
  `del` tinyint(1) NOT NULL COMMENT '删除标识, 0:未删除, 1:已删除',
  `creator` varchar(128) NOT NULL COMMENT '创建者',
  `modifier` varchar(128) NOT NULL COMMENT '修改者',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2047 DEFAULT CHARSET=utf8mb4 COMMENT='数据增强内容配置表';

-- ----------------------------
-- Table structure for data_prediction_source
-- ----------------------------
DROP TABLE IF EXISTS `data_prediction_source`;
CREATE TABLE `data_prediction_source` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(128) NOT NULL COMMENT '租户 ID',
  `data_source_list` text COMMENT '数据增强数据集 json',
  `prompt_type` tinyint(4) DEFAULT NULL COMMENT '指令类型:0:系统预置,1:自定义',
  `prompt` text NOT NULL COMMENT '抽取指令',
  `prediction_update_type` tinyint(1) NOT NULL COMMENT '数据预测更新方式, 0:预测到结果后定时更新, 1:预测到结果停止预测更新',
  `trigger_mod` tinyint(4) NOT NULL COMMENT '标签更新触发类型:0:定时触发,1:手动触发,2:实时触发',
  `trigger_frequency` tinyint(4) DEFAULT NULL COMMENT '执行频率:0:每天,1:每周,2:每月',
  `trigger_frequency_value` varchar(255) DEFAULT '' COMMENT '执行频率json',
  `description` varchar(255) DEFAULT '' COMMENT '描述',
  `task` bigint(20) DEFAULT NULL COMMENT '任务ID',
  `cal_status` tinyint(4) DEFAULT '0' COMMENT '标签计算状态: 0:待计算,1:计算中,2:计算成功,3:计算失败,4:计算取消',
  `del` tinyint(1) NOT NULL COMMENT '删除标识, 0:未删除, 1:已删除',
  `creator` varchar(128) NOT NULL COMMENT '创建者',
  `modifier` varchar(128) NOT NULL COMMENT '修改者',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=184 DEFAULT CHARSET=utf8mb4 COMMENT='数据增强数据源配置表';

-- ----------------------------
-- Table structure for datatable_access_token
-- ----------------------------
DROP TABLE IF EXISTS `datatable_access_token`;
CREATE TABLE `datatable_access_token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `table_name` varchar(255) NOT NULL COMMENT '数据表名称',
  `access_key` varchar(255) NOT NULL COMMENT '访问密钥',
  `secret_key` varchar(255) NOT NULL COMMENT '密钥',
  `access_key_desc` varchar(255) DEFAULT NULL COMMENT '密钥描述',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1489 DEFAULT CHARSET=utf8mb4 COMMENT='访问管理表';

-- ----------------------------
-- Table structure for datatable_info
-- ----------------------------
DROP TABLE IF EXISTS `datatable_info`;
CREATE TABLE `datatable_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `cn_name` varchar(255) NOT NULL COMMENT '数据表名称',
  `en_name` varchar(255) NOT NULL COMMENT '数据表英文名称',
  `data_type` int(10) NOT NULL COMMENT '数据类型',
  `description` text,
  `status` tinyint(4) NOT NULL COMMENT '状态',
  `is_visable` tinyint(1) NOT NULL COMMENT '是否可见 0：可见 1：不可见',
  `is_preset` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: 预置 1：新建',
  `is_del` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: 正常 1：已删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `table_name` varchar(255) NOT NULL,
  `tenantId` varchar(128) NOT NULL COMMENT '租户id',
  `db_type` varchar(255) NOT NULL DEFAULT 'doris',
  PRIMARY KEY (`id`),
  KEY `idx_table_name` (`en_name`),
  KEY `idx_update_time` (`update_time`)
) ENGINE=InnoDB AUTO_INCREMENT=3174 DEFAULT CHARSET=utf8mb4 COMMENT='数据表管理表';

-- ----------------------------
-- Table structure for datatable_meta_info
-- ----------------------------
DROP TABLE IF EXISTS `datatable_meta_info`;
CREATE TABLE `datatable_meta_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `table_en_name` varchar(255) NOT NULL COMMENT '数据表名称',
  `data_table_id` bigint(20) NOT NULL COMMENT '数据表唯一id',
  `en_field` varchar(255) NOT NULL COMMENT '数据字段英文名称',
  `cn_field` varchar(255) NOT NULL COMMENT '数据字段中文名称',
  `field_type` varchar(50) NOT NULL COMMENT '数据类型',
  `description` varchar(255) DEFAULT NULL COMMENT '数据描述',
  `is_filter_criteria` tinyint(1) NOT NULL DEFAULT '0' COMMENT '作为筛选条件',
  `is_required` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否必填 0非必填 1必填',
  `is_secrete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否加密',
  `from_baidu` tinyint(1) DEFAULT '0' COMMENT '是否来自百度',
  `is_visable` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否可见 0：可见 1：不可见',
  `field_tag` int(10) NOT NULL DEFAULT '0' COMMENT '字段标记 0：无 1：主键 2：度量 3：敏感 4：分区',
  `config_infos` text COMMENT '配置信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `value_type` varchar(255) NOT NULL COMMENT '取值类型如string：enum/text',
  `data_type` varchar(64) NOT NULL COMMENT '数据类型',
  `number` int(11) DEFAULT NULL,
  `is_show_value` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_table_name` (`table_en_name`),
  KEY `idx_table_id` (`data_table_id`),
  KEY `idx_update_time` (`update_time`)
) ENGINE=InnoDB AUTO_INCREMENT=69727 DEFAULT CHARSET=utf8mb4 COMMENT='字段元数据信息表';

-- ----------------------------
-- Table structure for field_encry_config
-- ----------------------------
DROP TABLE IF EXISTS `field_encry_config`;
CREATE TABLE `field_encry_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `table_en_name` varchar(255) NOT NULL COMMENT '数据表名称（加密）',
  `en_field` varchar(255) NOT NULL COMMENT '数据字段英文名称（加密）',
  `secret_key` varchar(255) NOT NULL COMMENT '加密密钥',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `data_table_id` varchar(255) DEFAULT NULL COMMENT '数据表唯一id',
  PRIMARY KEY (`id`),
  KEY `idx_table_name` (`table_en_name`)
) ENGINE=InnoDB AUTO_INCREMENT=765 DEFAULT CHARSET=utf8mb4 COMMENT='加密信息表';

-- ----------------------------
-- Table structure for field_show_config
-- ----------------------------
DROP TABLE IF EXISTS `field_show_config`;
CREATE TABLE `field_show_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_table_id` bigint(20) NOT NULL COMMENT '数据集ID',
  `tenant_id` varchar(128) NOT NULL COMMENT '租户ID',
  `show_field` text NOT NULL COMMENT '展示列，逗号分隔',
  `creator` varchar(255) DEFAULT NULL COMMENT '创建人',
  `modifier` varchar(255) DEFAULT NULL COMMENT '更新人',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `field_show_config_unique` (`data_table_id`,`tenant_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8 COMMENT='数据集显示列字段配置表';

-- ----------------------------
-- Table structure for id_mapping_datatable
-- ----------------------------
DROP TABLE IF EXISTS `id_mapping_datatable`;
CREATE TABLE `id_mapping_datatable` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id对主键',
  `data_table_id` bigint(20) NOT NULL COMMENT '数据表唯一id',
  `del` tinyint(1) NOT NULL COMMENT '删除标识,0:未删除,1:已删除',
  `creator` varchar(128) NOT NULL COMMENT '创建者',
  `modifier` varchar(128) NOT NULL COMMENT '修改者',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` varchar(128) NOT NULL COMMENT '租户id',
  `preset` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0: 非预置 1：预置',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=523 DEFAULT CHARSET=utf8mb4 COMMENT='IDmapping 数据集刷新规则';

-- ----------------------------
-- Table structure for id_mapping_generator
-- ----------------------------
DROP TABLE IF EXISTS `id_mapping_generator`;
CREATE TABLE `id_mapping_generator` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id-mapping-生成表主键',
  `trigger_mod` tinyint(4) NOT NULL COMMENT '更新触发类型:0:定时触发,1:手动触发',
  `trigger_frequency` tinyint(4) DEFAULT NULL COMMENT '执行频率:0:每天,1:每周,2:每月',
  `trigger_frequency_value` varchar(256) DEFAULT NULL COMMENT '执行频率json',
  `cal_status` tinyint(4) DEFAULT '0' COMMENT '计算状态: 0:待计算,1:计算中,2:计算成功,3:计算失败,4:计算取消',
  `del` tinyint(1) NOT NULL COMMENT '删除标识,0:未删除,1:已删除',
  `creator` varchar(128) NOT NULL COMMENT '创建者',
  `modifier` varchar(128) NOT NULL COMMENT '修改者',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_cal_date` timestamp NULL DEFAULT NULL COMMENT '上一次执行时间',
  `task_id` bigint(20) DEFAULT NULL COMMENT '任务ID',
  `tenant_id` varchar(128) DEFAULT '36729388145693696',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10176 DEFAULT CHARSET=utf8mb4 COMMENT='id-mapping-generator';

-- ----------------------------
-- Table structure for id_mapping_relation
-- ----------------------------
DROP TABLE IF EXISTS `id_mapping_relation`;
CREATE TABLE `id_mapping_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id对主键',
  `preset` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0: 非预置 1：预置',
  `data_table_id` bigint(20) NOT NULL COMMENT '数据表唯一id',
  `en_fields` json DEFAULT NULL COMMENT 'IDmapping id对',
  `cn_fields` json DEFAULT NULL COMMENT 'IDmapping id对中文展示，en_field 不会修改可缓存中文名',
  `del` tinyint(1) NOT NULL COMMENT '删除标识,0:未删除,1:已删除',
  `creator` varchar(128) NOT NULL COMMENT '创建者',
  `modifier` varchar(128) NOT NULL COMMENT '修改者',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` varchar(128) NOT NULL COMMENT '租户id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=625 DEFAULT CHARSET=utf8mb4 COMMENT='IDmapping 关系表';

-- ----------------------------
-- Table structure for id_mapping_rule
-- ----------------------------
DROP TABLE IF EXISTS `id_mapping_rule`;
CREATE TABLE `id_mapping_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id对主键',
  `en_field` varchar(255) NOT NULL COMMENT '数据字段英文名称',
  `cn_field` varchar(255) NOT NULL COMMENT '数据字段中文名称',
  `description` varchar(255) DEFAULT NULL COMMENT '字段描述',
  `field_type` tinyint(2) DEFAULT '0' COMMENT '取值类型 0 多值，1单值',
  `preset` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0: 非预置 1：预置',
  `merge_policy` tinyint(2) DEFAULT '0' COMMENT '针对多值类型的合并策略，对单值无效 0 最新，1 最早',
  `priority` int(10) NOT NULL COMMENT '优先级',
  `tenant_id` varchar(128) NOT NULL COMMENT '租户id',
  `creator` varchar(128) NOT NULL COMMENT '创建者',
  `modifier` varchar(128) NOT NULL COMMENT '修改者',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1951 DEFAULT CHARSET=utf8mb4 COMMENT='IDmapping字段规则';

-- ----------------------------
-- Table structure for label
-- ----------------------------
DROP TABLE IF EXISTS `label`;
CREATE TABLE `label` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签id',
  `user_id` varchar(128) NOT NULL COMMENT '用户 ID',
  `catalog_id` bigint(20) NOT NULL COMMENT '标签目录id',
  `label_name` varchar(128) NOT NULL COMMENT '标签名称',
  `label_value_update_mod` tinyint(4) NOT NULL COMMENT '标签更新取值逻辑:0:每次重新计算,1:合并历史值',
  `label_value_save_mod` tinyint(4) NOT NULL COMMENT '标签值保存类型:0:单值,1:多值',
  `trigger_mod` tinyint(4) NOT NULL COMMENT '标签更新触发类型:0:定时触发,1:手动触发',
  `trigger_frequency` tinyint(4) DEFAULT NULL COMMENT '执行频率:0:每天,1:每周,2:每月',
  `trigger_frequency_value` varchar(256) DEFAULT NULL COMMENT '执行频率json',
  `label_rule` text NOT NULL COMMENT '标签值规则json',
  `exec_mod` tinyint(4) DEFAULT '0' COMMENT '生产方式:0:业务规则,1:SQL,2:业务模式',
  `field` bigint(20) DEFAULT NULL COMMENT '字段',
  `distribution` text COMMENT '标签分布统计结果json',
  `label_cal_status` tinyint(4) DEFAULT '0' COMMENT '标签计算状态: 0:待计算,1:计算中,2:计算成功,3:计算失败,4:计算取消',
  `last_cal_date` timestamp NULL DEFAULT NULL COMMENT '上一次执行时间',
  `del` tinyint(1) NOT NULL COMMENT '删除标识,0:未删除,1:已删除',
  `creator` varchar(128) NOT NULL COMMENT '创建者',
  `modifier` varchar(128) NOT NULL COMMENT '修改者',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `task` bigint(20) DEFAULT NULL COMMENT '任务ID',
  `tenant_id` varchar(128) NOT NULL COMMENT '租户 ID',
  `recalculate` tinyint(1) DEFAULT '0' COMMENT '是否需要覆盖更新',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1099 DEFAULT CHARSET=utf8mb4 COMMENT='标签表';

-- ----------------------------
-- Table structure for label_catalog
-- ----------------------------
DROP TABLE IF EXISTS `label_catalog`;
CREATE TABLE `label_catalog` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签目录id',
  `parent_id` bigint(20) NOT NULL COMMENT '父级标签目录id',
  `catalog_name` varchar(128) NOT NULL COMMENT '标签目录名称',
  `sort` bigint(20) NOT NULL COMMENT '排序值',
  `user_id` varchar(128) NOT NULL COMMENT '用户 ID',
  `del` tinyint(1) NOT NULL COMMENT '删除标识, 0:未删除, 1: 已删除',
  `creator` varchar(128) NOT NULL COMMENT '创建者',
  `modifier` varchar(128) NOT NULL COMMENT '修改者',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `label_count` int(11) NOT NULL DEFAULT '0' COMMENT '标签数量',
  `tenant_id` varchar(128) NOT NULL COMMENT '租户 ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=994 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for label_field
-- ----------------------------
DROP TABLE IF EXISTS `label_field`;
CREATE TABLE `label_field` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字段 ID',
  `field_type` varchar(32) NOT NULL COMMENT '字段类型, STRING/INT/ARRAY...等',
  `field_desc` varchar(256) DEFAULT NULL COMMENT '字段描述',
  `label_table` varchar(128) NOT NULL COMMENT '宽表名',
  `table_space` varchar(128) NOT NULL COMMENT '宽表空间',
  `del` tinyint(1) NOT NULL COMMENT '删除标识,0:未删除,1:已删除',
  `creator` varchar(128) NOT NULL COMMENT '创建者',
  `modifier` varchar(128) NOT NULL COMMENT '修改者',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1096 DEFAULT CHARSET=utf8mb4 COMMENT='标签字段表';

-- ----------------------------
-- Table structure for memory_extract
-- ----------------------------
DROP TABLE IF EXISTS `memory_extract`;
CREATE TABLE `memory_extract` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID/记忆ID',
  `tenant_id` varchar(128) NOT NULL COMMENT '租户 ID',
  `data_table_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '抽取数据集ID',
  `dataset_name` varchar(255) NOT NULL DEFAULT '' COMMENT '抽取数据集-中文名称',
  `field_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '抽取数据集字段ID',
  `field_name` varchar(255) NOT NULL DEFAULT '' COMMENT '抽取数据集字段-中文名称',
  `field_name_en` varchar(255) NOT NULL DEFAULT '' COMMENT '抽取数据集字段-英文名称',
  `prompt_type` tinyint(4) DEFAULT NULL COMMENT '指令类型:0:系统预置,1:自定义',
  `prompt` text NOT NULL COMMENT '抽取指令',
  `trigger_mod` tinyint(4) NOT NULL COMMENT '标签更新触发类型:0:定时触发,1:手动触发,2:实时触发',
  `trigger_frequency` tinyint(4) DEFAULT NULL COMMENT '执行频率:0:每天,1:每周,2:每月',
  `trigger_frequency_value` varchar(255) DEFAULT '' COMMENT '执行频率json',
  `description` varchar(500) DEFAULT '' COMMENT '描述',
  `data_filter_rule` text COMMENT '抽取数据范围规则json',
  `status` tinyint(1) NOT NULL COMMENT '状态, 0:启用, 1:关闭',
  `is_default` tinyint(1) NOT NULL COMMENT '是否为预置数据集, 0:不是, 1:是',
  `task` bigint(20) DEFAULT NULL COMMENT '任务ID',
  `cal_status` tinyint(4) DEFAULT '0' COMMENT '标签计算状态: 0:待计算,1:计算中,2:计算成功,3:计算失败,4:计算取消',
  `del` tinyint(1) NOT NULL COMMENT '删除标识, 0:未删除, 1:已删除',
  `creator` varchar(128) NOT NULL COMMENT '创建者',
  `modifier` varchar(128) NOT NULL COMMENT '修改者',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=608 DEFAULT CHARSET=utf8mb4 COMMENT='记忆抽取任务表';

-- ----------------------------
-- Table structure for sop_user_config
-- ----------------------------
DROP TABLE IF EXISTS `sop_user_config`;
CREATE TABLE `sop_user_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'primary key',
  `tenant_id` varchar(128) NOT NULL COMMENT '租户 ID',
  `task_id` varchar(128) NOT NULL COMMENT '外呼任务ID',
  `version` varchar(128) DEFAULT '' COMMENT '版本',
  `core_metric` tinyint(4) NOT NULL COMMENT '核心指标:0:到达人数,1:到达次数',
  `assist_metric` tinyint(4) NOT NULL COMMENT '辅助指标:0:到达人数,1:到达次数',
  `warning_threshold` int(11) NOT NULL COMMENT '挂断提醒阈值',
  `del` tinyint(1) NOT NULL COMMENT '删除标识,0:未删除,1:已删除',
  `creator` varchar(128) NOT NULL COMMENT '创建者',
  `modifier` varchar(128) NOT NULL COMMENT '修改者',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3967 DEFAULT CHARSET=utf8mb4 COMMENT='SOP分析设置';

-- ----------------------------
-- Table structure for task_file_import
-- ----------------------------
DROP TABLE IF EXISTS `task_file_import`;
CREATE TABLE `task_file_import` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `group_id` varchar(255) DEFAULT NULL COMMENT '任务分组标识',
  `source_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `bos_name` varchar(255) NOT NULL COMMENT '系统文件名key',
  `bos_err_name` varchar(255) DEFAULT NULL COMMENT '导入失败数据bos文件名key',
  `suffix_name` varchar(20) DEFAULT NULL COMMENT '后缀名',
  `write_type` varchar(100) DEFAULT NULL COMMENT '写入模式：COVER-覆盖式写入，INCREMENT-增量式写入',
  `mapping_type` varchar(100) DEFAULT NULL COMMENT '字段映射模式：EQUAL_NAME-同名映射（默认），AI_SEMANTEME-AI语义映射',
  `tenant_id` varchar(128) NOT NULL COMMENT '租户ID',
  `data_table_id` bigint(20) NOT NULL COMMENT '数据集ID',
  `status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '任务状态：0-已上传，1-导入中，2-导入完成，3-导入失败',
  `del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识,0:未删除,1:已删除',
  `creator` varchar(128) NOT NULL COMMENT '创建者',
  `modifier` varchar(128) NOT NULL COMMENT '修改者',
  `operator_name` varchar(100) DEFAULT NULL COMMENT '操作者姓名',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `message` varchar(255) DEFAULT NULL COMMENT '错误信息',
  `field_mapping` blob COMMENT '字段映射配置(byte数组压缩)',
  `first_row_data` blob COMMENT '首行数据，同组相同(byte数组压缩)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=335 DEFAULT CHARSET=utf8 COMMENT='文件导入数据任务表';

-- ----------------------------
-- Table structure for task_info
-- ----------------------------
DROP TABLE IF EXISTS `task_info`;
CREATE TABLE `task_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '任务类型, 0:贴源层集成任务, 1:中间层指标加工任务',
  `task_desc` varchar(256) DEFAULT NULL COMMENT '任务描述',
  `task_conf` text COMMENT '任务配置信息, JSON 格式',
  `trigger_cron` varchar(64) DEFAULT '' COMMENT '定时任务 cron 表达式',
  `next_exec_date` timestamp NULL DEFAULT NULL COMMENT '下一次执行时间',
  `del` tinyint(1) NOT NULL COMMENT '删除标识, 0:未删除, 1:已删除',
  `creator` varchar(128) NOT NULL COMMENT '创建者',
  `modifier` varchar(128) NOT NULL COMMENT '修改者',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `trigger_mod` tinyint(4) DEFAULT '0' COMMENT '触发类型:0:定时触发,1:手动触发,2:实时触发',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1863 DEFAULT CHARSET=utf8mb4 COMMENT='任务表';

-- ----------------------------
-- Table structure for task_scheduler
-- ----------------------------
DROP TABLE IF EXISTS `task_scheduler`;
CREATE TABLE `task_scheduler` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务执行ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID,全局唯一',
  `external_id` varchar(256) DEFAULT NULL COMMENT '外部执行ID',
  `body` text COMMENT '额外信息',
  `message` text COMMENT '结果信息',
  `status` tinyint(4) NOT NULL COMMENT '任务执行状态',
  `del` tinyint(1) NOT NULL COMMENT '删除标识,0:未删除,1:已删除',
  `creator` varchar(128) NOT NULL COMMENT '创建者',
  `modifier` varchar(128) NOT NULL COMMENT '修改者',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id_id` (`task_id`,`id`),
  KEY `idx_task_scheduler_status_task_id` (`status`,`task_id`,`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7537318 DEFAULT CHARSET=utf8mb4 COMMENT='任务调度执行表';

-- ----------------------------
-- Table structure for tenant_info
-- ----------------------------
DROP TABLE IF EXISTS `tenant_info`;
CREATE TABLE `tenant_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `tenantId` varchar(255) NOT NULL COMMENT '租户id',
  `accountId` varchar(255) DEFAULT NULL COMMENT '账户id',
  `userName` varchar(255) DEFAULT NULL COMMENT '用户名称',
  `tenant_source` varchar(255) NOT NULL COMMENT '租户来源:login/aiob',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `user_id` varchar(20) DEFAULT NULL,
  `version` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `tenantId` (`tenantId`),
  UNIQUE KEY `accountId` (`accountId`),
  KEY `idx_tenantId` (`tenantId`),
  KEY `idx_userName` (`userName`)
) ENGINE=InnoDB AUTO_INCREMENT=1648 DEFAULT CHARSET=utf8mb4 COMMENT='租户信息表';

SET FOREIGN_KEY_CHECKS = 1;
