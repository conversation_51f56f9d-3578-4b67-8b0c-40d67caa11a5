package com.baidu.keyue.deepsight.models.rules.dto;

import com.baidu.keyue.deepsight.enums.LogicEnum;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @className: RuleGroup
 * @description: 数据筛选规则组
 * @author: wangzhongcheng
 * @date: 2024/12/21 16:02
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RuleGroup {

    /**
     * 规则组名称
     */
    private String name;

    /**
     * 规则组逻辑关系，and、or
     */
    private LogicEnum relation;

    /**
     * 可以嵌套规则组，如果存在 ruleNodes 则为空，反之不为空
     */
    private List<RuleGroup> ruleGroups;

    /**
     * 最底层为规则节点，如果存在 ruleGroups 则为空，反之不为空
     */
    private List<RuleNode> ruleNodes;

    public static RuleGroup clone(RuleGroup ruleGroup) {
        String ruleGroupJsonStr = JsonUtils.toJsonUnchecked(ruleGroup);
        return JsonUtils.toObjectWithoutException(ruleGroupJsonStr, RuleGroup.class);
    }
}
