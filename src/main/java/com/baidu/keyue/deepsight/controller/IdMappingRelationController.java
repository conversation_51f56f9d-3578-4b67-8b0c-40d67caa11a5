package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.idmapping.request.relation.CreateIdMappingRelRequest;
import com.baidu.keyue.deepsight.models.idmapping.request.relation.DeleteIdMappingRelRequest;
import com.baidu.keyue.deepsight.models.idmapping.request.relation.ListIdMappingRelRequest;
import com.baidu.keyue.deepsight.models.idmapping.request.relation.UpdateIdMappingRelRequest;
import com.baidu.keyue.deepsight.models.idmapping.request.relation.VerifyPublicIdRequest;
import com.baidu.keyue.deepsight.models.idmapping.response.relation.IdMappingDatasetFieldListResponse;
import com.baidu.keyue.deepsight.models.idmapping.response.relation.IdMappingDatasetListResponse;
import com.baidu.keyue.deepsight.models.idmapping.response.relation.IdMappingRelItemResponse;
import com.baidu.keyue.deepsight.models.idmapping.response.relation.VerifyPublicIdResponse;
import com.baidu.keyue.deepsight.service.idmapping.IdMappingManagerService;
import com.baidu.keyue.deepsight.service.idmapping.IdMappingRelService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * ID对抽取API
 * @className: IdMappingRelationController
 * @description: ID对抽取接口
 * @author: wangzhongcheng
 * @date: 2025/3/6 11:33
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/deepsight/v1/idMappingRel")
public class IdMappingRelationController {

    @Autowired
    private IdMappingRelService idMappingRelService;

    @Autowired
    private IdMappingManagerService idMappingManagerService;

    /**
     * 创建ID对
     * @return
     */
    @PostMapping("/create")
    public BaseResponse<Void> createIdMappingRelation(@Valid @RequestBody CreateIdMappingRelRequest request) {
        Boolean switchStatus = idMappingManagerService.getSwitchStatus();
        if (Objects.nonNull(switchStatus) && switchStatus) {
            log.error("创建ID对失败，ID-Mapping启动中 dataTableId: {}", request.getDataTableId());
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "ID-Mapping启动中，ID对不能创建");
        }

        idMappingManagerService.createIdMappingRel(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 更新ID对
     * @return
     */
    @PostMapping("/update")
    public BaseResponse<Void> updateIdMappingRelation(@Valid @RequestBody UpdateIdMappingRelRequest request) {
        Boolean switchStatus = idMappingManagerService.getSwitchStatus();
        if (Objects.nonNull(switchStatus) && switchStatus) {
            log.error("更新ID对失败，ID-Mapping启动中 idMappingRelId: {}", request.getIdMappingRelId());
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "ID-Mapping启动中，ID对不能修改");
        }

        idMappingManagerService.updateIdMappingRel(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 删除ID对
     * @return
     */
    @PostMapping("/delete")
    public BaseResponse<Void> deleteIdMappingRelation(@Valid @RequestBody DeleteIdMappingRelRequest request) {
        Boolean switchStatus = idMappingManagerService.getSwitchStatus();
        if (Objects.nonNull(switchStatus) && switchStatus) {
            log.error("更新ID对失败，ID-Mapping启动中 idMappingRelId: {}", request.getIdMappingRelId());
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "ID-Mapping启动中，ID对不能删除");
        }

        idMappingManagerService.deleteIdMappingRel(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 验证公共ID对是否存在，新建或者更新ID对时校验使用
     * @param request id对字段
     * @return false:不存在 true:存在
     */
    @PostMapping("/verifyPublicId")
    public BaseResponse<VerifyPublicIdResponse> verifyIdMappingPublicId(
            @Valid @RequestBody VerifyPublicIdRequest request) {
        boolean isExist = idMappingRelService.isExistPublicId(request.getDataTableId(), request.getTableEnFields());
        return BaseResponse.of(new VerifyPublicIdResponse(isExist));
    }

    /**
     * ID对列表查询
     * @param request 分页请求
     * @return ID对分页查询结果
     */
    @PostMapping("/list")
    public BasePageResponse<IdMappingRelItemResponse> listIdMappingRel(
            @Valid @RequestBody ListIdMappingRelRequest request) {
        return BasePageResponse.of(idMappingRelService.listIdMappingRel(request));
    }

    /**
     * 获取ID对可选择的数据集列表
     *
     * @return 返回数据集列表的响应对象
     */
    @GetMapping("/dataset/list")
    public BaseResponse<IdMappingDatasetListResponse> listDataset() {
        return BaseResponse.of(idMappingRelService.listIdMappingDataset());
    }

    /**
     * 获取ID对可选择的数据集字段列表
     *
     * @return 返回数据集列表的响应对象
     */
    @GetMapping("/dataset/field/list")
    public BaseResponse<IdMappingDatasetFieldListResponse> listDatasetFields(@Param("dataTableId") Long dataTableId) {
        return BaseResponse.of(idMappingRelService.listIdMappingDatasetField(dataTableId));
    }
}
