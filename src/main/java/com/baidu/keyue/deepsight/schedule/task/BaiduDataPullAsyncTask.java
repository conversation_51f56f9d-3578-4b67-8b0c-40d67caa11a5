package com.baidu.keyue.deepsight.schedule.task;

import com.baidu.keyue.deepsight.database.redis.RedisConfiguration;
import com.baidu.keyue.deepsight.service.user.BaiduUserDataService;
import com.baidu.keyue.deepsight.service.user.UserProfileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;


@Slf4j
@Service
public class BaiduDataPullAsyncTask {

    @Autowired
    private RedisConfiguration redisConfiguration;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private BaiduUserDataService baiduUserDataService;

    @Autowired
    private UserProfileService userProfileService;

    public String generateSchedulerLockKey(String... keys) {
        return StringUtils.join(keys, "-");
    }

    @Async("baiduDataPullTaskExecutor")
    public CompletableFuture<Void> processTable(String table) {
        String lockKey = generateSchedulerLockKey(redisConfiguration.getPrefix(), "pullDataScheduler", "Lock", table);
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("pullDataScheduler get lock failed, lockKey: {}", lockKey);
            return CompletableFuture.completedFuture(null);
        }

        // 拉取所有mock_user_xxx表的用户mobile用于查询百度数据，然后将查询到的数据更新到mock_user_xxx表中
        log.info("pullDataScheduler start handle table:{}", table);
        try {
            baiduUserDataService.handelBaiduDataProcess(table);
        } catch (Exception e) {
            log.error("pullDataScheduler handelBaiduDataProcess exec task failed, table: {}, err: ", table, e);
        }

        try {
            String tenantId = StringUtils.substringAfterLast(table, "_");
            userProfileService.mergeUserProfileByTenantId(tenantId);
        } catch (Exception e) {
            log.error("pullDataScheduler mergeUserProfileByTenantId exec task failed, table: {}, err: ", table, e);
        }
        if (lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
        return CompletableFuture.completedFuture(null);
    }

}
