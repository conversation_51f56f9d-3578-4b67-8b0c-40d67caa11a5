package com.baidu.keyue.deepsight.enums;

import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @className: FilterTypeEnum
 * @description:
 * @author: wangz<PERSON><PERSON>
 * @date: 2024/12/21 16:23
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum FilterTypeEnum {

    STRING("string", "字符串"),
    NUMBER("number", "数值"),
    TIME("time", "时间"),
    BOOLEAN("boolean", "布尔值");

    @JsonValue
    private String type;
    private String desc;

    /**
     * 根据字段类型枚举转换为过滤器类型枚举
     */
    public static FilterTypeEnum convertFrom(String filedType) {
        return convertFrom(TableFieldTypeEnum.fromValue(filedType));
    }

    /**
     * 根据字段类型枚举转换为过滤器类型枚举
     */
    public static FilterTypeEnum convertFrom(TableFieldTypeEnum fieldTypeEnum) {
        switch (fieldTypeEnum) {
            case STRING, STRINGS:
                return STRING;
            case NUMBER:
                return NUMBER;
            case TIME:
                return TIME;
            case BOOLEAN:
                return BOOLEAN;
        }

        throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "不支持的数据类型");
    }

}
