package com.baidu.keyue.deepsight.service.customer.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.argThat;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

import com.baidu.keyue.deepsight.config.BosConfig;
import com.baidu.keyue.deepsight.config.CustomerCalculateConfiguration;
import com.baidu.keyue.deepsight.database.doris.DorisConfiguration;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.GroupingTypeEnum;
import com.baidu.keyue.deepsight.enums.MatchPoliciesEnum;
import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.enums.UpdateModEnum;
import com.baidu.keyue.deepsight.models.base.request.DeepSightWebContext;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.customer.request.CreateCustomerGroupRequest;
import com.baidu.keyue.deepsight.models.customer.request.CreateCustomerImportRequest;
import com.baidu.keyue.deepsight.models.customer.request.DeleteCustomerGroupRequest;
import com.baidu.keyue.deepsight.models.customer.request.DeleteCustomerRequest;
import com.baidu.keyue.deepsight.models.customer.request.GetCustomerGroupRequest;
import com.baidu.keyue.deepsight.models.customer.request.GetCustomerListRequest;
import com.baidu.keyue.deepsight.models.customer.request.SamplingStatisticsCustomerGroupRequest;
import com.baidu.keyue.deepsight.models.customer.request.UpdateCustomerGroupRequest;
import com.baidu.keyue.deepsight.models.customer.response.CreateCustomerImportResponse;
import com.baidu.keyue.deepsight.models.customer.response.CustomerGroupDetailResponse;
import com.baidu.keyue.deepsight.models.customer.response.SamplingStatisticsCustomerGroupResponse;
import com.baidu.keyue.deepsight.models.datamanage.dto.FileDetailDto;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.label.TriggerFrequencyValue;
import com.baidu.keyue.deepsight.models.rules.dto.DqlParseResult;
import com.baidu.keyue.deepsight.models.rules.dto.RuleGroup;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroupCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.CustomerGroupMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.datamanage.impl.DorisConfServiceImpl;
import com.baidu.keyue.deepsight.service.datamanage.impl.TableRecordCommonService;
import com.baidu.keyue.deepsight.service.rules.RuleManagerService;
import com.baidu.keyue.deepsight.service.rules.RuleParseService;
import com.baidu.keyue.deepsight.service.tasks.TaskInfoService;
import com.baidu.keyue.deepsight.service.tool.BosUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.utils.XID;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
@TestPropertySource(properties = {
        "kafka.topics.dataSync=deep_sight_data_sync",
        "file.upload.suffixNames=.csv,.xlsx"
})
public class CustomerGroupServiceImplTest {


    @Mock
    private CustomerCalculateConfiguration customerCalculateConfiguration;

    @Mock
    private DorisConfiguration dorisConfiguration;

    private SamplingStatisticsCustomerGroupRequest request2;

    private CustomerGroup customerGroup;

    @Mock
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    private GetCustomerListRequest request1;

    private DqlParseResult dqlParseResult;

    @Mock
    private RuleManagerService ruleManagerService;

    @Mock
    private DorisConfServiceImpl dorisConfService;

    private DataTableInfo dataTableInfo;

    private TableFieldMetaInfo primaryKeyField;

    private VisibleFieldResponse visibleField;

    @Mock
    private RuleParseService ruleParseService;

    @Mock
    private RedissonClient redisson;

    @Mock
    private TaskInfoService taskInfoService;

    @Mock
    private WebContextHolder webContextHolder;

    @Mock
    private BosUtils bosUtils;

    @Mock
    private BosConfig bosConfig;

    private CreateCustomerImportRequest request;

    private List<FileDetailDto> files;

    @Mock
    private DataTableInfoMapper dataTableInfoMapper;

    @Mock
    private DataTableManageService dataTableManageService;

    private static final Long CUSTOMER_GROUP_ID = 1L;
    @Mock
    private CustomerGroupMapper customerGroupMapper;

    @Mock
    private DorisService dorisService;
    @Mock
    private TableRecordCommonService tableRecordCommonService;

    @InjectMocks
    private CustomerGroupServiceImpl customerGroupService;

    @Mock
    private KafkaTemplate<String, String> kafkaTemplate;

    private static final String TENANT_ID = "testTenant";

    private static final String USER_ID = "testUser";

    private static final String GROUP_NAME = "testGroup";

    private final Long customerGroupId = 100L;
    private final String tableName = "mock_user_table";
    private final Long dataTableId = 1L;

    @BeforeEach
    void setup() {
        FileDetailDto file = new FileDetailDto();
        file.setFileName("test.csv");
        file.setBosKey("test.csv");
        files = Collections.singletonList(file);

        request = new CreateCustomerImportRequest();
        request.setFiles(files);

        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setTenantId(12345L);
        userAuthInfo.setUserId(123L);
        DeepSightWebContext deepSightWebContext = new DeepSightWebContext(userAuthInfo);
        String requestId = XID.generateRequestID();
        deepSightWebContext.setRequestId(requestId);

        // 设置全局租户信息
        WebContextHolder.setDeepSightWebContext(deepSightWebContext);
    }

    @Test
    void deleteCustomer_shouldThrowExceptionWhenTableNotExists() {
        // Arrange
        when(dataTableInfoMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        DeleteCustomerRequest request = new DeleteCustomerRequest();
        request.setIds(Arrays.asList("1", "2", "3"));
        request.setOp("DELETE");
        request.setCustomerGroupId(customerGroupId);

        // Act & Assert
        DeepSightException.CustomerGroupFailedException exception = assertThrows(
                DeepSightException.CustomerGroupFailedException.class,
                () -> customerGroupService.deleteCustomer(request)
        );
        assertEquals("不存在的表", exception.getMessage());
    }

    @Test
    void deleteCustomer_shouldExecuteUpdateSqlWhenAllConditionsMet() throws Exception {
        // Arrange
        DataTableInfo tableInfo = new DataTableInfo();
        tableInfo.setId(dataTableId);
        tableInfo.setTableName(tableName);
        when(dataTableInfoMapper.selectByExample(any())).thenReturn(Collections.singletonList(tableInfo));

        TableFieldMetaInfo metaInfo = new TableFieldMetaInfo();
        metaInfo.setEnField("customer_id");
        when(tableFieldMetaInfoMapper.selectByExampleWithBLOBs(any()))
                .thenReturn(Collections.singletonList(metaInfo));

        DeleteCustomerRequest request = new DeleteCustomerRequest();
        request.setIds(Arrays.asList("1", "2", "3"));
        request.setOp("DELETE");
        request.setCustomerGroupId(customerGroupId);

        // Act
        customerGroupService.deleteCustomer(request);

        // Assert
        String expectedSql = "update mock_user_table AS a set a.process_customer_100 = '0' where customer_id in (\"1\",\"2\",\"3\")";
        verify(dorisService).execSql(expectedSql);
    }

    @Test
    void testNewDiffusionCustomerGroupWhenGroupExistsShouldReturnExistingGroup() {
        CustomerGroup existingGroup = new CustomerGroup();
        existingGroup.setId(1L);
        when(customerGroupMapper.selectByExample(any(CustomerGroupCriteria.class)))
                .thenReturn(List.of(existingGroup));

        CustomerGroup result = customerGroupService.newDiffusionCustomerGroup(TENANT_ID, USER_ID, GROUP_NAME);

        assertNotNull(result);
        assertEquals(1L, result.getId());
        verify(customerGroupMapper, times(1)).selectByExample(any(CustomerGroupCriteria.class));
        verifyNoMoreInteractions(customerGroupMapper);
        verifyNoInteractions(dorisService);
    }

    @Test
    void testNewDiffusionCustomerGroupWhenGroupNotExistsShouldCreateNewGroup() {
        when(customerGroupMapper.selectByExample(any(CustomerGroupCriteria.class)))
                .thenReturn(Collections.emptyList());

        CustomerGroup newGroup = new CustomerGroup();
        newGroup.setId(1L);
        when(customerGroupMapper.insert(any(CustomerGroup.class))).thenAnswer(invocation -> {
            CustomerGroup group = invocation.getArgument(0);
            group.setId(1L);
            return 1;
        });

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            tenantUtilsMock.when(() -> TenantUtils.generateMockUserTableName(TENANT_ID))
                    .thenReturn("mock_user_table");
            tenantUtilsMock.when(() -> TenantUtils.generateUserProfileTableName(TENANT_ID))
                    .thenReturn("user_profile_table");

            CustomerGroup result = customerGroupService.newDiffusionCustomerGroup(TENANT_ID, USER_ID, GROUP_NAME);

            assertNotNull(result);
            assertEquals(1L, result.getId());
            assertEquals(USER_ID, result.getUserId());
            assertEquals(TENANT_ID, result.getTenantId());
            assertEquals(GROUP_NAME, result.getCustomerGroupName());
            assertEquals("", result.getCustomerGroupDescription());
            assertEquals(UpdateModEnum.REPLACE.getCode(), result.getCustomerGroupValueUpdateMod());
            assertEquals(TriggerModeEnum.MANUAL.getCode(), result.getTriggerMod());
            assertEquals(TaskExecStatusEnum.SUCCESS.getCode(), result.getCalStatus());
            assertEquals(0L, result.getTask());
            assertEquals(DelEnum.NOT_DELETED.getBoolean(), result.getDel());
            assertEquals(GroupingTypeEnum.MODEL_PREDICTION.getCode(), result.getGroupingType());
            assertFalse(result.getConfigTag());

            verify(customerGroupMapper, times(1)).insert(any(CustomerGroup.class));
            verify(dorisService, times(2)).execSql(anyString());
        }
    }

    @Test
    void testNewDiffusionCustomerGroupWhenInsertFailsShouldReturnNull() {
        when(customerGroupMapper.selectByExample(any(CustomerGroupCriteria.class)))
                .thenReturn(Collections.emptyList());
        when(customerGroupMapper.insert(any(CustomerGroup.class))).thenThrow(new RuntimeException("Mock DB error"));

        CustomerGroup result = customerGroupService.newDiffusionCustomerGroup(TENANT_ID, USER_ID, GROUP_NAME);

        assertNull(result);
        verify(customerGroupMapper, times(1)).insert(any(CustomerGroup.class));
    }

    @Test
    void testGetCustomerGroupDetail() {
        // Setup request
        GetCustomerGroupRequest request = new GetCustomerGroupRequest();
        request.setCustomerGroupId(CUSTOMER_GROUP_ID);
        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setTenantId(1L);
        userAuthInfo.setUserId(1L);
        // Mock WebContextHolder
        WebContextHolder.setDeepSightWebContext(new DeepSightWebContext(userAuthInfo));
        // Mock DataTableInfo
        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        when(dataTableInfoMapper.selectByExample(any())).thenReturn(List.of(dataTableInfo));

        // Mock CustomerGroup
        CustomerGroup customerGroup = new CustomerGroup();
        customerGroup.setId(CUSTOMER_GROUP_ID);
        customerGroup.setCustomerGroupName(GROUP_NAME);
        customerGroup.setCustomerGroupDescription("Test Description");
        customerGroup.setCreateTime(new Date());
        customerGroup.setCreator(USER_ID);
        customerGroup.setCalStatus(TaskExecStatusEnum.SUCCESS.getCode());
        customerGroup.setTriggerMod(TriggerModeEnum.MANUAL.getCode());
        customerGroup.setTriggerFrequency(TriggerFrequencyEnum.DAY.getCode());
        customerGroup.setTriggerFrequencyValue("{\"hour\":10,\"minute\":30}");
        customerGroup.setCustomerGroupValueUpdateMod(UpdateModEnum.REPLACE.getCode());
        customerGroup.setCustomerGroupRule("{\"rules\":[]}");
        customerGroup.setPreset(false);
        customerGroup.setLastCalDate(new Date());
        customerGroup.setGroupingType(GroupingTypeEnum.FILE_IMPORT.getCode());
        customerGroup.setConfigTag(true);
        customerGroup.setTenantId(TENANT_ID);
        customerGroup.setDel(DelEnum.NOT_DELETED.getBoolean());
        when(customerGroupMapper.selectByExampleWithBLOBs(any())).thenReturn(List.of(customerGroup));

        // Execute
        CustomerGroupDetailResponse response = customerGroupService.getCustomerGroupDetail(request);

        // Verify
        assertNotNull(response);
        assertEquals(GROUP_NAME, response.getCustomerGroupName());
        assertEquals(CUSTOMER_GROUP_ID, response.getCustomerGroupId());
        assertEquals(1L, response.getDataTableId());
        assertEquals("Test Description", response.getCustomerGroupDescription());
        assertEquals(USER_ID, response.getCreator());
        assertEquals(TaskExecStatusEnum.SUCCESS, response.getStatus());
        assertEquals(TriggerModeEnum.MANUAL, response.getTriggerMod());
        assertEquals(TriggerFrequencyEnum.DAY, response.getTriggerFrequency());
        assertNotNull(response.getTriggerFrequencyValue());
        assertEquals(UpdateModEnum.REPLACE, response.getUpdateMod());
        assertNotNull(response.getCustomerGroupRule());
        assertFalse(response.getIsPreset());
        assertNotNull(response.getLastTaskDate());
        assertEquals(GroupingTypeEnum.FILE_IMPORT, response.getGroupingType());
        assertEquals(true, response.getConfigTag());

        verify(customerGroupMapper, times(1)).selectByExampleWithBLOBs(any());
        verify(dataTableInfoMapper, times(1)).selectByExample(any());
    }

    @Test
    void importDataShouldReturnResponseWhenValidCsvFile() {
        CustomerGroupServiceImpl customerGroupService = Mockito.spy(CustomerGroupServiceImpl.class);
        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setTenantId(1L);
        userAuthInfo.setUserId(1L);
        String mockUserTableName = TenantUtils.generateMockUserTableName(userAuthInfo.getTenantId().toString());
        WebContextHolder.setDeepSightWebContext(new DeepSightWebContext(userAuthInfo));
        List<VisibleFieldResponse> visibleFields = new ArrayList<>();
        VisibleFieldResponse visibleFieldResponse = new VisibleFieldResponse();
        visibleFieldResponse.setEnName("user_id");
        visibleFields.add(visibleFieldResponse);
        BosConfig.Bucket bucket = new BosConfig.Bucket();
        bucket.setDataSync("dataSync");
        request.setName("test");
        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        ReflectionTestUtils.setField(customerGroupService, "tableRecordCommonService", tableRecordCommonService);
        ReflectionTestUtils.setField(customerGroupService, "dataTableManageService", dataTableManageService);
        ReflectionTestUtils.setField(customerGroupService, "customerGroupMapper", customerGroupMapper);
        ReflectionTestUtils.setField(customerGroupService, "bosConfig", bosConfig);
        ReflectionTestUtils.setField(customerGroupService, "ignoreField", Set.of("city"));
        ReflectionTestUtils.setField(customerGroupService, "suffixNames", "csv");
        when(customerGroupMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(bosConfig.getBucket()).thenReturn(bucket);
        when(tableRecordCommonService.getTableByTableName(anyString())).thenReturn(dataTableInfo);
        when(dataTableManageService.getVisibleFields(mockUserTableName, false)).thenReturn(visibleFields);
        doReturn(List.of("user_id")).when(customerGroupService).checkData(request.getFiles(), bucket.getDataSync());
        doReturn(1L).when(customerGroupService).createCustomerGroup(any(CreateCustomerGroupRequest.class));
        doReturn(Collections.emptyList()).when(customerGroupService).importByCsv(any(), any(), any(), any(), any(), any());
        CreateCustomerImportResponse response = customerGroupService.importData(request);
        assertNotNull(response);
        assertTrue(response.getMessage().isEmpty());
    }

    @Test
    void testDeleteCustomerGroupWhenGroupExistsShouldUpdateAndDeleteTask() {
        // Setup
        DeleteCustomerGroupRequest deleteRequest = new DeleteCustomerGroupRequest();
        deleteRequest.setCustomerGroupId(CUSTOMER_GROUP_ID);

        CustomerGroup customerGroup = new CustomerGroup();
        customerGroup.setId(CUSTOMER_GROUP_ID);
        customerGroup.setTask(2L);

        when(customerGroupMapper.selectByExampleWithBLOBs(any())).thenReturn(List.of(customerGroup));

        // Execute
        customerGroupService.deleteCustomerGroup(deleteRequest);

        // Verify
        verify(customerGroupMapper).selectByExampleWithBLOBs(any());
        verify(customerGroupMapper).updateByPrimaryKey(argThat(group ->
                group.getDel() == DelEnum.DELETED.getBoolean() &&
                        group.getModifier().equals("123") &&
                        group.getLastCalDate() != null &&
                        group.getUpdateTime() != null
        ));
        verify(taskInfoService).deleteFieldById(2L);
    }

    @Test
    void testDeleteCustomerGroupWhenGroupNotExistsShouldThrowException() {
        // Setup
        DeleteCustomerGroupRequest deleteRequest = new DeleteCustomerGroupRequest();
        deleteRequest.setCustomerGroupId(CUSTOMER_GROUP_ID);

        when(customerGroupMapper.selectByExampleWithBLOBs(any())).thenReturn(List.of());

        // Execute & Verify
        DeepSightException.CustomerGroupFailedException exception = assertThrows(
                DeepSightException.CustomerGroupFailedException.class,
                () -> customerGroupService.deleteCustomerGroup(deleteRequest)
        );
        assertEquals(ErrorCode.NOT_FOUND, exception.getErrorCode());
        assertEquals("客群不存在", exception.getMessage());

        verify(customerGroupMapper).selectByExampleWithBLOBs(any());
        verifyNoMoreInteractions(customerGroupMapper);
        verifyNoInteractions(taskInfoService);
    }

    @Test
    void testDeleteCustomerGroupWhenNoTaskShouldNotCallTaskService() {
        // Setup
        DeleteCustomerGroupRequest deleteRequest = new DeleteCustomerGroupRequest();
        deleteRequest.setCustomerGroupId(CUSTOMER_GROUP_ID);

        CustomerGroup customerGroup = new CustomerGroup();
        customerGroup.setId(CUSTOMER_GROUP_ID);
        customerGroup.setTask(null);

//        when(webContextHolder.getUserAuthInfo().getUserId()).thenReturn(123L);
//         when(customerGroupMapper.selectByPrimaryKey(CUSTOMER_GROUP_ID)).thenReturn(customerGroup);
        when(customerGroupMapper.selectByExampleWithBLOBs(any())).thenReturn(List.of(customerGroup));

        // Execute
        customerGroupService.deleteCustomerGroup(deleteRequest);

        // Verify
        verify(customerGroupMapper).selectByExampleWithBLOBs(any());
        verify(customerGroupMapper).updateByPrimaryKey(any(CustomerGroup.class));
        verifyNoInteractions(taskInfoService);
    }

    @Test
    public void getByIdAndTenantIdExists() {
        CustomerGroupCriteria customerGroupCriteria = new CustomerGroupCriteria();
        CustomerGroupCriteria.Criteria criteria = customerGroupCriteria.createCriteria();
        criteria.andTenantIdEqualTo("1").andDelEqualTo(DelEnum.NOT_DELETED.getBoolean()).andIdEqualTo(1L);
        when(customerGroupMapper.selectByExampleWithBLOBs(customerGroupCriteria)).thenReturn(List.of(new CustomerGroup()));
        customerGroupService.getByIdAndTenantId(1L, "1");
    }

    @Test
    public void getByIdAndTenantIdNotExists() {
        CustomerGroupCriteria customerGroupCriteria = new CustomerGroupCriteria();
        CustomerGroupCriteria.Criteria criteria = customerGroupCriteria.createCriteria();
        criteria.andTenantIdEqualTo("1").andDelEqualTo(DelEnum.NOT_DELETED.getBoolean()).andIdEqualTo(1L);
        when(customerGroupMapper.selectByExampleWithBLOBs(customerGroupCriteria)).thenReturn(Collections.emptyList());
        customerGroupService.getByIdAndTenantId(1L, "1");
    }

    @Test
    void testGetCustomerListSuccess() {
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TENANT_ID);
            mocked.when(WebContextHolder::getUserId).thenReturn(USER_ID);

            GetCustomerListRequest request = new GetCustomerListRequest();
            request.setPageNo(1);
            request.setPageSize(10);
            request.setCustomerGroupId(1L);

            dataTableInfo = new DataTableInfo();
            dataTableInfo.setId(1L);
            dataTableInfo.setTableName("test_table");

            primaryKeyField = new TableFieldMetaInfo();
            primaryKeyField.setEnField("id");
            primaryKeyField.setFieldTag(TableFieldTagEnum.PRIMARY.getCode());

            visibleField = new VisibleFieldResponse();
            visibleField.setEnName("name");

            dqlParseResult = new DqlParseResult();
            dqlParseResult.setSelect("*");
            dqlParseResult.setFrom("test_table");

            // Mock dataTableInfoMapper
            when(dataTableInfoMapper.selectByExample(any())).thenReturn(List.of(dataTableInfo));

            // Mock tableFieldMetaInfoMapper
            when(tableFieldMetaInfoMapper.selectByExampleWithBLOBs(any())).thenAnswer(invocation -> {
                TableFieldMetaInfoCriteria criteria = invocation.getArgument(0);
                if (criteria.getOredCriteria().get(0).getCriteria().get(0)
                        .getCondition().equals("is_visable =")) {
                    return List.of(new TableFieldMetaInfo());
                } else {
                    return List.of(primaryKeyField);
                }
            });

            // Mock ruleManagerService
            when(ruleManagerService.parseRuleNode(any())).thenReturn(dqlParseResult);

            // Mock dorisService
            when(dorisService.getCount(anyString())).thenReturn(100L);
            when(dorisService.selectList(anyString())).thenReturn(List.of(Map.of("name", "test")));

            // Mock dorisConfService
            when(dorisConfService.dorisDataConvertToShowData(anyLong(), any(), any()))
                    .thenReturn(Map.of("name", "test"));

            // Execute
            BasePageResponse.Page<Map<String, String>> result = customerGroupService.getCustomerList(request);

            // Verify
            assertNotNull(result);
            assertEquals(1, result.getPageNo());
            assertEquals(10, result.getPageSize());
            assertEquals(100L, result.getTotal());
            assertEquals(1, result.getResults().size());
            assertEquals("test", result.getResults().get(0).get("name"));
        }

    }

    @Test
    void testGetCustomerListWhenNoVisibleFields() {
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            GetCustomerListRequest request = new GetCustomerListRequest();
            request.setPageNo(1);
            request.setPageSize(10);
            request.setCustomerGroupId(1L);

            dataTableInfo = new DataTableInfo();
            dataTableInfo.setId(1L);
            dataTableInfo.setTableName("test_table");

            primaryKeyField = new TableFieldMetaInfo();
            primaryKeyField.setEnField("id");
            primaryKeyField.setFieldTag(TableFieldTagEnum.PRIMARY.getCode());

            visibleField = new VisibleFieldResponse();
            visibleField.setEnName("name");

            dqlParseResult = new DqlParseResult();
            dqlParseResult.setSelect("*");
            dqlParseResult.setFrom("test_table");

            // Mock dataTableInfoMapper
            when(dataTableInfoMapper.selectByExample(any())).thenReturn(List.of(dataTableInfo));

            // Mock tableFieldMetaInfoMapper to return empty list for visible fields
            when(tableFieldMetaInfoMapper.selectByExampleWithBLOBs(any())).thenReturn(Collections.emptyList());

            // Execute and verify exception
            assertThrows(DeepSightException.CustomerGroupFailedException.class,
                    () -> customerGroupService.getCustomerList(request));
        }
    }

    @Test
    void testUpdateCustomerGroupWhenGroupNotFoundShouldThrowException() {
        // Prepare test data
        UpdateCustomerGroupRequest request = new UpdateCustomerGroupRequest();
        request.setCustomerGroupId(1L);

        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(123L);
        userAuthInfo.setTenantId(456L);
        DeepSightWebContext webContext = new DeepSightWebContext(userAuthInfo);
        WebContextHolder.setDeepSightWebContext(webContext);

        // Mock dependencies
        when(customerGroupMapper.selectByExampleWithBLOBs(any(CustomerGroupCriteria.class)))
                .thenReturn(Collections.emptyList());
        doNothing().when(ruleParseService).checkRuleGroupTenantPermission(any(), any());
        RLock rLock = mock(RLock.class);
        when(rLock.tryLock()).thenReturn(true);
        when(redisson.getLock(anyString())).thenReturn(rLock);

        // Execute and verify
        assertThrows(DeepSightException.CustomerGroupFailedException.class,
                () -> customerGroupService.updateCustomerGroup(request));
    }

    @Test
    void testUpdateCustomerGroupSuccess() {
        // Prepare test data
        UpdateCustomerGroupRequest request = new UpdateCustomerGroupRequest();
        request.setCustomerGroupId(1L);
        request.setCustomerGroupName("newName");
        request.setCustomerGroupDescription("newDesc");
        request.setCustomerGroupRule(new RuleGroup());
        request.setUpdateMod(UpdateModEnum.MERGE);
        request.setTriggerMod(TriggerModeEnum.MANUAL);

        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(123L);
        userAuthInfo.setTenantId(456L);
        DeepSightWebContext webContext = new DeepSightWebContext(userAuthInfo);
        WebContextHolder.setDeepSightWebContext(webContext);

        CustomerGroup existingGroup = new CustomerGroup();
        existingGroup.setId(1L);
        existingGroup.setCalStatus(TaskExecStatusEnum.SUCCESS.getCode());
        existingGroup.setTask(100L);

        // Mock dependencies
        when(customerGroupMapper.selectByExampleWithBLOBs(any(CustomerGroupCriteria.class)))
                .thenReturn(List.of(existingGroup));
        doNothing().when(ruleParseService).checkRuleGroupTenantPermission(any(), any());
        RLock rLock = mock(RLock.class);
        when(rLock.tryLock()).thenReturn(true);
        when(redisson.getLock(anyString())).thenReturn(rLock);
        doNothing().when(taskInfoService).updateLabelCalTaskTrigger(anyLong(), any(), any(), any());

        // Execute
        customerGroupService.updateCustomerGroup(request);

        // Verify
        verify(customerGroupMapper, times(1)).selectByExampleWithBLOBs(any(CustomerGroupCriteria.class));
        verify(customerGroupMapper, times(1)).updateByPrimaryKeySelective(any(CustomerGroup.class));
        verify(taskInfoService, times(1)).updateLabelCalTaskTrigger(anyLong(), any(), any(), any());
    }

    @Test
    void testUpdateCustomerGroupWhenRunningShouldThrowException() {
        // Prepare test data
        UpdateCustomerGroupRequest request = new UpdateCustomerGroupRequest();
        request.setCustomerGroupId(1L);

        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(123L);
        userAuthInfo.setTenantId(456L);
        DeepSightWebContext webContext = new DeepSightWebContext(userAuthInfo);
        WebContextHolder.setDeepSightWebContext(webContext);

        CustomerGroup existingGroup = new CustomerGroup();
        existingGroup.setId(1L);
        existingGroup.setCalStatus(TaskExecStatusEnum.RUNNING.getCode());

        // Mock dependencies
        when(customerGroupMapper.selectByExampleWithBLOBs(any(CustomerGroupCriteria.class)))
                .thenReturn(List.of(existingGroup));
        doNothing().when(ruleParseService).checkRuleGroupTenantPermission(any(), any());
        RLock rLock = mock(RLock.class);
        when(rLock.tryLock()).thenReturn(true);
        when(redisson.getLock(anyString())).thenReturn(rLock);

        // Execute and verify
        assertThrows(DeepSightException.CustomerGroupFailedException.class,
                () -> customerGroupService.updateCustomerGroup(request));
    }

    @Test
    void testUpdateCustomerGroupWhenLockFailedShouldThrowException() {
        // Prepare test data
        UpdateCustomerGroupRequest request = new UpdateCustomerGroupRequest();
        request.setCustomerGroupId(1L);

        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(123L);
        userAuthInfo.setTenantId(456L);
        DeepSightWebContext webContext = new DeepSightWebContext(userAuthInfo);
        WebContextHolder.setDeepSightWebContext(webContext);

        CustomerGroup existingGroup = new CustomerGroup();
        existingGroup.setId(1L);
        existingGroup.setCalStatus(TaskExecStatusEnum.SUCCESS.getCode());

        // Mock dependencies
        when(customerGroupMapper.selectByExampleWithBLOBs(any(CustomerGroupCriteria.class)))
                .thenReturn(List.of(existingGroup));
        doNothing().when(ruleParseService).checkRuleGroupTenantPermission(any(), any());
        RLock lock = mock(RLock.class);
        when(redisson.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(false);

        // Execute and verify
        assertThrows(DeepSightException.BusyRequestException.class,
                () -> customerGroupService.updateCustomerGroup(request));
    }

    @Test
    void testUpdateCustomerGroupSuccess1() {
        // Prepare test data
        UpdateCustomerGroupRequest request = new UpdateCustomerGroupRequest();
        request.setCustomerGroupId(1L);
        request.setCustomerGroupName("newGroupName");
        request.setCustomerGroupDescription("new description");
        request.setUpdateMod(UpdateModEnum.REPLACE);
        request.setTriggerMod(TriggerModeEnum.MANUAL);
        request.setTriggerFrequency(TriggerFrequencyEnum.DAY);
        request.setTriggerFrequencyValue(new TriggerFrequencyValue());

        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(123L);
        userAuthInfo.setTenantId(456L);
        DeepSightWebContext webContext = new DeepSightWebContext(userAuthInfo);
        WebContextHolder.setDeepSightWebContext(webContext);

        // Mock dependencies
        CustomerGroup existingGroup = new CustomerGroup();
        existingGroup.setId(1L);
        existingGroup.setTenantId("456");
        existingGroup.setCalStatus(TaskExecStatusEnum.PENDING.getCode());
        existingGroup.setTask(100L);

        when(customerGroupMapper.selectByExampleWithBLOBs(any(CustomerGroupCriteria.class)))
                .thenReturn(Collections.singletonList(existingGroup));

        doNothing().when(ruleParseService).checkRuleGroupTenantPermission(any(), any());

        RLock rLock = mock(RLock.class);
        when(rLock.tryLock()).thenReturn(true);
        when(redisson.getLock(anyString())).thenReturn(rLock);

        when(customerGroupMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
        doNothing().when(taskInfoService).updateLabelCalTaskTrigger(anyLong(), any(), any(), any());

        // Execute
        customerGroupService.updateCustomerGroup(request);

        // Verify
        verify(customerGroupMapper).updateByPrimaryKeySelective(any(CustomerGroup.class));
        verify(taskInfoService).updateLabelCalTaskTrigger(eq(100L), any(), any(), any());
        verify(rLock).unlock();
    }

    @Test
    void testUpdateCustomerGroupWhenGroupIsRunningShouldThrowException() {
        // Prepare test data
        UpdateCustomerGroupRequest request = new UpdateCustomerGroupRequest();
        request.setCustomerGroupId(1L);

        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(123L);
        userAuthInfo.setTenantId(456L);
        DeepSightWebContext webContext = new DeepSightWebContext(userAuthInfo);
        WebContextHolder.setDeepSightWebContext(webContext);

        // Mock dependencies
        CustomerGroup existingGroup = new CustomerGroup();
        existingGroup.setId(1L);
        existingGroup.setTenantId("456");
        existingGroup.setCalStatus(TaskExecStatusEnum.RUNNING.getCode());

        when(customerGroupMapper.selectByExampleWithBLOBs(any(CustomerGroupCriteria.class)))
                .thenReturn(Collections.singletonList(existingGroup));

        doNothing().when(ruleParseService).checkRuleGroupTenantPermission(any(), any());

        RLock rLock = mock(RLock.class);
        when(rLock.tryLock()).thenReturn(true);
        when(redisson.getLock(anyString())).thenReturn(rLock);

        // Execute and verify
        assertThrows(DeepSightException.CustomerGroupFailedException.class,
                () -> customerGroupService.updateCustomerGroup(request));
    }

    @Test
    void testUpdateCustomerGroupWhenLockFailedShouldThrowException1() {
        // Prepare test data
        UpdateCustomerGroupRequest request = new UpdateCustomerGroupRequest();
        request.setCustomerGroupId(1L);

        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(123L);
        userAuthInfo.setTenantId(456L);
        DeepSightWebContext webContext = new DeepSightWebContext(userAuthInfo);
        WebContextHolder.setDeepSightWebContext(webContext);

        // Mock dependencies
        CustomerGroup existingGroup = new CustomerGroup();
        existingGroup.setId(1L);
        existingGroup.setTenantId("456");
        existingGroup.setCalStatus(TaskExecStatusEnum.PENDING.getCode());

        when(customerGroupMapper.selectByExampleWithBLOBs(any(CustomerGroupCriteria.class)))
                .thenReturn(Collections.singletonList(existingGroup));

        doNothing().when(ruleParseService).checkRuleGroupTenantPermission(any(), any());

        RLock rLock = mock(RLock.class);
        when(rLock.tryLock()).thenReturn(false);
        when(redisson.getLock(anyString())).thenReturn(rLock);

        // Execute and verify
        assertThrows(DeepSightException.BusyRequestException.class,
                () -> customerGroupService.updateCustomerGroup(request));
    }

    @Test
    void samplingStatisticsConsumerGroupWhenUserCountLessThanSampleNumberShouldReturnSampleCount() {
        SamplingStatisticsCustomerGroupRequest request = new SamplingStatisticsCustomerGroupRequest();
        request.setCustomerGroupId(123L);

        customerGroup = new CustomerGroup();
        customerGroup.setId(123L);
        customerGroup.setTenantId("test_tenant");

        customerGroup.setCustomerGroupRule("{}");
        customerGroup.setCalStatus(TaskExecStatusEnum.RUNNING.getCode());
        when(customerGroupMapper.selectByPrimaryKey(request.getCustomerGroupId())).thenReturn(customerGroup);
        when(customerCalculateConfiguration.getSampleNumber()).thenReturn(1000);
        when(dorisConfiguration.getDb()).thenReturn("test_db");

        String userTableName = TenantUtils.generateMockUserTableName(customerGroup.getTenantId());
        String fullTableName = String.format("`test_db`.`%s`", userTableName);

        when(dorisService.getSingleTableCount(fullTableName)).thenReturn(800L); // userCount < sampleNumber

        DqlParseResult mockParseResult = mock(DqlParseResult.class);
        when(ruleManagerService.parseRuleGroup(any(RuleGroup.class), any(AtomicInteger.class))).thenReturn(mockParseResult);
        when(mockParseResult.parseSampleStatisticsSql(1000, fullTableName)).thenReturn("sample_sql");
        when(dorisService.getCount("sample_sql")).thenReturn(200L);

        SamplingStatisticsCustomerGroupResponse response = customerGroupService.samplingStatisticsConsumerGroup(request);

        assertNotNull(response);
        assertEquals(200L, response.getCount());
    }

    @Test
    void samplingStatisticsConsumerGroupWhenUserCountGreaterThanSampleNumberShouldReturnPredictedCount() {
        SamplingStatisticsCustomerGroupRequest request = new SamplingStatisticsCustomerGroupRequest();
        request.setCustomerGroupId(123L);

        customerGroup = new CustomerGroup();
        customerGroup.setId(123L);
        customerGroup.setTenantId("test_tenant");

        customerGroup.setCustomerGroupRule("{}");
        customerGroup.setCalStatus(TaskExecStatusEnum.RUNNING.getCode());
        when(customerGroupMapper.selectByPrimaryKey(request.getCustomerGroupId())).thenReturn(customerGroup);
        when(customerCalculateConfiguration.getSampleNumber()).thenReturn(1000);
        when(dorisConfiguration.getDb()).thenReturn("test_db");

        String userTableName = TenantUtils.generateMockUserTableName(customerGroup.getTenantId());
        String fullTableName = String.format("`test_db`.`%s`", userTableName);

        when(dorisService.getSingleTableCount(fullTableName)).thenReturn(5000L); // userCount > sampleNumber

        DqlParseResult mockParseResult = mock(DqlParseResult.class);
        when(ruleManagerService.parseRuleGroup(any(RuleGroup.class), any(AtomicInteger.class))).thenReturn(mockParseResult);
        when(mockParseResult.parseSampleStatisticsSql(1000, fullTableName)).thenReturn("sample_sql");
        when(dorisService.getCount("sample_sql")).thenReturn(200L);

        SamplingStatisticsCustomerGroupResponse response = customerGroupService.samplingStatisticsConsumerGroup(request);

        assertNotNull(response);
        // Expected: (5000 / 1000) * 200 = 5 * 200 = 1000
        assertEquals(1000L, response.getCount());
    }

    @Test
    void samplingStatisticsConsumerGroupWhenCustomerGroupNotFoundShouldReturnEmptyResponse() {
        SamplingStatisticsCustomerGroupRequest request = new SamplingStatisticsCustomerGroupRequest();
        request.setCustomerGroupId(123L);

        customerGroup = new CustomerGroup();
        customerGroup.setId(123L);
        customerGroup.setTenantId("test_tenant");

        when(customerGroupMapper.selectByPrimaryKey(request.getCustomerGroupId())).thenReturn(null);

        SamplingStatisticsCustomerGroupResponse response = customerGroupService.samplingStatisticsConsumerGroup(request);

        assertNotNull(response);
        assertEquals(0, response.getCount());
        verify(customerGroupMapper).selectByPrimaryKey(request.getCustomerGroupId());
    }

    @Test
    void samplingStatisticsConsumerGroupWhenCustomerGroupRuleEmptyShouldReturnEmptyResponse() {
        SamplingStatisticsCustomerGroupRequest request = new SamplingStatisticsCustomerGroupRequest();
        request.setCustomerGroupId(123L);

        customerGroup = new CustomerGroup();
        customerGroup.setId(123L);
        customerGroup.setTenantId("test_tenant");

        customerGroup.setCustomerGroupRule(null);
        when(customerGroupMapper.selectByPrimaryKey(request.getCustomerGroupId())).thenReturn(customerGroup);

        SamplingStatisticsCustomerGroupResponse response = customerGroupService.samplingStatisticsConsumerGroup(request);

        assertNotNull(response);
        assertEquals(0, response.getCount());
    }

    @Test
    void samplingStatisticsConsumerGroupWhenTaskAlreadySuccessShouldReturnCachedCount() {
        SamplingStatisticsCustomerGroupRequest request = new SamplingStatisticsCustomerGroupRequest();
        request.setCustomerGroupId(123L);

        customerGroup = new CustomerGroup();
        customerGroup.setId(123L);
        customerGroup.setTenantId("test_tenant");

        customerGroup.setCustomerGroupRule("{}");
        customerGroup.setCalStatus(TaskExecStatusEnum.SUCCESS.getCode());
        when(customerGroupMapper.selectByPrimaryKey(request.getCustomerGroupId())).thenReturn(customerGroup);
        when(dorisService.getCount(anyString())).thenReturn(1000L);

        SamplingStatisticsCustomerGroupResponse response = customerGroupService.samplingStatisticsConsumerGroup(request);

        assertNotNull(response);
        assertEquals(1000L, response.getCount());
    }

    @Test
    void testSaveData_WhenItemMapEmpty_ShouldReturnNull() {
        // Arrange
        Map<String, Object> itemMap = Collections.emptyMap();
        List<Map<String, Object>> errors = new ArrayList<>();
        CreateCustomerImportResponse response = new CreateCustomerImportResponse();
        response.setCustomerGroupId(1L);
        String tenantId = "testTenant";
        Map<String, String> encryptFields = Collections.emptyMap();
        MatchPoliciesEnum matchPolicies = MatchPoliciesEnum.ALL;
        List<String> requiredFields = Collections.emptyList();
        Map<String, String> fileTypeMap = Collections.emptyMap();

        // Act
        Void result = customerGroupService.saveData(itemMap, errors, response, tenantId,
                encryptFields, matchPolicies, requiredFields, fileTypeMap);

        // Assert
        assertNull(result);
        assertEquals(0, response.getFailCount().get());
        assertEquals(0, response.getSuccessCount().get());
        verifyNoInteractions(dorisService, kafkaTemplate, tableRecordCommonService);
    }

    @Test
    void testSaveData_WhenMatchFound_ShouldUpdateFieldAndSendKafkaMessage() {
        ReflectionTestUtils.setField(customerGroupService, "topic", "deep_sight_data_sync");
        // Arrange
        Map<String, Object> itemMap = new HashMap<>();
        itemMap.put("user_id", "123");
        itemMap.put("name", "test");

        List<Map<String, Object>> errors = new ArrayList<>();
        CreateCustomerImportResponse response = new CreateCustomerImportResponse();
        response.setCustomerGroupId(1L);
        response.setInterNames(Set.of("name"));

        String tenantId = "testTenant";
        Map<String, String> encryptFields = Collections.emptyMap();
        MatchPoliciesEnum matchPolicies = MatchPoliciesEnum.ALL;
        List<String> requiredFields = Collections.emptyList();
        Map<String, String> fileTypeMap = Collections.emptyMap();

        Map<String, Object> encryptedItem = new HashMap<>(itemMap);
        encryptedItem.put("name", "encryptedName");

        Map<String, Object> dbResult = new HashMap<>();
        dbResult.put("user_id", "123");
        dbResult.put("name", "test");

        when(tableRecordCommonService.encryptItem(any(), any())).thenReturn(encryptedItem);
        when(dorisService.selectList(anyString())).thenReturn(List.of(dbResult));

        // Act
        Void result = customerGroupService.saveData(itemMap, errors, response, tenantId,
                encryptFields, matchPolicies, requiredFields, fileTypeMap);

        // Assert
        assertNull(result);
        assertEquals(1, response.getSuccessCount().get());
        assertEquals(0, response.getFailCount().get());
        verify(tableRecordCommonService).encryptItem(any(), any());
        verify(dorisService).selectList(anyString());
        verify(kafkaTemplate).send(eq("deep_sight_data_sync"), anyString());
    }

    @Test
    void testSaveData_WhenNoMatchAndPolicyAll_ShouldCreateNewRecord() {
        ReflectionTestUtils.setField(customerGroupService, "topic", "deep_sight_data_sync");
        // Arrange
        Map<String, Object> itemMap = new HashMap<>();
        itemMap.put("name", "test");

        List<Map<String, Object>> errors = new ArrayList<>();
        CreateCustomerImportResponse response = new CreateCustomerImportResponse();
        response.setCustomerGroupId(1L);
        response.setInterNames(Set.of("name"));

        String tenantId = "testTenant";
        Map<String, String> encryptFields = Collections.emptyMap();
        MatchPoliciesEnum matchPolicies = MatchPoliciesEnum.ALL;
        List<String> requiredFields = Collections.emptyList();
        Map<String, String> fileTypeMap = Collections.emptyMap();

        Map<String, Object> encryptedItem = new HashMap<>(itemMap);
        encryptedItem.put("name", "encryptedName");

        when(tableRecordCommonService.encryptItem(any(), any())).thenReturn(encryptedItem);
        when(dorisService.selectList(anyString())).thenReturn(Collections.emptyList());
        when(tableRecordCommonService.recordCheck(any(), any(), any(), any())).thenReturn(Collections.emptyList());

        // Act
        Void result = customerGroupService.saveData(itemMap, errors, response, tenantId,
                encryptFields, matchPolicies, requiredFields, fileTypeMap);

        // Assert
        assertNull(result);
        assertEquals(1, response.getSuccessCount().get());
        assertEquals(0, response.getFailCount().get());
        verify(tableRecordCommonService, times(2)).encryptItem(any(), any());
        verify(dorisService).selectList(anyString());
        verify(tableRecordCommonService).recordCheck(any(), any(), any(), any());
        verify(kafkaTemplate).send(eq("deep_sight_data_sync"), anyString());
        assertNotNull(itemMap.get("user_id"));
    }

    @Test
    void testSaveData_WhenParamsErrorException_ShouldHandleError() {
        // Arrange
        Map<String, Object> itemMap = new HashMap<>();
        itemMap.put("name", "test");

        List<Map<String, Object>> errors = new ArrayList<>();
        CreateCustomerImportResponse response = new CreateCustomerImportResponse();
        response.setCustomerGroupId(1L);
        response.setInterNames(Set.of("name"));

        String tenantId = "testTenant";
        Map<String, String> encryptFields = Collections.emptyMap();
        MatchPoliciesEnum matchPolicies = MatchPoliciesEnum.ALL;
        List<String> requiredFields = Collections.emptyList();
        Map<String, String> fileTypeMap = Collections.emptyMap();

        when(tableRecordCommonService.recordCheck(any(), any(), any(), any()))
                .thenThrow(new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "Invalid params"));

        // Act
        Void result = customerGroupService.saveData(itemMap, errors, response, tenantId,
                encryptFields, matchPolicies, requiredFields, fileTypeMap);

        // Assert
        assertNull(result);
        assertEquals(1, response.getFailCount().get());
        assertEquals(0, response.getSuccessCount().get());
        assertEquals(1, errors.size());
        assertEquals("Invalid params", errors.get(0).get("deeepSightErrorMsg"));
    }

    @Test
    void testSaveData_WhenGeneralException_ShouldHandleError() {
        // Arrange
        Map<String, Object> itemMap = new HashMap<>();
        itemMap.put("name", "test");

        List<Map<String, Object>> errors = new ArrayList<>();
        CreateCustomerImportResponse response = new CreateCustomerImportResponse();
        response.setCustomerGroupId(1L);
        response.setInterNames(Set.of("name"));

        String tenantId = "testTenant";
        Map<String, String> encryptFields = Collections.emptyMap();
        MatchPoliciesEnum matchPolicies = MatchPoliciesEnum.ALL;
        List<String> requiredFields = Collections.emptyList();
        Map<String, String> fileTypeMap = Collections.emptyMap();

        when(tableRecordCommonService.encryptItem(any(), any()))
                .thenThrow(new RuntimeException("Unexpected error"));

        // Act
        Void result = customerGroupService.saveData(itemMap, errors, response, tenantId,
                encryptFields, matchPolicies, requiredFields, fileTypeMap);

        // Assert
        assertNull(result);
        assertEquals(1, response.getFailCount().get());
        assertEquals(0, response.getSuccessCount().get());
        assertEquals(1, errors.size());
    }

    @Test
    public void initService() {
        ReflectionTestUtils.setField(customerGroupService, "ignoreMatchFields", "user_id,user_name");
        customerGroupService.initService();
        Object ignoreField = ReflectionTestUtils.getField(customerGroupService, "ignoreField");
        assertEquals(ignoreField, Set.of("user_id", "user_name"));
    }

    @Test
    void getUserCount_shouldReturnCountWhenQuerySuccess() {
        // Arrange
        String tenantId = "testTenant";
        Long groupId = 100L;
        String expectedTableName = "mock_table";
        String expectedFieldName = "process_customer_100";
        String expectedSql = String.format("SELECT COUNT(*) FROM %s WHERE %s = '1'", expectedTableName, expectedFieldName);
        Long expectedCount = 50L;

        try (var tenantUtilsMock = mockStatic(TenantUtils.class)) {
            tenantUtilsMock.when(() -> TenantUtils.generateMockUserTableName(tenantId))
                    .thenReturn(expectedTableName);

            when(dorisService.getCount(expectedSql)).thenReturn(expectedCount);

            // Act
            Long actualCount = customerGroupService.getUserCount(tenantId, groupId);

            // Assert
            assertEquals(expectedCount, actualCount);
            verify(dorisService).getCount(expectedSql);
        }
    }

    @Test
    void getUserCount_shouldReturnZeroWhenExceptionOccurs() {
        // Arrange
        String tenantId = "testTenant";
        Long groupId = 100L;
        String expectedTableName = "mock_table";
        String expectedFieldName = "process_customer_100";
        String expectedSql = String.format("SELECT COUNT(*) FROM %s WHERE %s = '1'", expectedTableName, expectedFieldName);

        try (var tenantUtilsMock = mockStatic(TenantUtils.class)) {
            tenantUtilsMock.when(() -> TenantUtils.generateMockUserTableName(tenantId))
                    .thenReturn(expectedTableName);

            when(dorisService.getCount(expectedSql)).thenThrow(new RuntimeException("DB error"));

            // Act
            Long actualCount = customerGroupService.getUserCount(tenantId, groupId);

            // Assert
            assertEquals(0L, actualCount);
            verify(dorisService).getCount(expectedSql);
        }
    }

    @Test
    void getUserCount_shouldHandleEmptyGroupIds() {
        // Arrange
        String tenantId = "testTenant";
        Set<Long> groupIds = Collections.emptySet();
        long expectedCount = 0L;

        // Act
        Long actualCount = customerGroupService.getUserCount(tenantId, groupIds);

        // Assert
        assertEquals(expectedCount, actualCount);
        verify(dorisService, never()).getCount(anyString());
    }

    @Test
    void getUserCount_shouldReturnCountWhenSuccess() {
        // Arrange
        String tenantId = "testTenant";
        Set<Long> groupIds = Set.of(1L, 2L, 3L);
        String expectedTableName = "mock_user_table_testTenant";
        String expectedSql = "SELECT COUNT(1) FROM mock_user_table_testTenant WHERE process_customer_1 = '1' OR process_customer_2 = '1' OR process_customer_3 = '1'";
        long expectedCount = 10L;

        try (MockedStatic<TenantUtils> tenantUtilsMock = Mockito.mockStatic(TenantUtils.class)) {
            tenantUtilsMock.when(() -> TenantUtils.generateMockUserTableName(tenantId))
                    .thenReturn(expectedTableName);

            when(dorisService.getCount(anyString())).thenReturn(expectedCount);

            // Act
            Long actualCount = customerGroupService.getUserCount(tenantId, groupIds);

            // Assert
            assertEquals(expectedCount, actualCount);
            verify(dorisService, times(1)).getCount(anyString());
        }
    }

    @Test
    void getUserCount_shouldReturnZeroWhenExceptionOccurs1() {
        // Arrange
        String tenantId = "testTenant";
        Set<Long> groupIds = Set.of(1L, 2L, 3L);
        String expectedTableName = "mock_user_table_testTenant";
        String expectedSql = "SELECT COUNT(1) FROM mock_user_table_testTenant WHERE process_customer_1 = '1' OR process_customer_2 = '1' OR process_customer_3 = '1'";

        try (MockedStatic<TenantUtils> tenantUtilsMock = Mockito.mockStatic(TenantUtils.class)) {
            tenantUtilsMock.when(() -> TenantUtils.generateMockUserTableName(tenantId))
                    .thenReturn(expectedTableName);

            when(dorisService.getCount(anyString())).thenThrow(new RuntimeException("DB error"));

            // Act
            Long actualCount = customerGroupService.getUserCount(tenantId, groupIds);

            // Assert
            assertEquals(0L, actualCount);
            verify(dorisService, times(1)).getCount(anyString());
        }
    }
}