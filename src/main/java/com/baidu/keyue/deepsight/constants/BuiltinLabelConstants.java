package com.baidu.keyue.deepsight.constants;

/**
 * 内置标签常量定义
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
public final class BuiltinLabelConstants {

    private BuiltinLabelConstants() {
        // 工具类，禁止实例化
    }

    /**
     * 陌生号码接通意愿标签值常量
     */
    public static final class ConnectWillingness {
        public static final String HIGH_DEFENSE = "高度防御型";
        public static final String HIGH_DEFENSE_DESC = "接通率≤20%，对陌生号码高度防御";
        public static final String HIGH_DEFENSE_GROUP = "高度防御型条件组";
        public static final String THRESHOLD_HIGH_DEFENSE = "0.2";

        public static final String CAUTIOUS = "谨慎筛选型";
        public static final String CAUTIOUS_DESC = "接通率21%-50%，对陌生号码谨慎筛选";
        public static final String CAUTIOUS_GROUP = "谨慎筛选型条件组";
        public static final String THRESHOLD_CAUTIOUS_MIN = "0.2";
        public static final String THRESHOLD_CAUTIOUS_MAX = "0.5";

        public static final String OPEN = "开放型";
        public static final String OPEN_DESC = "接通率>50%，对陌生号码较为开放";
        public static final String OPEN_GROUP = "开放型条件组";
        public static final String THRESHOLD_OPEN = "0.5";
    }

    /**
     * 陌拜开拓接受度标签值常量
     */
    public static final class ColdCallAcceptance {
        public static final String HIGH_ACCEPTANCE = "高接受度";
        public static final String HIGH_ACCEPTANCE_DESC = "首轮挂断率≤15%，对陌拜开拓接受度高";
        public static final String HIGH_ACCEPTANCE_GROUP = "高接受度条件组";
        public static final String THRESHOLD_HIGH_ACCEPTANCE = "0.15";

        public static final String NORMAL_ACCEPTANCE = "一般接受度";
        public static final String NORMAL_ACCEPTANCE_DESC = "首轮挂断率16%-50%，对陌拜开拓接受度一般";
        public static final String NORMAL_ACCEPTANCE_GROUP = "一般接受度条件组";
        public static final String THRESHOLD_NORMAL_MIN = "0.15";
        public static final String THRESHOLD_NORMAL_MAX = "0.5";

        public static final String REJECTION = "排斥型";
        public static final String REJECTION_DESC = "首轮挂断率>50%，对陌拜开拓排斥";
        public static final String REJECTION_GROUP = "排斥型条件组";
        public static final String THRESHOLD_REJECTION = "0.5";
    }

    /**
     * 平均对话轮次标签值常量
     */
    public static final class AvgConversationRounds {
        public static final String INVALID = "无效对话";
        public static final String INVALID_DESC = "平均对话轮次0-2轮，对话无效";
        public static final String INVALID_GROUP = "无效对话条件组";
        public static final String THRESHOLD_INVALID_MIN = "0";
        public static final String THRESHOLD_INVALID_MAX = "2";

        public static final String SHALLOW = "浅层交互";
        public static final String SHALLOW_DESC = "平均对话轮次3-6轮，浅层交互";
        public static final String SHALLOW_GROUP = "浅层交互条件组";
        public static final String THRESHOLD_SHALLOW_MIN = "3";
        public static final String THRESHOLD_SHALLOW_MAX = "6";

        public static final String DEEP = "深度沟通";
        public static final String DEEP_DESC = "平均对话轮次7-10轮，深度沟通";
        public static final String DEEP_GROUP = "深度沟通条件组";
        public static final String THRESHOLD_DEEP_MIN = "7";
        public static final String THRESHOLD_DEEP_MAX = "10";

        public static final String HIGH_VALUE = "高价值客户";
        public static final String HIGH_VALUE_DESC = "平均对话轮次>10轮，高价值客户";
        public static final String HIGH_VALUE_GROUP = "高价值客户条件组";
        public static final String THRESHOLD_HIGH_VALUE = "10";
    }


    /**
     * 任务描述常量
     */
    public static final class TaskDescriptions {
        public static final String BUILTIN_LABEL_TASK = "内置标签计算任务";
    }

    /**
     * 目录层级常量
     */
    public static final class CatalogHierarchy {
        /** 根目录ID，表示一级目录的父ID */
        public static final Long ROOT_CATALOG_ID = 0L;

        /** 一级目录层级 */
        public static final int PRIMARY_LEVEL = 1;

        /** 二级目录层级 */
        public static final int SECONDARY_LEVEL = 2;
    }

    /**
     * 用户指标表字段常量
     * user_metric表是Doris聚合表，不在MySQL的datatable_info中注册
     */
    public static final class UserMetricFields {
        /** 用户ID字段 */
        public static final String ONE_ID = "oneId";

        /** 接通率字段 */
        public static final String CONNECT_RATE = "connect_rate";

        /** 时间段统计字段 */
        public static final String TIME_BUCKET_STATISTICS = "time_bucket_statistics";

        /** 首轮挂断率字段 */
        public static final String FIRST_ROUND_HANGUP_RATE = "first_round_hangup_rate";

        /** 平均对话轮次字段 */
        public static final String AVG_ROUNDS = "avg_rounds";

        /** 平均通话时长字段 */
        public static final String AVG_DURATION = "avg_duration";
    }

    /**
     * 用户指标表字段描述常量
     */
    public static final class UserMetricFieldDescs {
        /** 接通率字段描述 */
        public static final String CONNECT_RATE_DESC = "接通率";

        /** 首轮挂断率字段描述 */
        public static final String FIRST_ROUND_HANGUP_RATE_DESC = "首轮挂断率";

        /** 平均对话轮次字段描述 */
        public static final String AVG_ROUNDS_DESC = "平均对话轮次";

    }

    /**
     * 用户指标表字段类型常量
     */
    public static final class UserMetricFieldTypes {
        /** 数值类型字段（用于LabelField.fieldType） */
        public static final String DOUBLE = "double";
    }

    /**
     * 系统用户相关常量
     */
    public static final class SystemUser {
        /** 系统用户ID */
        public static final Long SYSTEM_USER_ID = -1L;
    }

    /**
     * 标签值枚举，用于类型安全的标签值管理
     */
    public enum LabelValueEnum {
        // 陌生号码接通意愿
        HIGH_DEFENSE(ConnectWillingness.HIGH_DEFENSE, ConnectWillingness.HIGH_DEFENSE_DESC),
        CAUTIOUS(ConnectWillingness.CAUTIOUS, ConnectWillingness.CAUTIOUS_DESC),
        OPEN(ConnectWillingness.OPEN, ConnectWillingness.OPEN_DESC),

        // 陌拜开拓接受度
        HIGH_ACCEPTANCE(ColdCallAcceptance.HIGH_ACCEPTANCE, ColdCallAcceptance.HIGH_ACCEPTANCE_DESC),
        NORMAL_ACCEPTANCE(ColdCallAcceptance.NORMAL_ACCEPTANCE, ColdCallAcceptance.NORMAL_ACCEPTANCE_DESC),
        REJECTION(ColdCallAcceptance.REJECTION, ColdCallAcceptance.REJECTION_DESC),

        // 平均对话轮次
        INVALID(AvgConversationRounds.INVALID, AvgConversationRounds.INVALID_DESC),
        SHALLOW(AvgConversationRounds.SHALLOW, AvgConversationRounds.SHALLOW_DESC),
        DEEP(AvgConversationRounds.DEEP, AvgConversationRounds.DEEP_DESC),
        HIGH_VALUE(AvgConversationRounds.HIGH_VALUE, AvgConversationRounds.HIGH_VALUE_DESC);

        private final String value;
        private final String description;

        LabelValueEnum(String value, String description) {
            this.value = value;
            this.description = description;
        }

        public String getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }
}
