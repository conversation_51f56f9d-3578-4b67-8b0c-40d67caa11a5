package com.baidu.keyue.deepsight.service.diffusion.impl;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.database.doris.DorisConfiguration;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.LogicEnum;
import com.baidu.keyue.deepsight.enums.OperationModeEnum;
import com.baidu.keyue.deepsight.enums.RuleTypeEnum;
import com.baidu.keyue.deepsight.enums.UserFiledEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.diffusion.DiffusionCalculateContext;
import com.baidu.keyue.deepsight.models.diffusion.dto.CharacteristicDistributionContractDTO;
import com.baidu.keyue.deepsight.models.diffusion.dto.DiffusionCharacteristicDTO;
import com.baidu.keyue.deepsight.models.diffusion.dto.GradingDistributionDTO;
import com.baidu.keyue.deepsight.models.diffusion.result.request.CharacteristicAnalyseRequest;
import com.baidu.keyue.deepsight.models.diffusion.result.request.CustomerDiffusionContentListRequest;
import com.baidu.keyue.deepsight.models.diffusion.result.request.DeleteDiffusionResultRequest;
import com.baidu.keyue.deepsight.models.diffusion.result.request.SamplingContractRequest;
import com.baidu.keyue.deepsight.models.diffusion.result.response.CharacteristicAnalyseResponse;
import com.baidu.keyue.deepsight.models.diffusion.result.response.SamplingContractResponse;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.rules.dto.DqlParseResult;
import com.baidu.keyue.deepsight.models.rules.dto.RuleFilter;
import com.baidu.keyue.deepsight.models.rules.dto.RuleGroup;
import com.baidu.keyue.deepsight.models.rules.dto.RuleNode;
import com.baidu.keyue.deepsight.models.rules.response.DatasetPropertiesResult;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTask;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.mapper.CustomerDiffusionTaskMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TaskSchedulerMapper;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.datamanage.impl.DorisConfServiceImpl;
import com.baidu.keyue.deepsight.service.diffusion.DiffusionResultService;
import com.baidu.keyue.deepsight.service.operation.OperationService;
import com.baidu.keyue.deepsight.service.rules.RuleManagerService;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.TableNameUtil;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @className: DiffusionResultServiceImpl
 * @description: 扩散结果服务实现
 * @author: wangzhongcheng
 * @date: 2025/3/25 17:15
 */
@Slf4j
@Service
public class DiffusionResultServiceImpl implements DiffusionResultService {

    @Autowired
    private DataTableManageService dataTableManageService;

    @Autowired
    private RuleManagerService ruleManagerService;

    @Autowired
    private DorisConfServiceImpl dorisConfServiceImpl;

    @Autowired
    private DorisService dorisService;

    @Autowired
    private DorisConfiguration dorisConfiguration;

    @Autowired
    private DataTableInfoMapper dataTableInfoMapper;

    @Autowired
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @Autowired
    private TaskSchedulerMapper taskSchedulerMapper;

    @Autowired
    private CustomerDiffusionTaskMapper diffusionTaskMapper;

    @Autowired
    private OperationService operationService;

    @Override
    public List<VisibleFieldResponse> getVisibleFields(Long customerDiffusionTaskId) {
        checkTenantPermission(customerDiffusionTaskId);
        List<VisibleFieldResponse> result = new ArrayList<>();
        // 获取用户表信息
        DataTableInfo userDataTableInfo = getUserDataTableInfo();
        List<VisibleFieldResponse> visibleFields = dataTableManageService.getVisibleFields(userDataTableInfo.getId(), false);
        // oneId 需要放在第一列
        List<VisibleFieldResponse> oneIdField = visibleFields.stream()
                .filter(field -> Objects.equals(field.getEnName(), Constants.TABLE_USER_ONE_ID))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(oneIdField)) {
            oneIdField.add(VisibleFieldResponse.convertFrom(Constants.TABLE_USER_ONE_ID_CNAME, Constants.TABLE_USER_ONE_ID, true));
        }
        List<VisibleFieldResponse> nonOneIdField = visibleFields.stream()
                .filter(field -> !Objects.equals(field.getEnName(), Constants.TABLE_USER_ONE_ID))
                .toList();
        result.addAll(oneIdField);
        result.addAll(nonOneIdField);

        // 追加得分字段 - 得分字段放在最后
        result.add(VisibleFieldResponse.convertFrom(Constants.DORIS_SCORE_FIELD_NAME, Constants.DORIS_SCORE_FIELD_NAME, true));
        return result;
    }

    @Override
    public List<DatasetPropertiesResult> getFilterFields(Long customerDiffusionTaskId) {
        checkTenantPermission(customerDiffusionTaskId);
        // 获取用户表字段信息
        DataTableInfo userDataTableInfo = getUserDataTableInfo();
        Long userTableId = userDataTableInfo.getId();
        List<DatasetPropertiesResult> filterFields = dataTableManageService.getTableContentFilterProperties(userTableId, false);
        List<DatasetPropertiesResult> result = new ArrayList<>(filterFields);

        // 添加得分字段
        TableFieldMetaInfo scoreFieldMetaInfo = getScoreFieldMetaInfo(customerDiffusionTaskId);
        if  (Objects.nonNull(scoreFieldMetaInfo)) {
            result.add(DatasetPropertiesResult.convertFrom(scoreFieldMetaInfo));
        }
        return result;
    }

    @Override
    public BasePageResponse.Page<Map<String, String>> contentList(CustomerDiffusionContentListRequest request) {
        checkTenantPermission(request.getCustomerDiffusionTaskId());
        String tenantId = WebContextHolder.getTenantId();
        Long diffusionTaskId = request.getCustomerDiffusionTaskId();
        CustomerDiffusionTask diffusionTask = checkCustomerDiffusionTask(diffusionTaskId);
        TaskSchedulerWithBLOBs taskScheduler = getTaskSchedulerForDiffusionTask(diffusionTask);
        if (Objects.isNull(taskScheduler)) {
            log.info("没有成功的任务，返回默认结果");
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), 0L, List.of());
        }

        DataTableInfo userDataTableInfo = getUserDataTableInfo();
        List<VisibleFieldResponse> visibleFields = getVisibleFields(diffusionTaskId);

        // 构建扩散人群结果查询sql对象
        DqlParseResult dqlParseResult = buildContentSql(request, userDataTableInfo);
        String selectClause = visibleFields.stream()
                .map(field -> {
                    if (Constants.DORIS_SCORE_FIELD_NAME.equals(field.getEnName())) {
                        return String.format("`%s`.`%s`",
                                TableNameUtil.generateDiffusionTemporaryTableName(diffusionTaskId),
                                field.getEnName());
                    }
                    return String.format("`%s`.`%s`",
                            TenantUtils.generateMockUserTableName(tenantId),
                            field.getEnName());
                }).collect(Collectors.joining(Constants.SEPARATOR));
        dqlParseResult.setOrderBy(String.format("`%s`", Constants.DORIS_SCORE_FIELD_NAME));
        dqlParseResult.setAsc(false);
        dqlParseResult.setSelect(selectClause);

        // 查询扩散人群结果总数
        String countSql = dqlParseResult.parseCountSql();
        long total = dorisService.getCount(countSql);
        long offset = Math.max((request.getPageNo() - 1) * request.getPageSize(), 0);
        if (total == 0 || offset > total) {
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), total, List.of());
        }

        String sql = dqlParseResult.parseDorisSql();
        List<Map<String, Object>> list = dorisService.selectList(sql);
        List<Map<String, String>> results = list.stream()
                .map(tableContent -> {
                    Map<String, String> showData =
                            dorisConfServiceImpl.dorisDataConvertToShowData(userDataTableInfo.getId(), tableContent, visibleFields);
                    // 得分保留一位小数
                    if (showData.containsKey(Constants.DORIS_SCORE_FIELD_NAME)) {
                        showData.put(Constants.DORIS_SCORE_FIELD_NAME,
                                String.format("%.1f", Double.parseDouble(showData.get(Constants.DORIS_SCORE_FIELD_NAME))));
                    }
                    return showData;
                })
                .toList();
        return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), total, results);
    }

    @Override
    public void deleteDiffusionResult(DeleteDiffusionResultRequest request) {
        String oneId = request.getOneId();
        Long customerDiffusionTaskId = request.getCustomerDiffusionTaskId();
        checkTenantPermission(customerDiffusionTaskId);
        String temporaryTableName = TableNameUtil.generateDiffusionTemporaryTableName(customerDiffusionTaskId);
        String deleteOneDiffusionResultSql = String.format("DELETE FROM `%s` WHERE `oneId` = '%s'", temporaryTableName, StringEscapeUtils.escapeSql(oneId));
        log.info("删除扩散结果 SQL: {}", deleteOneDiffusionResultSql);
        dorisService.execSql(deleteOneDiffusionResultSql);
    }

    @Override
    public CharacteristicAnalyseResponse characteristicAnalyse(CharacteristicAnalyseRequest request) {
        checkTenantPermission(request.getCustomerDiffusionTaskId());
        CustomerDiffusionTask diffusionTask = checkCustomerDiffusionTask(request.getCustomerDiffusionTaskId());
        TaskSchedulerWithBLOBs taskScheduler = getTaskSchedulerForDiffusionTask(diffusionTask);
        if (Objects.isNull(taskScheduler)) {
            return CharacteristicAnalyseResponse.getDefaultResponse();
        }

        Long diffusionTaskId = diffusionTask.getId();
        Long schedulerTaskId = diffusionTask.getTaskId();
        if (StringUtils.isEmpty(taskScheduler.getBody())) {
            log.error("特征分析人群扩散任务调度 body 为空：diffusionTaskId: {}, taskId: {}", diffusionTaskId, schedulerTaskId);
            throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND, "人群扩散任务解析特征失败");
        }

        // 解析扩散计算上下文，上下文中包含人群特征重要度结果、扩散结果得分区间分布统计结果
        DiffusionCalculateContext diffusionCalculateContext =
                JsonUtils.toObjectWithoutException(taskScheduler.getBody(), DiffusionCalculateContext.class);
        if (Objects.isNull(diffusionCalculateContext) || CollectionUtils.isEmpty(diffusionCalculateContext.getFeatures())) {
            log.error("人群扩散任务调度 body 转json失败：diffusionTaskId: {}, body: {}", diffusionTaskId, taskScheduler.getBody());
            throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND, "人群扩散任务解析特征失败");
        }

        // 如果没有得分区间分布统计结果，则重新计算得分区间分布统计结果
        if (CollectionUtils.isEmpty(diffusionCalculateContext.getGradingDistributionList())) {
            List<GradingDistributionDTO> gradingStatistics = getDiffusionGradingStatistics(request.getCustomerDiffusionTaskId());
            diffusionCalculateContext.setGradingDistributionList(gradingStatistics);
            taskScheduler.setBody(JsonUtils.toJsonUnchecked(diffusionCalculateContext));
            taskSchedulerMapper.updateByPrimaryKeyWithBLOBs(taskScheduler);
        }

        return CharacteristicAnalyseResponse.convertFrom(diffusionCalculateContext);
    }

    @Override
    public SamplingContractResponse samplingCharacteristicContract(SamplingContractRequest request) {
        checkTenantPermission(request.getCustomerDiffusionTaskId());
        // 获取前top6的特征
        CustomerDiffusionTask diffusionTask = checkCustomerDiffusionTask(request.getCustomerDiffusionTaskId());
        TaskSchedulerWithBLOBs taskScheduler = getTaskSchedulerForDiffusionTask(diffusionTask);
        if (Objects.isNull(taskScheduler)) {
            return SamplingContractResponse.getDefaultResponse();
        }

        Long diffusionTaskId = diffusionTask.getId();
        Long schedulerTaskId = diffusionTask.getTaskId();
        if (StringUtils.isEmpty(taskScheduler.getBody())) {
            log.error("抽样对比人群扩散任务调度 body 为空：diffusionTaskId: {}, taskId: {}", diffusionTaskId, schedulerTaskId);
            throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND, "人群扩散任务解析特征失败");
        }

        // 解析扩散计算上下文，上下文中包含人群特征重要度结果、特征抽样对比结果
        DiffusionCalculateContext diffusionCalculateContext =
                JsonUtils.toObjectWithoutException(taskScheduler.getBody(), DiffusionCalculateContext.class);
        if (Objects.isNull(diffusionCalculateContext) || CollectionUtils.isEmpty(diffusionCalculateContext.getFeatures())) {
            log.error("人群扩散任务调度 body 转json失败：diffusionTaskId: {}, body: {}", diffusionTaskId, taskScheduler.getBody());
            throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND, "人群扩散任务解析特征失败");
        }

        // 如果没有特征抽样对比结果，则重新计算特征抽样对比结果
        if (CollectionUtils.isEmpty(diffusionCalculateContext.getContractList())) {
            List<CharacteristicDistributionContractDTO> contractDTOList =
                    getCharacteristicDistributionContract(diffusionTaskId, diffusionCalculateContext);
            diffusionCalculateContext.setContractList(contractDTOList);
            taskScheduler.setBody(JsonUtils.toJsonUnchecked(diffusionCalculateContext));
            taskSchedulerMapper.updateByPrimaryKeyWithBLOBs(taskScheduler);
        }

        return SamplingContractResponse.convertFrom(diffusionCalculateContext);
    }

    /**
     * 获取特征抽样对比结果
     *
     * @param diffusionTaskId 扩散任务 ID
     * @return 特征抽样对比结果
     */
    private List<CharacteristicDistributionContractDTO> getCharacteristicDistributionContract(Long diffusionTaskId,
                                                                                              DiffusionCalculateContext diffusionContext) {
        ArrayList<CharacteristicDistributionContractDTO> result = new ArrayList<>();
        // 按照 score 从大到小排序，至多取前6个特征
        String userTableName = TenantUtils.generateMockUserTableName(WebContextHolder.getTenantId());
        String diffusionTableName = TableNameUtil.generateDiffusionTemporaryTableName(diffusionTaskId);
        List<DiffusionCharacteristicDTO> features = new ArrayList<>(diffusionContext.getFeatures());
        Collections.sort(features);
        if (features.size() > Constants.CHARACTERISTIC_CONTRACT_COUNT) {
            features = features.subList(0, Constants.CHARACTERISTIC_CONTRACT_COUNT);
        }

        // 进行预测结果特征分析
        String predictContractSqlTemp = """
                WITH
                predict_tmp AS (
                    SELECT {3}, COUNT(*) AS 'predict_count'
                    FROM {0}
                    INNER JOIN {1}
                    ON {0}.{2} = {1}.{2}
                    GROUP BY {3}
                ),
                sample_tmp AS (
                    SELECT {3}, COUNT(*) AS 'sample_count'
                    FROM (SELECT {3} FROM {0} TABLESAMPLE({4} rows) LIMIT {4}) AS tmp
                    GROUP BY {3}
                )
                SELECT predict_tmp.{3}, predict_count, sample_count
                FROM predict_tmp
                LEFT JOIN sample_tmp
                ON predict_tmp.{3} = sample_tmp.{3}
                ORDER BY predict_count DESC
                LIMIT 10
                """;
        Long predictResultCount = countDiffusionResultCount(diffusionTaskId);
        OperationModeEnum operationModeEnum = operationService.detectTenantOperationMode(WebContextHolder.getTenantId());
        for (DiffusionCharacteristicDTO feature : features) {
            String attributeField = StringUtils.EMPTY;
            switch (operationModeEnum) {
                case OPERATION_BY_SELF:
                    attributeField = Constants.DIFFUSION_CHARACTERISTIC_WITH_SELF_OPERATION.get(feature.getAttribute());
                    break;
                case OPERATION_BY_BAIDU_OP:
                    UserFiledEnum userFiledEnum = UserFiledEnum.valueOfName(feature.getAttribute());
                    if (Objects.isNull(userFiledEnum)) {
                        log.error("特征字段不存在：feature: {}", feature.getAttribute());
                        throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND, "特征字段不存在");
                    }
                    attributeField = userFiledEnum.getMergeFiled();
                    break;
                default:
                    attributeField = feature.getAttribute();
            }
            String contractSql = MessageFormat.format(predictContractSqlTemp,
                    userTableName,
                    diffusionTableName,
                    Constants.TABLE_USER_ONE_ID,
                    attributeField,
                    predictResultCount);

            List<Map<String, Object>> dataList = dorisService.selectList(contractSql);
            result.add(CharacteristicDistributionContractDTO.convertFrom(feature.getAttribute(), attributeField , dataList));
        }

        return result;
    }

    /**
     * 获取人群扩散的预测人数
     */
    private Long countDiffusionResultCount(Long diffusionTaskId) {
        String diffusionResultTableName = TableNameUtil.generateDiffusionTemporaryTableName(diffusionTaskId);
        return dorisService.getSingleTableCount(diffusionResultTableName);
    }

    /**
     * 获取扩散计算得分区间分布统计结果
     *
     * @param customerDiffusionTaskId 扩散任务 ID
     * @return 扩散计算得分区间分布统计结果
     */
    private List<GradingDistributionDTO> getDiffusionGradingStatistics(Long customerDiffusionTaskId) {
        String diffusionResultTableName = TableNameUtil.generateDiffusionTemporaryTableName(customerDiffusionTaskId);
        List<GradingDistributionDTO> gradingDistributionDTOS =
                dorisService.diffusionGradingStatistics(dorisConfiguration.getDb(), diffusionResultTableName);
        // 前端展示时，将得分区间调整为 0.0-0.1 => 0-0.1 和 0.9-1.0 => 0.9-1
        if (CollectionUtils.isNotEmpty(gradingDistributionDTOS)) {
            gradingDistributionDTOS.get(0).setGrade("0.9-1");
            gradingDistributionDTOS.get(gradingDistributionDTOS.size() - 1).setGrade("0-0.1");
        }

        return gradingDistributionDTOS;
    }

    /**
     * 获取人群扩散任务调度
     *
     * @param diffusionTask 扩散任务
     * @return TaskSchedulerWithBLOBs 扩散任务调度
     */
    private TaskSchedulerWithBLOBs getTaskSchedulerForDiffusionTask(CustomerDiffusionTask diffusionTask) {
        TaskSchedulerCriteria taskSchedulerCriteria = new TaskSchedulerCriteria();
        taskSchedulerCriteria.createCriteria()
                .andTaskIdEqualTo(diffusionTask.getTaskId())
                .andStatusEqualTo(TaskExecStatusEnum.SUCCESS.getCode());
        taskSchedulerCriteria.setOrderByClause(Constants.ORDER_BY_CREATE_TIME_DESC);
        List<TaskSchedulerWithBLOBs> taskSchedulers = taskSchedulerMapper.selectByExampleWithBLOBs(taskSchedulerCriteria);
        if (CollectionUtils.isEmpty(taskSchedulers)) {
            log.info("成功的人群扩散任务调度未查到:diffusionTaskId: {}, taskId: {}", diffusionTask.getId(), diffusionTask.getTaskId());
            return null;
        }
        return taskSchedulers.get(0);
    }

    public TaskSchedulerWithBLOBs getLatestSuccessTaskSchedulerRecord(long customerDiffusionTaskId, long taskId) {
        TaskSchedulerCriteria taskSchedulerCriteria = new TaskSchedulerCriteria();
        taskSchedulerCriteria.createCriteria().andTaskIdEqualTo(taskId).andStatusEqualTo(TaskExecStatusEnum.SUCCESS.getCode());
        taskSchedulerCriteria.setOrderByClause("create_time desc limit 1");
        List<TaskSchedulerWithBLOBs> taskSchedulers = taskSchedulerMapper.selectByExampleWithBLOBs(taskSchedulerCriteria);
        if (CollectionUtils.isEmpty(taskSchedulers)) {
            log.warn("getLatestSuccessTaskSchedulerRecord got empty scheduler tasks, diffusionTaskId: {}, taskId: {} ",
                    customerDiffusionTaskId, taskId);
            return null;
        }
        return taskSchedulers.get(0);
    }

    /**
     * 检查人群扩散任务是否存在；如果任务不存在，则抛出异常；如果任务存在，则返回任务对象
     *
     * @param customerDiffusionTaskId 扩散任务 ID
     * @return 人群扩散任务对象
     */
    @Override
    public CustomerDiffusionTask checkCustomerDiffusionTask(Long customerDiffusionTaskId) {
        CustomerDiffusionTask diffusionTask = diffusionTaskMapper.selectByPrimaryKey(customerDiffusionTaskId);
        if (Objects.isNull(diffusionTask)) {
            log.error("人群扩散任务不存在：diffusionTaskId: {}", customerDiffusionTaskId);
            throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND, "人群扩散任务不存在");
        }
        return diffusionTask;
    }

    /**
     * 构建扩散人群结果查询 SQL 对象
     *
     * @param request           扩散人群结果查询请求
     * @param userDataTableInfo 用户表信息
     * @return DqlParseResult 扩散人群结果查询 SQL 对象
     */
    private DqlParseResult buildContentSql(CustomerDiffusionContentListRequest request, DataTableInfo userDataTableInfo) {
        int size = Math.abs(request.getPageSize());
        int offset = Math.max((request.getPageNo() - 1) * request.getPageSize(), 0);
        TableFieldMetaInfo scoreFieldMetaInfo = getScoreFieldMetaInfo(request.getCustomerDiffusionTaskId());
        if (Objects.isNull(scoreFieldMetaInfo)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND, "扩散结果得分字段不存在");
        }

        // 针对基础用户过滤条件组装 SQL
        List<RuleFilter> userInfoRuleFilters = request.getFilters().stream()
                .filter(filter -> !Objects.equals(filter.getFieldId(), scoreFieldMetaInfo.getId()))
                .toList();
        // 构造 ruleNode
        RuleNode userRuleNode = RuleNode.builder()
                .type(RuleTypeEnum.USER)
                .filters(userInfoRuleFilters)
                .dataTableId(userDataTableInfo.getId()).build();

        // 针对扩展结果过滤条件组装 SQL
        List<RuleFilter> diffusionResultRuleFilters = request.getFilters().stream()
                .filter(filter -> Objects.equals(filter.getFieldId(), scoreFieldMetaInfo.getId()))
                .toList();
        RuleNode diffusionRuleNode = RuleNode.builder()
                .type(RuleTypeEnum.DATASET)
                .filters(diffusionResultRuleFilters)
                .dataTableId(scoreFieldMetaInfo.getDataTableId()).build();
        RuleGroup ruleGroup = new RuleGroup();
        ruleGroup.setRelation(LogicEnum.AND);
        ruleGroup.setRuleNodes(List.of(userRuleNode, diffusionRuleNode));
        DqlParseResult dqlParseResult = ruleManagerService.innerJoin(ruleGroup);
        dqlParseResult.setOffset(offset);
        dqlParseResult.setSize(size);
        return dqlParseResult;
    }

    /**
     * 获取用户表信息
     */
    private DataTableInfo getUserDataTableInfo() {
        String tenantId = WebContextHolder.getTenantId();
        String userEnTableName = TenantUtils.generateMockUserTableName(tenantId);
        DataTableInfoCriteria dataTableInfoCriteria = new DataTableInfoCriteria();
        dataTableInfoCriteria.createCriteria().andTableNameEqualTo(userEnTableName).andTenantidEqualTo(tenantId);
        List<DataTableInfo> dataTableInfos = dataTableInfoMapper.selectByExample(dataTableInfoCriteria);
        if (CollectionUtils.isEmpty(dataTableInfos)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND, "用户表不存在");
        }
        return dataTableInfos.get(0);
    }

    /**
     * 获取得分字段元信息
     */
    private TableFieldMetaInfo getScoreFieldMetaInfo(Long customerDiffusionTaskId) {
        TableFieldMetaInfoCriteria scoreFieldCriteria = new TableFieldMetaInfoCriteria();
        String temporaryDorisTableName = TableNameUtil.generateDiffusionTemporaryTableName(customerDiffusionTaskId);
        scoreFieldCriteria.createCriteria()
                .andTableEnNameEqualTo(temporaryDorisTableName).andEnFieldEqualTo(Constants.DORIS_SCORE_FIELD_NAME);
        List<TableFieldMetaInfo> tableFieldMetaInfos = tableFieldMetaInfoMapper.selectByExample(scoreFieldCriteria);

        if (CollectionUtils.isEmpty(tableFieldMetaInfos)) {
            log.info("临时表元信息得分字段不存在，没有预测成功的任务");
            return null;
        }
        return tableFieldMetaInfos.get(0);
    }

    @Override
    public void updateDiffusionTaskCustomerGroupInfo(long customerDiffusionTaskId, long customerGroupId) {
        CustomerDiffusionTask diffusionTask = diffusionTaskMapper.selectByPrimaryKey(customerDiffusionTaskId);
        if (Objects.isNull(diffusionTask)) {
            return;
        }
        diffusionTask.setGroupPackage(true);
        diffusionTask.setCustomerGroupId(customerGroupId);
        diffusionTaskMapper.updateByPrimaryKey(diffusionTask);
    }

    /**
     * 判断是否租户越权
     */
    public void checkTenantPermission(Long customerDiffusionTaskId) {
        String tenantId = WebContextHolder.getTenantId();
        CustomerDiffusionTask diffusionTask = diffusionTaskMapper.selectByPrimaryKey(customerDiffusionTaskId);
        if (Objects.isNull(diffusionTask)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND, "扩散任务不存在");
        }
        if (!Objects.equals(diffusionTask.getTenantId(), tenantId)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND, "扩散任务不属于该租户");
        }
    }

}