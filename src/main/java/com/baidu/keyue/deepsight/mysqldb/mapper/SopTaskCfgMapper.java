package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.SopTaskCfg;
import com.baidu.keyue.deepsight.mysqldb.entity.SopTaskCfgCriteria;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SOP离线分析任务配置Mapper
 */
public interface SopTaskCfgMapper {

    /**
     * 根据条件统计记录数
     */
    long countByExample(SopTaskCfgCriteria example);

    /**
     * 根据条件删除记录
     */
    int deleteByExample(SopTaskCfgCriteria example);

    /**
     * 根据主键删除记录
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入记录
     */
    int insert(SopTaskCfg record);

    /**
     * 选择性插入记录
     */
    int insertSelective(SopTaskCfg record);

    /**
     * 根据条件查询记录
     */
    List<SopTaskCfg> selectByExample(SopTaskCfgCriteria example);

    /**
     * 根据主键查询记录
     */
    SopTaskCfg selectByPrimaryKey(Long id);

    /**
     * 根据条件选择性更新记录
     */
    int updateByExampleSelective(@Param("record") SopTaskCfg record, @Param("example") SopTaskCfgCriteria example);

    /**
     * 根据条件更新记录
     */
    int updateByExample(@Param("record") SopTaskCfg record, @Param("example") SopTaskCfgCriteria example);

    /**
     * 根据主键选择性更新记录
     */
    int updateByPrimaryKeySelective(SopTaskCfg record);

    /**
     * 根据主键更新记录
     */
    int updateByPrimaryKey(SopTaskCfg record);

    /**
     * 根据租户ID、任务ID、机器人ID和场景查询任务
     */
    SopTaskCfg selectByTenantAndTaskAndRobotAndScene(@Param("tenantId") String tenantId, 
                                                     @Param("taskId") String taskId, 
                                                     @Param("robotId") String robotId, 
                                                     @Param("robotScene") Byte robotScene);

    /**
     * 根据租户ID和状态查询任务列表
     */
    List<SopTaskCfg> selectByTenantAndStatus(@Param("tenantId") String tenantId, @Param("status") Byte status);
}