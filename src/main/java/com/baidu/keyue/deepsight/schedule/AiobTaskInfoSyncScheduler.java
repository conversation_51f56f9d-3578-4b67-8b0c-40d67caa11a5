package com.baidu.keyue.deepsight.schedule;

import com.baidu.keyue.deepsight.database.redis.RedisConfiguration;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.TenantInfoMapper;
import com.baidu.keyue.deepsight.schedule.task.AiobTaskInfoSyncAsyncTask;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * @ClassName AiobInfoSyncScheduler
 * @Description 外呼信息同步：号线状态同步、任务状态同步等
 * <AUTHOR>
 * @Date 2025/7/7 11:40 AM
 */
@Slf4j
@Component
public class AiobTaskInfoSyncScheduler {

    @Value("${switch.aiobTaskInfoSync:false}")
    public boolean aiobTaskInfoSync;

    @Resource
    private TenantInfoMapper tenantInfoMapper;

    @Autowired
    private RedisConfiguration redisConfiguration;

    @Resource
    private RedissonClient redisson;

    @Autowired
    private AiobTaskInfoSyncAsyncTask aiobTaskInfoSyncAsyncTask;

    public String generateSchedulerLockKey(String... keys) {
        return StringUtils.join(keys, "-");
    }

    @Scheduled(cron = "${cron.aiobTaskInfoSync:0 0 * * * ?}")
    public void syncTaskInfo() {
        if (!aiobTaskInfoSync) {
            log.info("aiobTaskInfoSync is disabled. quit...");
            return;
        }

        log.info("aiobTaskInfoSync task start");
        String lockKey = generateSchedulerLockKey(redisConfiguration.getPrefix(), "AIOB_TASK_STATUS_UPDATE", "Lock");
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("aiobTaskInfoSync get lock failed.");
            return;
        }


        List<TenantInfo> tenantInfos = tenantInfoMapper.selectByExample(new TenantInfoCriteria());
        if (CollectionUtils.isEmpty(tenantInfos)) {
            log.info("aiobTaskInfoSync got empty diffusionTask. quit...");
            return;
        }

        try {
            log.info("aiobTaskInfoSync tenantInfos: {}", JsonUtils.toJsonWithOutException(tenantInfos));
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            for (TenantInfo tenantInfo : tenantInfos) {
                futures.add(aiobTaskInfoSyncAsyncTask.processTenantInfo(tenantInfo));
            }

            // 等待所有异步任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            log.error("aiobTaskInfoSync error", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        log.info("aiobTaskInfoSync task finished.");
    }


}
