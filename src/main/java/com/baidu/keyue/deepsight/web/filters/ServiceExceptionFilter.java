package com.baidu.keyue.deepsight.web.filters;

import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.exception.ServiceException;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.http.fileupload.impl.FileSizeLimitExceededException;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.List;

/**
 * ExceptionFilter
 * 全局异常处理类
 * */
@Slf4j
@ControllerAdvice
public class ServiceExceptionFilter {

    /**
     * 服务流程中的异常抛出处理
     * */
    @ExceptionHandler(value = ServiceException.class)
    public ResponseEntity<BaseResponse<?>> handleDeepSightException(ServiceException e) {
        if (e.getErrorCode() == ErrorCode.INTERNAL_ERROR) {
            log.error("request_id: {}, DeepSightException: ", e.getRequestId(), e);
        } else {
            log.warn("request_id: {}, DeepSightException: ", e.getRequestId(), e);
        }
        BaseResponse<?> body = new BaseResponse<>();
        body.setCode(e.getErrorCode().getCode());
        body.setMessage(e.getMessage());
        body.setRequestId(e.getRequestId());
        return new ResponseEntity<>(body, HttpStatus.valueOf(e.getErrorCode().getHttpCode()));
    }

    /**
     * spring-boot validation 参数检查异常处理
     * */
    @ExceptionHandler
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<BaseResponse<?>> handleException(MethodArgumentNotValidException e) {
        String requestId = WebContextHolder.getRequestId();
        log.warn("request_id: {}, handleException: ", requestId, e);
        List<String> errorMessages = e.getBindingResult().getFieldErrors().stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage).toList();
        BaseResponse<?> body = new BaseResponse<>();
        body.setCode(ErrorCode.BAD_REQUEST.getCode());
        body.setMessage(StringUtils.join(errorMessages, ", "));
        body.setRequestId(requestId);
        return new ResponseEntity<>(body, HttpStatus.valueOf(ErrorCode.BAD_REQUEST.getHttpCode()));
    }

    /**
     * 处理上传文件超限校验异常
     * */
    @ExceptionHandler(value = FileSizeLimitExceededException.class)
    public ResponseEntity<BaseResponse<?>> handleFileSizeLimitExceededException(FileSizeLimitExceededException e) {
        String requestId = WebContextHolder.getRequestId();
        log.warn("request_id: {}, FileSizeLimitExceededException: ", e.getMessage(), e);
        String fileName = e.getFileName();
        long permittedSize = e.getPermittedSize() / 1024 / 1024;
        BaseResponse<String> baseResponse = new BaseResponse<>();
        baseResponse.setRequestId(requestId);
        baseResponse.setCode(ErrorCode.BUSY_REQUEST.getCode());
        baseResponse.setMessage(String.format("%s文件过大，最大允许%sM", fileName, permittedSize));
        return new ResponseEntity<>(baseResponse, HttpStatus.BAD_REQUEST);
    }
}
