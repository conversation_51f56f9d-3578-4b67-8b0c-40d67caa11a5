package com.baidu.keyue.deepsight.exception;

import com.baidu.keyue.deepsight.config.Constants;
import lombok.Getter;

/**
 * @className: ErrorCode
 * @description: 错误码枚举
 * @author: lvtao03
 * @date: 2024/12/24 14:24
 */
public enum ErrorCode {

    SUCCESS(200, "ok", "请求成功"),
    BUSY_REQUEST(400, "BusyRequest", " 频繁请求"),
    BAD_REQUEST(400, "BadRequest", "参数不存在"),
    FORBIDDEN_REQUEST(403, "Forbidden", "无权限"),
    NOT_FOUND(404, "NotFound", "资源不存在"),
    // 不能抛出500异常，否则影响前端页面告警
    INTERNAL_ERROR(499, "InternalError", "服务器异常"),

    ;

    /**
     * HTTP状态码
     */
    @Getter
    final Integer httpCode;

    /**
     * 业务错配码
     */
    final String code;

    /**
     * 错误码说明
     */
    @Getter
    final String message;


    ErrorCode(Integer httpCode, String code, String message) {
        this.httpCode = httpCode;
        this.code = code;
        this.message = message;
    }

    /**
     * 增加前缀进行业务的区分，洞察：deepsight_
     * */
    public String getCode() {
        return Constants.ERROR_CODE_PREFIX + code;
    }

}
