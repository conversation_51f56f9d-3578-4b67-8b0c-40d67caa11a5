package com.baidu.keyue.deepsight.utils;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import java.util.UUID;

/**
 * TraceId工具类
 * 用于生成和管理请求链路追踪ID
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
public class TraceIdUtils {

    /**
     * TraceId在MDC中的key
     */
    public static final String TRACE_ID_KEY = "traceId";
    
    /**
     * RequestId在MDC中的key（兼容现有代码）
     */
    public static final String REQUEST_ID_KEY = "requestId";
    
    /**
     * 用户ID在MDC中的key
     */
    public static final String USER_ID_KEY = "userId";
    
    /**
     * 租户ID在MDC中的key
     */
    public static final String TENANT_ID_KEY = "tenantId";
    
    /**
     * 请求URI在MDC中的key
     */
    public static final String REQUEST_URI_KEY = "requestUri";
    
    /**
     * 请求方法在MDC中的key
     */
    public static final String REQUEST_METHOD_KEY = "requestMethod";

    /**
     * 生成新的TraceId
     * 格式：timestamp-uuid(前8位)
     * 
     * @return 新的TraceId
     */
    public static String generateTraceId() {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        long timestamp = System.currentTimeMillis();
        return timestamp + "-" + uuid.substring(0, 8);
    }

    /**
     * 生成简短的TraceId
     * 格式：uuid前12位
     * 
     * @return 简短的TraceId
     */
    public static String generateShortTraceId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 12);
    }

    /**
     * 设置TraceId到MDC
     * 
     * @param traceId TraceId
     */
    public static void setTraceId(String traceId) {
        if (StringUtils.isNotBlank(traceId)) {
            MDC.put(TRACE_ID_KEY, traceId);
            // 兼容现有的requestId字段
            MDC.put(REQUEST_ID_KEY, traceId);
        }
    }

    /**
     * 获取当前TraceId
     * 
     * @return 当前TraceId，如果不存在则返回null
     */
    public static String getTraceId() {
        String traceId = MDC.get(TRACE_ID_KEY);
        if (StringUtils.isBlank(traceId)) {
            // 兼容现有的requestId字段
            traceId = MDC.get(REQUEST_ID_KEY);
        }
        return traceId;
    }

    /**
     * 获取或生成TraceId
     * 如果当前没有TraceId，则生成一个新的
     * 
     * @return TraceId
     */
    public static String getOrGenerateTraceId() {
        String traceId = getTraceId();
        if (StringUtils.isBlank(traceId)) {
            traceId = generateTraceId();
            setTraceId(traceId);
        }
        return traceId;
    }

    /**
     * 设置用户信息到MDC
     * 
     * @param userId 用户ID
     * @param tenantId 租户ID
     */
    public static void setUserInfo(String userId, String tenantId) {
        if (StringUtils.isNotBlank(userId)) {
            MDC.put(USER_ID_KEY, userId);
        }
        if (StringUtils.isNotBlank(tenantId)) {
            MDC.put(TENANT_ID_KEY, tenantId);
        }
    }

    /**
     * 设置请求信息到MDC
     * 
     * @param requestUri 请求URI
     * @param requestMethod 请求方法
     */
    public static void setRequestInfo(String requestUri, String requestMethod) {
        if (StringUtils.isNotBlank(requestUri)) {
            MDC.put(REQUEST_URI_KEY, requestUri);
        }
        if (StringUtils.isNotBlank(requestMethod)) {
            MDC.put(REQUEST_METHOD_KEY, requestMethod);
        }
    }

    /**
     * 清除MDC中的所有追踪信息
     */
    public static void clear() {
        MDC.remove(TRACE_ID_KEY);
        MDC.remove(REQUEST_ID_KEY);
        MDC.remove(USER_ID_KEY);
        MDC.remove(TENANT_ID_KEY);
        MDC.remove(REQUEST_URI_KEY);
        MDC.remove(REQUEST_METHOD_KEY);
    }

    /**
     * 清除MDC中的所有信息
     */
    public static void clearAll() {
        MDC.clear();
    }

    /**
     * 获取当前用户ID
     * 
     * @return 用户ID
     */
    public static String getUserId() {
        return MDC.get(USER_ID_KEY);
    }

    /**
     * 获取当前租户ID
     * 
     * @return 租户ID
     */
    public static String getTenantId() {
        return MDC.get(TENANT_ID_KEY);
    }

    /**
     * 获取当前请求URI
     * 
     * @return 请求URI
     */
    public static String getRequestUri() {
        return MDC.get(REQUEST_URI_KEY);
    }

    /**
     * 获取当前请求方法
     * 
     * @return 请求方法
     */
    public static String getRequestMethod() {
        return MDC.get(REQUEST_METHOD_KEY);
    }

    /**
     * 创建子TraceId
     * 用于异步任务或子请求
     * 
     * @param parentTraceId 父TraceId
     * @return 子TraceId
     */
    public static String createChildTraceId(String parentTraceId) {
        if (StringUtils.isBlank(parentTraceId)) {
            return generateTraceId();
        }
        
        String childSuffix = generateShortTraceId().substring(0, 4);
        return parentTraceId + "-" + childSuffix;
    }

    /**
     * 为异步任务设置TraceId
     * 
     * @param parentTraceId 父TraceId
     * @return 子TraceId
     */
    public static String setAsyncTraceId(String parentTraceId) {
        String childTraceId = createChildTraceId(parentTraceId);
        setTraceId(childTraceId);
        return childTraceId;
    }

    /**
     * 检查是否存在TraceId
     * 
     * @return true如果存在TraceId
     */
    public static boolean hasTraceId() {
        return StringUtils.isNotBlank(getTraceId());
    }

    /**
     * 获取MDC中的所有信息（用于调试）
     * 
     * @return MDC信息字符串
     */
    public static String getMDCInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("TraceId: ").append(getTraceId()).append(", ");
        sb.append("UserId: ").append(getUserId()).append(", ");
        sb.append("TenantId: ").append(getTenantId()).append(", ");
        sb.append("RequestUri: ").append(getRequestUri()).append(", ");
        sb.append("RequestMethod: ").append(getRequestMethod());
        return sb.toString();
    }
}
