package com.baidu.keyue.deepsight.utils;

import com.baidu.keyue.deepsight.constants.SqlConstants;
import org.apache.commons.lang3.StringUtils;

/**
 * SQL工具类
 * 提供SQL构建和处理的通用方法
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
public final class SqlUtils {

    private SqlUtils() {
        // 工具类，禁止实例化
    }

    /**
     * 构建动态目录查询机器人名称的SQL
     * @param tableName 表名
     * @return 构建好的SQL语句
     * <AUTHOR>
     */
    public static String buildQueryRobotNamesSQL(String tableName) {
        if (StringUtils.isBlank(tableName)) {
            throw new IllegalArgumentException("表名不能为空");
        }
        return String.format(SqlConstants.DynamicCatalog.QUERY_ROBOT_NAMES, tableName);
    }

    /**
     * 构建动态目录查询tagExtractInfo和robotName映射的SQL
     * @param tableName 表名
     * @return 构建好的SQL语句
     * <AUTHOR>
     */
    public static String buildQueryTagExtractWithRobotNameSQL(String tableName) {
        if (StringUtils.isBlank(tableName)) {
            throw new IllegalArgumentException("表名不能为空");
        }
        return String.format(SqlConstants.DynamicCatalog.QUERY_TAG_EXTRACT_WITH_ROBOT_NAME, tableName);
    }

    /**
     * 构建字段验证SQL
     * @param tableName 表名
     * @param fieldName 字段名
     * @param fieldType 字段类型（ARRAY, DATETIME, STRING）
     * @param whereClause WHERE子句（可选）
     * @return 构建好的SQL语句
     * <AUTHOR>
     */
    public static String buildFieldValidationSQL(String tableName, String fieldName, FieldValidationType fieldType, String whereClause) {
        if (StringUtils.isBlank(tableName) || StringUtils.isBlank(fieldName)) {
            throw new IllegalArgumentException("表名和字段名不能为空");
        }
        
        String safeWhereClause = whereClause != null ? whereClause : SqlConstants.CommonConditions.EMPTY_STRING;
        
        switch (fieldType) {
            case ARRAY:
                return String.format(SqlConstants.LabelFieldValidation.VALIDATE_ARRAY_FIELD, 
                    tableName, fieldName, fieldName, safeWhereClause);
            case DATETIME:
                return String.format(SqlConstants.LabelFieldValidation.VALIDATE_DATETIME_FIELD, 
                    tableName, fieldName, safeWhereClause);
            case STRING:
                return String.format(SqlConstants.LabelFieldValidation.VALIDATE_STRING_FIELD, 
                    tableName, fieldName, fieldName, safeWhereClause);
            default:
                throw new IllegalArgumentException("不支持的字段类型: " + fieldType);
        }
    }

    /**
     * 构建robotName条件的WHERE子句
     * @param robotName 机器人名称
     * @return WHERE子句，如果robotName为空则返回空字符串
     * <AUTHOR>
     */
    public static String buildRobotNameWhereClause(String robotName) {
        if (StringUtils.isBlank(robotName)) {
            return SqlConstants.CommonConditions.EMPTY_STRING;
        }
        String escapedRobotName = escapeSingleQuote(robotName.trim());
        return String.format(SqlConstants.CommonConditions.ROBOT_NAME_CONDITION, escapedRobotName);
    }

    /**
     * 转义SQL中的单引号
     * @param value 需要转义的值
     * @return 转义后的值
     * <AUTHOR>
     */
    public static String escapeSingleQuote(String value) {
        if (value == null) {
            return null;
        }
        return value.replace(SqlConstants.SqlEscape.SINGLE_QUOTE, SqlConstants.SqlEscape.SINGLE_QUOTE_ESCAPE);
    }

    /**
     * 判断字段是否为数组类型
     * @param fieldName 字段名
     * @return 是否为数组字段
     * <AUTHOR>
     */
    public static boolean isArrayField(String fieldName) {
        if (StringUtils.isBlank(fieldName)) {
            return false;
        }
        String lowerFieldName = fieldName.toLowerCase();
        for (String indicator : SqlConstants.FieldType.ARRAY_FIELD_INDICATORS) {
            if (fieldName.equals(indicator) || lowerFieldName.contains(indicator.toLowerCase())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断字段是否为日期时间类型
     * @param fieldName 字段名
     * @return 是否为日期时间字段
     * <AUTHOR>
     */
    public static boolean isDateTimeField(String fieldName) {
        if (StringUtils.isBlank(fieldName)) {
            return false;
        }
        String lowerFieldName = fieldName.toLowerCase();
        for (String indicator : SqlConstants.FieldType.DATETIME_FIELD_INDICATORS) {
            if (fieldName.equals(indicator) || lowerFieldName.contains(indicator.toLowerCase())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据字段名自动判断字段验证类型
     * @param fieldName 字段名
     * @return 字段验证类型
     * <AUTHOR>
     */
    public static FieldValidationType determineFieldValidationType(String fieldName) {
        if (isArrayField(fieldName)) {
            return FieldValidationType.ARRAY;
        } else if (isDateTimeField(fieldName)) {
            return FieldValidationType.DATETIME;
        } else {
            return FieldValidationType.STRING;
        }
    }

    /**
     * 字段验证类型枚举
     */
    public enum FieldValidationType {
        /** 数组字段 */
        ARRAY,
        /** 日期时间字段 */
        DATETIME,
        /** 字符串字段 */
        STRING
    }

    /**
     * 构建意向标签查询SQL（只查最新一条记录）
     * @param tableName 表名
     * @param robotName 机器人名称
     * @return 构建好的SQL语句
     * <AUTHOR>
     */
    public static String buildIntentionLabelLatestSQL(String tableName, String robotName) {
        if (StringUtils.isBlank(tableName)) {
            throw new IllegalArgumentException("表名不能为空");
        }
        if (StringUtils.isBlank(robotName)) {
            throw new IllegalArgumentException("机器人名称不能为空");
        }

        String escapedRobotName = escapeSingleQuote(robotName.trim());
        return String.format(SqlConstants.BuiltinLabelQuery.QUERY_INTENTION_LABEL_LATEST,
            tableName, escapedRobotName);
    }

    /**
     * 构建LLM标签查询SQL（只查最新一条记录）
     * @param tableName 表名
     * @param robotName 机器人名称
     * @param labelKey 标签key（JSON中key字段的值）
     * @return 构建好的SQL语句
     * <AUTHOR>
     */
    public static String buildLLMLabelLatestSQL(String tableName, String robotName, String labelKey) {
        if (StringUtils.isBlank(tableName)) {
            throw new IllegalArgumentException("表名不能为空");
        }
        if (StringUtils.isBlank(robotName)) {
            throw new IllegalArgumentException("机器人名称不能为空");
        }
        if (StringUtils.isBlank(labelKey)) {
            throw new IllegalArgumentException("标签key不能为空");
        }

        String escapedRobotName = escapeSingleQuote(robotName.trim());
        String escapedLabelKey = escapeSingleQuote(labelKey.trim());
        return String.format(SqlConstants.BuiltinLabelQuery.QUERY_LLM_LABEL_LATEST,
            tableName, escapedRobotName, escapedLabelKey);
    }

    /**
     * 构建风险评估时间数据查询SQL
     * @param tableName 表名
     * @return 构建好的SQL语句
     * <AUTHOR>
     */
    public static String buildRiskTimeDataSQL(String tableName) {
        if (StringUtils.isBlank(tableName)) {
            throw new IllegalArgumentException("表名不能为空");
        }
        return String.format(SqlConstants.BuiltinLabelQuery.QUERY_RISK_TIME_DATA, tableName);
    }

    /**
     * 构建风险评估内容数据查询SQL
     * @param tableName 表名
     * @return 构建好的SQL语句
     * <AUTHOR>
     */
    public static String buildRiskContentDataSQL(String tableName) {
        if (StringUtils.isBlank(tableName)) {
            throw new IllegalArgumentException("表名不能为空");
        }
        return String.format(SqlConstants.BuiltinLabelQuery.QUERY_RISK_CONTENT_DATA, tableName);
    }
}
