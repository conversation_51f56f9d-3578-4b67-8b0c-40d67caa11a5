package com.baidu.keyue.deepsight.schedule.task;

import com.baidu.keyue.deepsight.service.agg.AiobSessionMetricAggService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;


@Slf4j
@Service
public class MetricAggAsyncTask {

    @Autowired
    private AiobSessionMetricAggService aiobSessionMetricAggService;

    @Async("metricAggTaskExecutor")
    public CompletableFuture<Void> processTable(String table) {
        try {
            aiobSessionMetricAggService.aiobSessionMetricAggExec(table);
        } catch (Exception e) {
            log.error("aiobSessionMetricAgg exec task failed, table: {}", table, e);
        }
        log.info("aiobSessionMetricAgg exec task finished, table: {}", table);

        return CompletableFuture.completedFuture(null);
    }

}
