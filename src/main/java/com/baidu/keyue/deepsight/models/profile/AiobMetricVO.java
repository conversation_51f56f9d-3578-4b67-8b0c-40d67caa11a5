package com.baidu.keyue.deepsight.models.profile;

import com.baidu.keyue.deepsight.utils.NumberFormatUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 外呼行为指标VO类
 *
 * <AUTHOR>
 * @date 2025/8/12 17:34
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiobMetricVO {

    /**
     * 接通率
     */
    private Float connectRate;

    /**
     * 首轮挂断率
     */
    private Float firstRoundHangupRate;

    /**
     * 平均对话轮次（四舍五入取整）
     */
    private Long avgRounds;

    /**
     * 平均通话时长(s)
     */
    private Float avgDuration;

    /**
     * 最近一次呼叫距离今日天数
     */
    private Long daysSinceLastCall;

    /**
     * 精度控制
     */
    public void roundMetric() {
        // 接通率 - 保留两位小数
        if (this.connectRate != null) {
            this.connectRate = NumberFormatUtil.roundToFloat(this.connectRate, 2);
        }

        // 首轮挂断率 - 保留两位小数
        if (this.firstRoundHangupRate != null) {
            this.firstRoundHangupRate = NumberFormatUtil.roundToFloat(this.firstRoundHangupRate, 2);
        }

//        // 平均对话轮次 - 四舍五入取整
//        if (this.avgRounds != null) {
//            this.avgRounds = (float) NumberFormatUtil.roundToInt(this.avgRounds);
//        }

        // 平均通话时长（秒） - 保留两位小数
        if (this.avgDuration != null) {
            this.avgDuration = NumberFormatUtil.roundToFloat(this.avgDuration, 2);
        }
    }

    public void setAvgRounds(Number n) {
        this.avgRounds = NumberFormatUtil.roundToLong(n);
    }

}
