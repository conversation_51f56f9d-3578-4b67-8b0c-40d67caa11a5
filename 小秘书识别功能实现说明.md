# 小秘书识别功能实现说明

## 功能概述

本功能实现了对外呼通话记录的小秘书识别分析，通过分析通话内容和挂断原因，判断是否为小秘书接听，以提高外呼系统的呼通率。

## 实现方案

### 1. 数据库变更
已在v1.1.3版本完成数据库字段添加：
- `aiob_conversation_session_service_^&` 表新增 `is_auto_answer` 字段 (tinyint类型，1是，0否)
- `global_default_session` 表新增 `is_auto_answer` 字段

### 2. 识别条件
需要同时满足以下三个条件才会进行小秘书识别：

1. **接通且外呼机器人主动挂断**
   - `sipCode = "200"` (接通状态)
   - `isRobotHangup = true` (机器人主动挂断)

2. **挂断原因为命中挂断词**
   - 从record记录的nodeInfo中获取
   - `systemEvent = "hungUp"`
   - `hungUpType = 6` (对应HungUpTypeHungUpRegular)

3. **对话轮次大于等于2**
   - `talkingTurn >= 2`

### 3. 核心组件

#### AssistantRecognitionService
小秘书识别服务类，负责：
- 检查前置条件
- 调用算法接口
- 解析识别结果

#### AIOBConversationDataWorker
外呼数据消费者，在`convertAiobSession`方法中集成小秘书识别逻辑。

### 4. 算法接口集成

#### 接口信息
- **URL**: `http://*************:8097/getAssistantRecognition/v1`
- **方法**: POST
- **Content-Type**: application/json

#### 请求参数
```json
{
    "taskId": "xxxxxxxxx11122",
    "content": "AI: 喂，您好...\n用户: 我是通话助理。\n...",
    "sampling_params": {
        "temperature": 0.1,
        "top_p": 0.8,
        "max_tokens": 2048
    }
}
```

#### 响应格式
```json
{
    "message": "OK",
    "status": "success",
    "results": {
        "is_smart_assistant": true,
        "reason": "用户发言中出现了提示性语句..."
    }
}
```

### 5. 处理流程

```
Kafka消息(session) 
    ↓
processData() 
    ↓
convertAiobSession() 
    ↓
performAssistantRecognition()
    ↓
checkBasicConditions() (检查条件1和3)
    ↓
assistantRecognitionService.recognizeAssistant()
    ↓
checkPreconditions() (检查条件2)
    ↓
callAssistantRecognitionApi() (调用算法接口)
    ↓
设置 is_auto_answer 字段值
```

### 6. 配置项

在`application.yml`中添加：
```yaml
assistant:
  recognition:
    url: "http://*************:8097/getAssistantRecognition/v1"
    timeout: 5000  # 5秒超时
```

### 7. 容错处理

- **算法接口调用失败**: 返回null，默认设置为非小秘书(0)
- **数据库查询异常**: 记录错误日志，返回false
- **数据解析异常**: 记录错误日志，默认设置为非小秘书(0)
- **前置条件不满足**: 直接设置为非小秘书(0)，不调用算法接口

### 8. 日志记录

- 识别过程的关键步骤都有详细日志
- 算法接口调用成功/失败的记录
- 识别结果和原因的记录
- 异常情况的错误日志

### 9. 性能考虑

- **无重试机制**: 避免阻塞Kafka消息消费
- **超时控制**: 5秒超时，防止长时间等待
- **条件预检**: 先检查基本条件，减少不必要的数据库查询和API调用
- **异常快速失败**: 异常情况下快速返回默认值

### 10. 测试覆盖

创建了完整的单元测试，覆盖：
- 正常识别流程
- 前置条件不满足的情况
- 算法接口调用失败的情况
- 各种响应格式的解析
- 异常情况的处理

## 使用示例

### 消息示例
根据提供的消息示例，该通话记录满足所有三个条件：
- `sipCode: "200"`, `isRobotHangup: true` ✅
- `talkingTurn: 3` ✅  
- nodeInfo解码后包含 `systemEvent: "hungUp"`, `hungUpType: 6` ✅

因此会调用算法接口进行识别，并根据对话内容"我是通话助理"判断为小秘书接听。

### 结果设置
- 识别为小秘书: `is_auto_answer = 1`
- 识别为非小秘书: `is_auto_answer = 0`
- 识别失败或异常: `is_auto_answer = 0` (默认值)

## 部署说明

1. 确保算法接口服务可访问
2. 更新配置文件中的算法接口URL
3. 重启应用服务
4. 监控日志确认功能正常运行

## 监控指标

建议监控以下指标：
- 小秘书识别调用次数
- 算法接口成功率
- 识别结果分布(是/否/失败)
- 平均响应时间
