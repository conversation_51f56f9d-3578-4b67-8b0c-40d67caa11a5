package com.baidu.keyue.deepsight.schedule.task;

import com.baidu.keyue.deepsight.database.redis.RedisConfiguration;
import com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSourceWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.service.dataprediction.DataPredictionCalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;


@Slf4j
@Service
public class DataPredictionAsyncTask {

    @Autowired
    private RedisConfiguration redisConfiguration;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private DataPredictionCalService dataPredictionCalService;

    public String generateSchedulerLockKey(String... keys) {
        return StringUtils.join(keys, "-");
    }

    @Async("dataPredictionTaskExecutor")
    public CompletableFuture<Void> processPair(Pair<DataPredictionSourceWithBLOBs, TaskInfo> pair) {
        String lockKey = generateSchedulerLockKey(redisConfiguration.getPrefix(), "dataPredictionScheduler", "Lock",
                String.valueOf(pair.getLeft().getId()), String.valueOf(pair.getRight().getId()));
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("dataPredictionScheduler get lock failed, lockKey: {}", lockKey);
            return CompletableFuture.completedFuture(null);
        }

        try {
            dataPredictionCalService.taskExec(pair.getLeft(), pair.getRight());
        } catch (Exception e) {
            log.error("dataPredictionScheduler exec task failed, ", e);
        }
        if (lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
        return CompletableFuture.completedFuture(null);
    }

}
