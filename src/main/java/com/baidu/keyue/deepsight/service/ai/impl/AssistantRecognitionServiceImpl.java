package com.baidu.keyue.deepsight.service.ai.impl;

import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.service.ai.AssistantRecognitionService;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小秘书识别服务
 * <AUTHOR> Assistant
 * @date 2025-08-12
 */
@Service
@Slf4j
public class AssistantRecognitionServiceImpl implements AssistantRecognitionService {
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private DorisService dorisService;
    
    @Value("${assistant.recognition.url}")
    private String assistantRecognitionUrl;
    
    @Value("${assistant.recognition.timeout:5000}")
    private int timeout;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 小秘书识别主方法
     */
    public Boolean recognizeAssistant(String sessionId, String taskId, String conversationContent, String tenantId) {
        // 1. 检查前置条件

        if (!checkPreconditions(sessionId, tenantId)) {
            log.debug("小秘书识别前置条件不满足, sessionId: {}", sessionId);
            return false; // 不满足条件，不进行识别
        }
        
        // 2. 调用算法接口
        return callAssistantRecognitionApi(taskId, conversationContent);
    }
    
    /**
     * 检查前置条件（条件2：挂断原因）
     */
    private boolean checkPreconditions(String sessionId, String tenantId) {
        String recordTableName = TenantUtils.generateAiobRecordTableName(tenantId);
        // 使用专门的挂断条件查询SQL，在数据库层面过滤条件
        String sql = ORMUtils.generateQueryRecordNodeInfoForHangupSQL(recordTableName, sessionId);

        try {
            List<Map<String, Object>> records = dorisService.selectList(sql);
            boolean hasHangupRecord = !records.isEmpty();

            if (hasHangupRecord) {
                log.debug("找到符合挂断条件的record, sessionId: {}", sessionId);
            } else {
                log.debug("未找到符合挂断条件的record, sessionId: {}", sessionId);
            }

            return hasHangupRecord;
        } catch (Exception e) {
            log.error("查询record挂断条件失败, sessionId: {}", sessionId, e);
            return false;
        }
    }

    /**
     * 调用算法接口（无重试机制）
     */
    private Boolean callAssistantRecognitionApi(String taskId, String conversationContent) {
        try {
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("taskId", taskId);
            requestBody.put("content", conversationContent);
            
            // 可选参数
            Map<String, Object> samplingParams = new HashMap<>();
            samplingParams.put("temperature", 0.1);
            samplingParams.put("top_p", 0.8);
            samplingParams.put("max_tokens", 2048);
            requestBody.put("sampling_params", samplingParams);
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            log.info("调用小秘书识别算法, taskId: {}, url: {}", taskId, assistantRecognitionUrl);
            
            // 单次调用，无重试
            ResponseEntity<Map> response = restTemplate.postForEntity(assistantRecognitionUrl, entity, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Boolean result = parseRecognitionResult(response.getBody());
                log.info("小秘书识别算法调用成功, taskId: {}, result: {}", taskId, result);
                return result;
            }
            
            log.warn("算法接口调用失败，返回状态码: {}, taskId: {}", response.getStatusCode(), taskId);
            return null;
            
        } catch (Exception e) {
            log.error("小秘书识别算法调用失败, taskId: {}", taskId, e);
            return null; // 调用失败返回null
        }
    }
    
    /**
     * 解析算法返回结果
     */
    private Boolean parseRecognitionResult(Map<String, Object> response) {
        try {
            // 检查状态码
            String status = (String) response.get("status");
            if (!"success".equals(status)) {
                String message = (String) response.get("message");
                log.warn("算法接口返回失败状态: status={}, message={}", status, message);
                return null;
            }

            // 获取results对象
            Object resultsObj = response.get("results");
            if (!(resultsObj instanceof Map)) {
                log.warn("算法接口返回格式错误，results字段不是Map类型: {}", response);
                return null;
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> results = (Map<String, Object>) resultsObj;

            // 获取is_smart_assistant字段
            Object isSmartAssistant = results.get("is_smart_assistant");
            if (isSmartAssistant == null) {
                log.warn("算法接口返回结果中缺少is_smart_assistant字段: {}", response);
                return null;
            }

            // 解析布尔值
            Boolean result = null;
            if (isSmartAssistant instanceof Boolean) {
                result = (Boolean) isSmartAssistant;
            } else if (isSmartAssistant instanceof String) {
                String strValue = (String) isSmartAssistant;
                result = "true".equalsIgnoreCase(strValue) || "True".equals(strValue);
            } else {
                log.warn("is_smart_assistant字段类型不支持: {}, type: {}",
                    isSmartAssistant, isSmartAssistant.getClass().getSimpleName());
                return null;
            }

            // 记录识别原因（用于调试）
            String reason = (String) results.get("reason");
            log.info("小秘书识别结果: {}, 原因: {}", result, reason);

            return result;

        } catch (Exception e) {
            log.error("解析算法返回结果异常: {}", response, e);
            return null;
        }
    }
}
