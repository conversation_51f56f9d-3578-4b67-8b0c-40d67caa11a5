package com.baidu.keyue.deepsight.mysqldb.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class LabelCriteria {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table label
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table label
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table label
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    public LabelCriteria() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table label
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(String value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(String value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(String value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(String value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(String value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLike(String value) {
            addCriterion("user_id like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotLike(String value) {
            addCriterion("user_id not like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<String> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<String> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(String value1, String value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(String value1, String value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andCatalogIdIsNull() {
            addCriterion("catalog_id is null");
            return (Criteria) this;
        }

        public Criteria andCatalogIdIsNotNull() {
            addCriterion("catalog_id is not null");
            return (Criteria) this;
        }

        public Criteria andCatalogIdEqualTo(Long value) {
            addCriterion("catalog_id =", value, "catalogId");
            return (Criteria) this;
        }

        public Criteria andCatalogIdNotEqualTo(Long value) {
            addCriterion("catalog_id <>", value, "catalogId");
            return (Criteria) this;
        }

        public Criteria andCatalogIdGreaterThan(Long value) {
            addCriterion("catalog_id >", value, "catalogId");
            return (Criteria) this;
        }

        public Criteria andCatalogIdGreaterThanOrEqualTo(Long value) {
            addCriterion("catalog_id >=", value, "catalogId");
            return (Criteria) this;
        }

        public Criteria andCatalogIdLessThan(Long value) {
            addCriterion("catalog_id <", value, "catalogId");
            return (Criteria) this;
        }

        public Criteria andCatalogIdLessThanOrEqualTo(Long value) {
            addCriterion("catalog_id <=", value, "catalogId");
            return (Criteria) this;
        }

        public Criteria andCatalogIdIn(List<Long> values) {
            addCriterion("catalog_id in", values, "catalogId");
            return (Criteria) this;
        }

        public Criteria andCatalogIdNotIn(List<Long> values) {
            addCriterion("catalog_id not in", values, "catalogId");
            return (Criteria) this;
        }

        public Criteria andCatalogIdBetween(Long value1, Long value2) {
            addCriterion("catalog_id between", value1, value2, "catalogId");
            return (Criteria) this;
        }

        public Criteria andCatalogIdNotBetween(Long value1, Long value2) {
            addCriterion("catalog_id not between", value1, value2, "catalogId");
            return (Criteria) this;
        }

        public Criteria andLabelNameIsNull() {
            addCriterion("label_name is null");
            return (Criteria) this;
        }

        public Criteria andLabelNameIsNotNull() {
            addCriterion("label_name is not null");
            return (Criteria) this;
        }

        public Criteria andLabelNameEqualTo(String value) {
            addCriterion("label_name =", value, "labelName");
            return (Criteria) this;
        }

        public Criteria andLabelNameNotEqualTo(String value) {
            addCriterion("label_name <>", value, "labelName");
            return (Criteria) this;
        }

        public Criteria andLabelNameGreaterThan(String value) {
            addCriterion("label_name >", value, "labelName");
            return (Criteria) this;
        }

        public Criteria andLabelNameGreaterThanOrEqualTo(String value) {
            addCriterion("label_name >=", value, "labelName");
            return (Criteria) this;
        }

        public Criteria andLabelNameLessThan(String value) {
            addCriterion("label_name <", value, "labelName");
            return (Criteria) this;
        }

        public Criteria andLabelNameLessThanOrEqualTo(String value) {
            addCriterion("label_name <=", value, "labelName");
            return (Criteria) this;
        }

        public Criteria andLabelNameLike(String value) {
            addCriterion("label_name like", value, "labelName");
            return (Criteria) this;
        }

        public Criteria andLabelNameNotLike(String value) {
            addCriterion("label_name not like", value, "labelName");
            return (Criteria) this;
        }

        public Criteria andLabelNameIn(List<String> values) {
            addCriterion("label_name in", values, "labelName");
            return (Criteria) this;
        }

        public Criteria andLabelNameNotIn(List<String> values) {
            addCriterion("label_name not in", values, "labelName");
            return (Criteria) this;
        }

        public Criteria andLabelNameBetween(String value1, String value2) {
            addCriterion("label_name between", value1, value2, "labelName");
            return (Criteria) this;
        }

        public Criteria andLabelNameNotBetween(String value1, String value2) {
            addCriterion("label_name not between", value1, value2, "labelName");
            return (Criteria) this;
        }

        public Criteria andLabelValueUpdateModIsNull() {
            addCriterion("label_value_update_mod is null");
            return (Criteria) this;
        }

        public Criteria andLabelValueUpdateModIsNotNull() {
            addCriterion("label_value_update_mod is not null");
            return (Criteria) this;
        }

        public Criteria andLabelValueUpdateModEqualTo(Byte value) {
            addCriterion("label_value_update_mod =", value, "labelValueUpdateMod");
            return (Criteria) this;
        }

        public Criteria andLabelValueUpdateModNotEqualTo(Byte value) {
            addCriterion("label_value_update_mod <>", value, "labelValueUpdateMod");
            return (Criteria) this;
        }

        public Criteria andLabelValueUpdateModGreaterThan(Byte value) {
            addCriterion("label_value_update_mod >", value, "labelValueUpdateMod");
            return (Criteria) this;
        }

        public Criteria andLabelValueUpdateModGreaterThanOrEqualTo(Byte value) {
            addCriterion("label_value_update_mod >=", value, "labelValueUpdateMod");
            return (Criteria) this;
        }

        public Criteria andLabelValueUpdateModLessThan(Byte value) {
            addCriterion("label_value_update_mod <", value, "labelValueUpdateMod");
            return (Criteria) this;
        }

        public Criteria andLabelValueUpdateModLessThanOrEqualTo(Byte value) {
            addCriterion("label_value_update_mod <=", value, "labelValueUpdateMod");
            return (Criteria) this;
        }

        public Criteria andLabelValueUpdateModIn(List<Byte> values) {
            addCriterion("label_value_update_mod in", values, "labelValueUpdateMod");
            return (Criteria) this;
        }

        public Criteria andLabelValueUpdateModNotIn(List<Byte> values) {
            addCriterion("label_value_update_mod not in", values, "labelValueUpdateMod");
            return (Criteria) this;
        }

        public Criteria andLabelValueUpdateModBetween(Byte value1, Byte value2) {
            addCriterion("label_value_update_mod between", value1, value2, "labelValueUpdateMod");
            return (Criteria) this;
        }

        public Criteria andLabelValueUpdateModNotBetween(Byte value1, Byte value2) {
            addCriterion("label_value_update_mod not between", value1, value2, "labelValueUpdateMod");
            return (Criteria) this;
        }

        public Criteria andLabelValueSaveModIsNull() {
            addCriterion("label_value_save_mod is null");
            return (Criteria) this;
        }

        public Criteria andLabelValueSaveModIsNotNull() {
            addCriterion("label_value_save_mod is not null");
            return (Criteria) this;
        }

        public Criteria andLabelValueSaveModEqualTo(Byte value) {
            addCriterion("label_value_save_mod =", value, "labelValueSaveMod");
            return (Criteria) this;
        }

        public Criteria andLabelValueSaveModNotEqualTo(Byte value) {
            addCriterion("label_value_save_mod <>", value, "labelValueSaveMod");
            return (Criteria) this;
        }

        public Criteria andLabelValueSaveModGreaterThan(Byte value) {
            addCriterion("label_value_save_mod >", value, "labelValueSaveMod");
            return (Criteria) this;
        }

        public Criteria andLabelValueSaveModGreaterThanOrEqualTo(Byte value) {
            addCriterion("label_value_save_mod >=", value, "labelValueSaveMod");
            return (Criteria) this;
        }

        public Criteria andLabelValueSaveModLessThan(Byte value) {
            addCriterion("label_value_save_mod <", value, "labelValueSaveMod");
            return (Criteria) this;
        }

        public Criteria andLabelValueSaveModLessThanOrEqualTo(Byte value) {
            addCriterion("label_value_save_mod <=", value, "labelValueSaveMod");
            return (Criteria) this;
        }

        public Criteria andLabelValueSaveModIn(List<Byte> values) {
            addCriterion("label_value_save_mod in", values, "labelValueSaveMod");
            return (Criteria) this;
        }

        public Criteria andLabelValueSaveModNotIn(List<Byte> values) {
            addCriterion("label_value_save_mod not in", values, "labelValueSaveMod");
            return (Criteria) this;
        }

        public Criteria andLabelValueSaveModBetween(Byte value1, Byte value2) {
            addCriterion("label_value_save_mod between", value1, value2, "labelValueSaveMod");
            return (Criteria) this;
        }

        public Criteria andLabelValueSaveModNotBetween(Byte value1, Byte value2) {
            addCriterion("label_value_save_mod not between", value1, value2, "labelValueSaveMod");
            return (Criteria) this;
        }

        public Criteria andTriggerModIsNull() {
            addCriterion("trigger_mod is null");
            return (Criteria) this;
        }

        public Criteria andTriggerModIsNotNull() {
            addCriterion("trigger_mod is not null");
            return (Criteria) this;
        }

        public Criteria andTriggerModEqualTo(Byte value) {
            addCriterion("trigger_mod =", value, "triggerMod");
            return (Criteria) this;
        }

        public Criteria andTriggerModNotEqualTo(Byte value) {
            addCriterion("trigger_mod <>", value, "triggerMod");
            return (Criteria) this;
        }

        public Criteria andTriggerModGreaterThan(Byte value) {
            addCriterion("trigger_mod >", value, "triggerMod");
            return (Criteria) this;
        }

        public Criteria andTriggerModGreaterThanOrEqualTo(Byte value) {
            addCriterion("trigger_mod >=", value, "triggerMod");
            return (Criteria) this;
        }

        public Criteria andTriggerModLessThan(Byte value) {
            addCriterion("trigger_mod <", value, "triggerMod");
            return (Criteria) this;
        }

        public Criteria andTriggerModLessThanOrEqualTo(Byte value) {
            addCriterion("trigger_mod <=", value, "triggerMod");
            return (Criteria) this;
        }

        public Criteria andTriggerModIn(List<Byte> values) {
            addCriterion("trigger_mod in", values, "triggerMod");
            return (Criteria) this;
        }

        public Criteria andTriggerModNotIn(List<Byte> values) {
            addCriterion("trigger_mod not in", values, "triggerMod");
            return (Criteria) this;
        }

        public Criteria andTriggerModBetween(Byte value1, Byte value2) {
            addCriterion("trigger_mod between", value1, value2, "triggerMod");
            return (Criteria) this;
        }

        public Criteria andTriggerModNotBetween(Byte value1, Byte value2) {
            addCriterion("trigger_mod not between", value1, value2, "triggerMod");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyIsNull() {
            addCriterion("trigger_frequency is null");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyIsNotNull() {
            addCriterion("trigger_frequency is not null");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyEqualTo(Byte value) {
            addCriterion("trigger_frequency =", value, "triggerFrequency");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyNotEqualTo(Byte value) {
            addCriterion("trigger_frequency <>", value, "triggerFrequency");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyGreaterThan(Byte value) {
            addCriterion("trigger_frequency >", value, "triggerFrequency");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyGreaterThanOrEqualTo(Byte value) {
            addCriterion("trigger_frequency >=", value, "triggerFrequency");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyLessThan(Byte value) {
            addCriterion("trigger_frequency <", value, "triggerFrequency");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyLessThanOrEqualTo(Byte value) {
            addCriterion("trigger_frequency <=", value, "triggerFrequency");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyIn(List<Byte> values) {
            addCriterion("trigger_frequency in", values, "triggerFrequency");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyNotIn(List<Byte> values) {
            addCriterion("trigger_frequency not in", values, "triggerFrequency");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyBetween(Byte value1, Byte value2) {
            addCriterion("trigger_frequency between", value1, value2, "triggerFrequency");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyNotBetween(Byte value1, Byte value2) {
            addCriterion("trigger_frequency not between", value1, value2, "triggerFrequency");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueIsNull() {
            addCriterion("trigger_frequency_value is null");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueIsNotNull() {
            addCriterion("trigger_frequency_value is not null");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueEqualTo(String value) {
            addCriterion("trigger_frequency_value =", value, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueNotEqualTo(String value) {
            addCriterion("trigger_frequency_value <>", value, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueGreaterThan(String value) {
            addCriterion("trigger_frequency_value >", value, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueGreaterThanOrEqualTo(String value) {
            addCriterion("trigger_frequency_value >=", value, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueLessThan(String value) {
            addCriterion("trigger_frequency_value <", value, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueLessThanOrEqualTo(String value) {
            addCriterion("trigger_frequency_value <=", value, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueLike(String value) {
            addCriterion("trigger_frequency_value like", value, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueNotLike(String value) {
            addCriterion("trigger_frequency_value not like", value, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueIn(List<String> values) {
            addCriterion("trigger_frequency_value in", values, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueNotIn(List<String> values) {
            addCriterion("trigger_frequency_value not in", values, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueBetween(String value1, String value2) {
            addCriterion("trigger_frequency_value between", value1, value2, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueNotBetween(String value1, String value2) {
            addCriterion("trigger_frequency_value not between", value1, value2, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andExecModIsNull() {
            addCriterion("exec_mod is null");
            return (Criteria) this;
        }

        public Criteria andExecModIsNotNull() {
            addCriterion("exec_mod is not null");
            return (Criteria) this;
        }

        public Criteria andExecModEqualTo(Byte value) {
            addCriterion("exec_mod =", value, "execMod");
            return (Criteria) this;
        }

        public Criteria andExecModNotEqualTo(Byte value) {
            addCriterion("exec_mod <>", value, "execMod");
            return (Criteria) this;
        }

        public Criteria andExecModGreaterThan(Byte value) {
            addCriterion("exec_mod >", value, "execMod");
            return (Criteria) this;
        }

        public Criteria andExecModGreaterThanOrEqualTo(Byte value) {
            addCriterion("exec_mod >=", value, "execMod");
            return (Criteria) this;
        }

        public Criteria andExecModLessThan(Byte value) {
            addCriterion("exec_mod <", value, "execMod");
            return (Criteria) this;
        }

        public Criteria andExecModLessThanOrEqualTo(Byte value) {
            addCriterion("exec_mod <=", value, "execMod");
            return (Criteria) this;
        }

        public Criteria andExecModIn(List<Byte> values) {
            addCriterion("exec_mod in", values, "execMod");
            return (Criteria) this;
        }

        public Criteria andExecModNotIn(List<Byte> values) {
            addCriterion("exec_mod not in", values, "execMod");
            return (Criteria) this;
        }

        public Criteria andExecModBetween(Byte value1, Byte value2) {
            addCriterion("exec_mod between", value1, value2, "execMod");
            return (Criteria) this;
        }

        public Criteria andExecModNotBetween(Byte value1, Byte value2) {
            addCriterion("exec_mod not between", value1, value2, "execMod");
            return (Criteria) this;
        }

        public Criteria andFieldIsNull() {
            addCriterion("field is null");
            return (Criteria) this;
        }

        public Criteria andFieldIsNotNull() {
            addCriterion("field is not null");
            return (Criteria) this;
        }

        public Criteria andFieldEqualTo(Long value) {
            addCriterion("field =", value, "field");
            return (Criteria) this;
        }

        public Criteria andFieldNotEqualTo(Long value) {
            addCriterion("field <>", value, "field");
            return (Criteria) this;
        }

        public Criteria andFieldGreaterThan(Long value) {
            addCriterion("field >", value, "field");
            return (Criteria) this;
        }

        public Criteria andFieldGreaterThanOrEqualTo(Long value) {
            addCriterion("field >=", value, "field");
            return (Criteria) this;
        }

        public Criteria andFieldLessThan(Long value) {
            addCriterion("field <", value, "field");
            return (Criteria) this;
        }

        public Criteria andFieldLessThanOrEqualTo(Long value) {
            addCriterion("field <=", value, "field");
            return (Criteria) this;
        }

        public Criteria andFieldIn(List<Long> values) {
            addCriterion("field in", values, "field");
            return (Criteria) this;
        }

        public Criteria andFieldNotIn(List<Long> values) {
            addCriterion("field not in", values, "field");
            return (Criteria) this;
        }

        public Criteria andFieldBetween(Long value1, Long value2) {
            addCriterion("field between", value1, value2, "field");
            return (Criteria) this;
        }

        public Criteria andFieldNotBetween(Long value1, Long value2) {
            addCriterion("field not between", value1, value2, "field");
            return (Criteria) this;
        }

        public Criteria andLabelCalStatusIsNull() {
            addCriterion("label_cal_status is null");
            return (Criteria) this;
        }

        public Criteria andLabelCalStatusIsNotNull() {
            addCriterion("label_cal_status is not null");
            return (Criteria) this;
        }

        public Criteria andLabelCalStatusEqualTo(Byte value) {
            addCriterion("label_cal_status =", value, "labelCalStatus");
            return (Criteria) this;
        }

        public Criteria andLabelCalStatusNotEqualTo(Byte value) {
            addCriterion("label_cal_status <>", value, "labelCalStatus");
            return (Criteria) this;
        }

        public Criteria andLabelCalStatusGreaterThan(Byte value) {
            addCriterion("label_cal_status >", value, "labelCalStatus");
            return (Criteria) this;
        }

        public Criteria andLabelCalStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("label_cal_status >=", value, "labelCalStatus");
            return (Criteria) this;
        }

        public Criteria andLabelCalStatusLessThan(Byte value) {
            addCriterion("label_cal_status <", value, "labelCalStatus");
            return (Criteria) this;
        }

        public Criteria andLabelCalStatusLessThanOrEqualTo(Byte value) {
            addCriterion("label_cal_status <=", value, "labelCalStatus");
            return (Criteria) this;
        }

        public Criteria andLabelCalStatusIn(List<Byte> values) {
            addCriterion("label_cal_status in", values, "labelCalStatus");
            return (Criteria) this;
        }

        public Criteria andLabelCalStatusNotIn(List<Byte> values) {
            addCriterion("label_cal_status not in", values, "labelCalStatus");
            return (Criteria) this;
        }

        public Criteria andLabelCalStatusBetween(Byte value1, Byte value2) {
            addCriterion("label_cal_status between", value1, value2, "labelCalStatus");
            return (Criteria) this;
        }

        public Criteria andLabelCalStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("label_cal_status not between", value1, value2, "labelCalStatus");
            return (Criteria) this;
        }

        public Criteria andLastCalDateIsNull() {
            addCriterion("last_cal_date is null");
            return (Criteria) this;
        }

        public Criteria andLastCalDateIsNotNull() {
            addCriterion("last_cal_date is not null");
            return (Criteria) this;
        }

        public Criteria andLastCalDateEqualTo(Date value) {
            addCriterion("last_cal_date =", value, "lastCalDate");
            return (Criteria) this;
        }

        public Criteria andLastCalDateNotEqualTo(Date value) {
            addCriterion("last_cal_date <>", value, "lastCalDate");
            return (Criteria) this;
        }

        public Criteria andLastCalDateGreaterThan(Date value) {
            addCriterion("last_cal_date >", value, "lastCalDate");
            return (Criteria) this;
        }

        public Criteria andLastCalDateGreaterThanOrEqualTo(Date value) {
            addCriterion("last_cal_date >=", value, "lastCalDate");
            return (Criteria) this;
        }

        public Criteria andLastCalDateLessThan(Date value) {
            addCriterion("last_cal_date <", value, "lastCalDate");
            return (Criteria) this;
        }

        public Criteria andLastCalDateLessThanOrEqualTo(Date value) {
            addCriterion("last_cal_date <=", value, "lastCalDate");
            return (Criteria) this;
        }

        public Criteria andLastCalDateIn(List<Date> values) {
            addCriterion("last_cal_date in", values, "lastCalDate");
            return (Criteria) this;
        }

        public Criteria andLastCalDateNotIn(List<Date> values) {
            addCriterion("last_cal_date not in", values, "lastCalDate");
            return (Criteria) this;
        }

        public Criteria andLastCalDateBetween(Date value1, Date value2) {
            addCriterion("last_cal_date between", value1, value2, "lastCalDate");
            return (Criteria) this;
        }

        public Criteria andLastCalDateNotBetween(Date value1, Date value2) {
            addCriterion("last_cal_date not between", value1, value2, "lastCalDate");
            return (Criteria) this;
        }

        public Criteria andDelIsNull() {
            addCriterion("del is null");
            return (Criteria) this;
        }

        public Criteria andDelIsNotNull() {
            addCriterion("del is not null");
            return (Criteria) this;
        }

        public Criteria andDelEqualTo(Boolean value) {
            addCriterion("del =", value, "del");
            return (Criteria) this;
        }

        public Criteria andDelNotEqualTo(Boolean value) {
            addCriterion("del <>", value, "del");
            return (Criteria) this;
        }

        public Criteria andDelGreaterThan(Boolean value) {
            addCriterion("del >", value, "del");
            return (Criteria) this;
        }

        public Criteria andDelGreaterThanOrEqualTo(Boolean value) {
            addCriterion("del >=", value, "del");
            return (Criteria) this;
        }

        public Criteria andDelLessThan(Boolean value) {
            addCriterion("del <", value, "del");
            return (Criteria) this;
        }

        public Criteria andDelLessThanOrEqualTo(Boolean value) {
            addCriterion("del <=", value, "del");
            return (Criteria) this;
        }

        public Criteria andDelIn(List<Boolean> values) {
            addCriterion("del in", values, "del");
            return (Criteria) this;
        }

        public Criteria andDelNotIn(List<Boolean> values) {
            addCriterion("del not in", values, "del");
            return (Criteria) this;
        }

        public Criteria andDelBetween(Boolean value1, Boolean value2) {
            addCriterion("del between", value1, value2, "del");
            return (Criteria) this;
        }

        public Criteria andDelNotBetween(Boolean value1, Boolean value2) {
            addCriterion("del not between", value1, value2, "del");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("creator like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("creator not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andModifierIsNull() {
            addCriterion("modifier is null");
            return (Criteria) this;
        }

        public Criteria andModifierIsNotNull() {
            addCriterion("modifier is not null");
            return (Criteria) this;
        }

        public Criteria andModifierEqualTo(String value) {
            addCriterion("modifier =", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierNotEqualTo(String value) {
            addCriterion("modifier <>", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierGreaterThan(String value) {
            addCriterion("modifier >", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierGreaterThanOrEqualTo(String value) {
            addCriterion("modifier >=", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierLessThan(String value) {
            addCriterion("modifier <", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierLessThanOrEqualTo(String value) {
            addCriterion("modifier <=", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierLike(String value) {
            addCriterion("modifier like", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierNotLike(String value) {
            addCriterion("modifier not like", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierIn(List<String> values) {
            addCriterion("modifier in", values, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierNotIn(List<String> values) {
            addCriterion("modifier not in", values, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierBetween(String value1, String value2) {
            addCriterion("modifier between", value1, value2, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierNotBetween(String value1, String value2) {
            addCriterion("modifier not between", value1, value2, "modifier");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andTaskIsNull() {
            addCriterion("task is null");
            return (Criteria) this;
        }

        public Criteria andTaskIsNotNull() {
            addCriterion("task is not null");
            return (Criteria) this;
        }

        public Criteria andTaskEqualTo(Long value) {
            addCriterion("task =", value, "task");
            return (Criteria) this;
        }

        public Criteria andTaskNotEqualTo(Long value) {
            addCriterion("task <>", value, "task");
            return (Criteria) this;
        }

        public Criteria andTaskGreaterThan(Long value) {
            addCriterion("task >", value, "task");
            return (Criteria) this;
        }

        public Criteria andTaskGreaterThanOrEqualTo(Long value) {
            addCriterion("task >=", value, "task");
            return (Criteria) this;
        }

        public Criteria andTaskLessThan(Long value) {
            addCriterion("task <", value, "task");
            return (Criteria) this;
        }

        public Criteria andTaskLessThanOrEqualTo(Long value) {
            addCriterion("task <=", value, "task");
            return (Criteria) this;
        }

        public Criteria andTaskIn(List<Long> values) {
            addCriterion("task in", values, "task");
            return (Criteria) this;
        }

        public Criteria andTaskNotIn(List<Long> values) {
            addCriterion("task not in", values, "task");
            return (Criteria) this;
        }

        public Criteria andTaskBetween(Long value1, Long value2) {
            addCriterion("task between", value1, value2, "task");
            return (Criteria) this;
        }

        public Criteria andTaskNotBetween(Long value1, Long value2) {
            addCriterion("task not between", value1, value2, "task");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andRecalculateIsNull() {
            addCriterion("recalculate is null");
            return (Criteria) this;
        }

        public Criteria andRecalculateIsNotNull() {
            addCriterion("recalculate is not null");
            return (Criteria) this;
        }

        public Criteria andRecalculateEqualTo(Boolean value) {
            addCriterion("recalculate =", value, "recalculate");
            return (Criteria) this;
        }

        public Criteria andRecalculateNotEqualTo(Boolean value) {
            addCriterion("recalculate <>", value, "recalculate");
            return (Criteria) this;
        }

        public Criteria andRecalculateGreaterThan(Boolean value) {
            addCriterion("recalculate >", value, "recalculate");
            return (Criteria) this;
        }

        public Criteria andRecalculateGreaterThanOrEqualTo(Boolean value) {
            addCriterion("recalculate >=", value, "recalculate");
            return (Criteria) this;
        }

        public Criteria andRecalculateLessThan(Boolean value) {
            addCriterion("recalculate <", value, "recalculate");
            return (Criteria) this;
        }

        public Criteria andRecalculateLessThanOrEqualTo(Boolean value) {
            addCriterion("recalculate <=", value, "recalculate");
            return (Criteria) this;
        }

        public Criteria andRecalculateIn(List<Boolean> values) {
            addCriterion("recalculate in", values, "recalculate");
            return (Criteria) this;
        }

        public Criteria andRecalculateNotIn(List<Boolean> values) {
            addCriterion("recalculate not in", values, "recalculate");
            return (Criteria) this;
        }

        public Criteria andRecalculateBetween(Boolean value1, Boolean value2) {
            addCriterion("recalculate between", value1, value2, "recalculate");
            return (Criteria) this;
        }

        public Criteria andRecalculateNotBetween(Boolean value1, Boolean value2) {
            addCriterion("recalculate not between", value1, value2, "recalculate");
            return (Criteria) this;
        }

        public Criteria andIsBuiltinEqualTo(Boolean value) {
            addCriterion("is_builtin =", value, "isBuiltin");
            return (Criteria) this;
        }

    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table label
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table label
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}