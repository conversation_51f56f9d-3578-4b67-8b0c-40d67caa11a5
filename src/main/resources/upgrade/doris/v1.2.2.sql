ALTER TABLE user_metric_^& ADD COLUMN last_call_date DATE COMMENT '最后一次呼叫日期, 格式yyyy-MM-dd' DEFAULT NULL;
ALTER TABLE `user_metric_^&` ADD COLUMN `weekly_hourly_auto_answer_calls` JSON COMMENT '用户每周每小时的小秘书接通数统计，JSON格式7×24矩阵：[[hour0_calls, hour1_calls, ..., hour23_calls], ...]，外层数组7个元素代表周日到周六，内层数组24个元素代表0-23小时的接通次数';
ALTER TABLE `user_metric_^&` ADD COLUMN `weekly_hourly_lost_calls_rate` JSON COMMENT '用户每周每小时的未接通率，JSON格式7×24矩阵：[[hour0_rate, hour1_rate, ..., hour23_rate], ...]，外层数组7个元素代表周日到周六，内层数组24个元素代表0-23小时的未接通率';

ALTER TABLE `aiob_conversation_session_agg_^&` ADD COLUMN `total_auto_answer_calls` BIGINT SUM COMMENT '时段内小秘书接听总次数';

ALTER TABLE `aiob_conversation_record_^&` ADD COLUMN `nodeInfo` text NULL COMMENT '节点信息';
