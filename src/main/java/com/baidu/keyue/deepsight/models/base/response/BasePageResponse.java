package com.baidu.keyue.deepsight.models.base.response;

import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @className: BasePageResponse
 * @description: 通用分页返回结构体
 * @author: lvtao03
 * @date: 2024/12/24 14:24
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BasePageResponse<T> {

    /**
     * 请求 ID
     */
    private String requestId;

    /**
     * 错误码，枚举参考 ErrorCode.java
     */
    private String code;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 分页结果
     */
    private Page<T> page = new Page<>();

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Page<T> {

        /**
         * 分页页码
         */
        private Integer pageNo;

        /**
         * 分页大小
         */
        private Integer pageSize;

        /**
         * 总记录数
         */
        private Long total;

        /**
         * 当前分页数据列表
         */
        private List<T> results = new ArrayList<>();

        public static <T> Page<T> of(Integer pageNo, Integer pageSize, Long total, List<T> results) {
            Page<T> page = new Page<>();
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            page.setResults(results);
            page.setTotal(total);
            return page;
        }
    }

    public static <T> BasePageResponse<T> of(ErrorCode errorCode) {
        BasePageResponse<T> response = new BasePageResponse<>();
        response.setCode(errorCode.getCode());
        response.setMessage(errorCode.getMessage());
        response.setRequestId(WebContextHolder.getRequestId());
        return response;
    }

    public static <T> BasePageResponse<T> of(Page<T> page) {
        BasePageResponse<T> response = new BasePageResponse<>();
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setMessage(ErrorCode.SUCCESS.getMessage());
        response.setRequestId(WebContextHolder.getRequestId());
        response.setPage(page);
        return response;
    }

}