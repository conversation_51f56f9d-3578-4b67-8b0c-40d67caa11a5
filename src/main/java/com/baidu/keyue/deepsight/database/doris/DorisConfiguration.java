package com.baidu.keyue.deepsight.database.doris;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @className: MysqlConfiguration
 * @description:
 * @author: wangzhongcheng
 * @date: 2024/12/31 16:48
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "spring.datasource.doris")
public class DorisConfiguration {
    private String url;
    private String db;
    private String username;
    private String password;
    @Value("${driver-class-name")
    private String driverClassName;
}
