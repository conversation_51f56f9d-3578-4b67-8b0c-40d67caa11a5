package com.baidu.keyue.deepsight.service.agg;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.config.AiobMetricAggConfiguration;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.models.agg.AiobAggMetricCal;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import com.baidu.keyue.deepsight.utils.LocalDateTimeUtils;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Slf4j
@Service
public class AiobSessionMetricAggService {

    private final AiobMetricAggConfiguration aiobAggConfiguration;
    private final DorisService dorisService;

    public AiobSessionMetricAggService(
            AiobMetricAggConfiguration aiobAggConfiguration, DorisService dorisService) {
        this.aiobAggConfiguration = aiobAggConfiguration;
        this.dorisService = dorisService;
    }

    public List<String> getSessionAggTables() {
        if (!aiobAggConfiguration.getStatus()) {
            return Collections.emptyList();
        }
        return dorisService.showTablesWithPrefix(Constants.AIOB_SESSION_AGG_TABLE_PREFIX);
    }

    /**
     * 根据天级别的聚合统计表，统计 N 天内的集合指标
     * @param aggTable 天级别的聚合统计表
     */
    public void aiobSessionMetricAggExec(String aggTable) {
        String tenantId = StringUtils.substringAfterLast(aggTable, '_');
        if (StringUtils.isBlank(tenantId)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "聚合统计表名异常");
        }

        String yesterdayDate = DatetimeUtils.yesterdayDate();
        String startDate = DatetimeUtils.passedDateByDay(aiobAggConfiguration.getDay());

        // 1. 针对aggTable中，前一天的增量 oneID，判断统计范围
        // 2. 根据 oneID列表， 批量从 aggTable 中获取最近 N 天的数据，进行聚合统计
        getOneIdsFromSessionAggTableInYesterday(aggTable, yesterdayDate)
                .flatMap(oneId -> this.aiobMetricAggByOneId(oneId, aggTable, startDate, yesterdayDate))
                .flatMap(cal -> this.saveAiobMetric(cal, tenantId))
                .then()
                .onErrorResume(e -> {
                    log.error("aiobSessionMetricAggExec error, aggTable: {}, err:", aggTable, e);
                    return Mono.empty();
                })
                .doOnTerminate(() -> {
                    log.info("aiobSessionMetricAggExec done, aggTable: {}", aggTable);
                })
                .subscribe();
    }

    public Flux<String> getOneIdsFromSessionAggTableInYesterday(String aggTable, String date) {
        String sql = ORMUtils.generateNewOneIdsFromSessionAggTableInYesterday(aggTable, date);
        return dorisService.queryDorisStreaming(sql).flatMap(this::parserOneId);
    }

    public Mono<String> parserOneId(Map<String, Object> item) {
        return Mono.fromCallable(() -> item.get(Constants.TABLE_USER_ONE_ID).toString())
                .subscribeOn(Schedulers.boundedElastic());
    }

    public Mono<AiobAggMetricCal> aiobMetricAggByOneId(String oneId, String aggTable, String startDate, String endDate) {
        return Mono.fromCallable(() -> {
            AiobAggMetricCal metricCal = new AiobAggMetricCal();
            if (StringUtils.isBlank(oneId)) {
                return metricCal;
            }

            // 1.查询最近 N 天的数据
            String sql = ORMUtils.generateQuerySessionAggRecordsWith(aggTable, startDate, endDate, oneId);
            List<Map<String, Object>> recordList = Lists.newArrayList();
            try {
                recordList = dorisService.selectList(sql);
            } catch (Exception e) {
                log.error("aiobMetricAggByOneId selectList error, sql: {}, err:", sql, e);
                return metricCal;
            }

            // 2.在内存中做统计
            for (Map<String, Object> record : recordList) {
                metricCal.appendTotalConnectedCalls(record.get("total_connected_calls"));           // 接通总次数
                metricCal.appendTotalCalls(record.get("total_calls"));                              // 拨打总次数
                metricCal.appendTotalFirstRoundHangup(record.get("total_first_round_hangup"));      // 首轮挂断次数
                metricCal.appendTotalRounds(record.get("total_rounds"));                            // 对话总轮数
                metricCal.appendTotalDurationTime(record.get("total_duration_time"));               // 通话总时长（单位秒）
                metricCal.appendTimeBucket(record.get("time_bucket").toString(), record.get("total_connected_calls")); // 时段
            }
            metricCal.setLastCallDate(LocalDateTimeUtils.parseDate(endDate));

            // 3.返回统计结果
            metricCal.statistics();
            metricCal.setOneId(oneId);
            return metricCal;
        }).subscribeOn(Schedulers.boundedElastic());
    }

    public Mono<Void> saveAiobMetric(AiobAggMetricCal metricCal, String tenantId) {
        return Mono.fromCallable(() -> {
            if (StringUtils.isBlank(metricCal.getOneId())) {
                return null;
            }
            String sql = ORMUtils.generateInsertUserMetricSql(Constants.USER_METRIC_AGG_TABLE_PREFIX + tenantId, metricCal);
            try {
                dorisService.execSql(sql);
            } catch (Exception e) {
                log.error("saveAiobMetric error, sql: {}, err:", sql, e);
            }
            return null;
        }).then();
    }

}
