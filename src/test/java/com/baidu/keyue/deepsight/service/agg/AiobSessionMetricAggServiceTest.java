package com.baidu.keyue.deepsight.service.agg;

import org.apache.commons.lang3.StringUtils;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.baidu.keyue.deepsight.config.AiobMetricAggConfiguration;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.models.agg.AiobAggMetricCal;
import com.baidu.keyue.deepsight.exception.DeepSightException;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
public class AiobSessionMetricAggServiceTest {

    @Mock
    private AiobMetricAggConfiguration aiobAggConfiguration;

    @Mock
    private DorisService dorisService;

    @InjectMocks
    private AiobSessionMetricAggService service;

    @Test
    void getSessionAggTablesWhenDisabledShouldReturnEmptyList() {
        when(aiobAggConfiguration.getStatus()).thenReturn(false);
        List<String> result = service.getSessionAggTables();
        assertTrue(result.isEmpty());
    }

    @Test
    void getSessionAggTablesWhenEnabledShouldReturnTables() {
        when(aiobAggConfiguration.getStatus()).thenReturn(true);
        when(dorisService.showTablesWithPrefix(Constants.AIOB_SESSION_AGG_TABLE_PREFIX))
                .thenReturn(List.of("table1", "table2"));

        List<String> result = service.getSessionAggTables();
        assertEquals(2, result.size());
    }

    @Test
    void aiobSessionMetricAggExecWithInvalidTableNameShouldThrowException() {
        assertThrows(DeepSightException.ParamsErrorException.class,
                () -> service.aiobSessionMetricAggExec("invalid_table_"));
    }

    @Test
    void constructorShouldInitializeDependencies() {
        assertNotNull(service);
    }

    @Test
    void getOneIdsFromSessionAggTableInYesterdayShouldReturnFluxOfOneIds() {
        String aggTable = "agg_table_tenant1";
        String date = "20230101";
        Map<String, Object> mockRecord = Map.of(Constants.TABLE_USER_ONE_ID, "oneId1");

        when(dorisService.queryDorisStreaming(anyString()))
                .thenReturn(Flux.just(mockRecord));

        Flux<String> result = service.getOneIdsFromSessionAggTableInYesterday(aggTable, date);

        StepVerifier.create(result)
                .expectNext("oneId1")
                .verifyComplete();
    }

    @Test
    void parserOneIdShouldExtractOneIdFromMap() {
        Map<String, Object> item = Map.of(Constants.TABLE_USER_ONE_ID, "testOneId");

        Mono<String> result = service.parserOneId(item);

        StepVerifier.create(result)
                .expectNext("testOneId")
                .verifyComplete();
    }

    @Test
    void aiobMetricAggByOneIdWithBlankOneIdShouldReturnEmptyMetric() {
        Mono<AiobAggMetricCal> result = service.aiobMetricAggByOneId("", "table", "start", "end");

        StepVerifier.create(result)
                .assertNext(metric -> assertTrue(StringUtils.isBlank(metric.getOneId())))
                .verifyComplete();
    }

    @Test
    void aiobMetricAggByOneIdWithValidOneIdShouldReturnAggregatedMetric() {
        String oneId = "oneId1";
        String aggTable = "agg_table_tenant1";
        String startDate = "20230101";
        String endDate = "20230102";

        Map<String, Object> record = Map.of(
                "total_connected_calls", 1L,
                "total_calls", 2L,
                "total_first_round_hangup", 1L,
                "total_rounds", 5L,
                "total_duration_time", 100L,
                "time_bucket", "morning"
        );

        when(dorisService.selectList(anyString())).thenReturn(List.of(record));

        Mono<AiobAggMetricCal> result = service.aiobMetricAggByOneId(oneId, aggTable, startDate, endDate);

        StepVerifier.create(result)
                .assertNext(metric -> {
                    assertEquals(oneId, metric.getOneId());
                    assertEquals(1, metric.getTotalConnectedCalls());
                    assertEquals(2, metric.getTotalCalls());
                })
                .verifyComplete();
    }

    @Test
    void saveAiobMetricWithBlankOneIdShouldCompleteWithoutSaving() {
        AiobAggMetricCal metric = new AiobAggMetricCal();
        metric.setOneId("");

        Mono<Void> result = service.saveAiobMetric(metric, "tenant1");

        StepVerifier.create(result)
                .verifyComplete();
        verify(dorisService, never()).execSql(anyString());
    }

    @Test
    void saveAiobMetricWithValidMetricShouldExecuteSql() {
        AiobAggMetricCal metric = new AiobAggMetricCal();
        metric.setOneId("oneId1");

        Mono<Void> result = service.saveAiobMetric(metric, "tenant1");

        StepVerifier.create(result)
                .verifyComplete();
        verify(dorisService, times(1)).execSql(anyString());
    }

}