package com.baidu.keyue.deepsight.models.profile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName UserAnalysisRequest
 * @Description 用户分析查询请求
 * <AUTHOR>
 * @Date 2025/8/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserAnalysisRequest {

    /**
     * oneId
     */
    @NotBlank(message = "oneId不能为空")
    private String oneId;
}
