package com.baidu.keyue.deepsight.service.datamanage.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.baidu.keyue.deepsight.enums.DbTypeEnum;
import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.mysqldb.aiob.entity.DesSecretKey;
import com.baidu.keyue.deepsight.mysqldb.aiob.entity.DesSystemSecretKey;
import com.baidu.keyue.deepsight.mysqldb.aiob.mapper.DesSecretKeyMapper;
import com.baidu.keyue.deepsight.mysqldb.aiob.mapper.DesSystemSecretKeyMapper;
import com.baidu.keyue.deepsight.mysqldb.entity.AccessToken;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.FieldEncryConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.AccessTokenMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.FieldEncryConfigMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
public class TableRecordCommonServiceTest {

    @InjectMocks
    private TableRecordCommonService tableRecordCommonService;

    @Mock
    private DataTableManageService dataTableManageService;

    @Mock
    private FieldEncryConfigMapper fieldEncryConfigMapper;

    @Mock
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @Mock
    private DataTableInfoMapper dataTableInfoMapper;

    @Mock
    private DesSecretKeyMapper desSecretKeyMapper;

    @Mock
    private DesSystemSecretKeyMapper desSystemSecretKeyMapper;

    @Mock
    private AccessTokenMapper tokenMapper;

    @Test
    void testGetFieldsType() {
        Long dataTableId = 1L;
        TableFieldMetaInfo metaInfo1 = new TableFieldMetaInfo();
        metaInfo1.setEnField("field1");
        metaInfo1.setDataType("string");
        TableFieldMetaInfo metaInfo2 = new TableFieldMetaInfo();
        metaInfo2.setEnField("field2");
        metaInfo2.setDataType("number");

        when(dataTableManageService.queryTableFieldsMetaInfo(dataTableId))
                .thenReturn(Arrays.asList(metaInfo1, metaInfo2));

        Map<String, String> result = tableRecordCommonService.getFieldsType(dataTableId);
        assertEquals(2, result.size());
        assertEquals("string", result.get("field1"));
        assertEquals("number", result.get("field2"));
    }

    @Test
    void testSwitchField() {
        List<String> errorFields = new ArrayList<>();

        // Test number type
        tableRecordCommonService.switchField("numberField", 123, "int", errorFields);
        assertTrue(errorFields.isEmpty());

        // Test invalid number type
        tableRecordCommonService.switchField("invalidNumber", "not a number", "int", errorFields);
        assertFalse(errorFields.isEmpty());
    }

    @Test
    void testGetTableToken() {
        String tableName = "test_table";
        AccessToken expected = new AccessToken();
        expected.setId(1L);

        when(tokenMapper.selectByExample(any()))
                .thenReturn(Collections.singletonList(expected));

        AccessToken result = tableRecordCommonService.getTableToken(tableName);
        assertEquals(expected, result);
    }

    @Test
    void testGetAiobSecretKeyType2() {
        Integer secretType = 2;
        Long tenantId = 1L;
        String secretId = "123";
        DesSystemSecretKey secretKey = new DesSystemSecretKey();
        secretKey.setSystemSecretKey("system_key");

        when(desSystemSecretKeyMapper.selectByExample(any()))
                .thenReturn(Collections.singletonList(secretKey));

        String result = tableRecordCommonService.getAiobSecretKey(secretType, tenantId, secretId);
        assertEquals("system_key", result);
    }

    @Test
    void testRecordConvert() {
        Map<String, Object> itemMap = new HashMap<>();
        itemMap.put("field1", "value1");

        tableRecordCommonService.recordConvert(itemMap);
        // Just verify no exception thrown
        assertNotNull(itemMap);
    }

    @Test
    void testEncryptItem() {
        Map<String, Object> itemMap = new HashMap<>();
        itemMap.put("field1", "value1");
        itemMap.put("field2", "value2");

        Map<String, String> fieldEncryptInfo = new HashMap<>();
        fieldEncryptInfo.put("field1", "key1");

        Map<String, Object> result = tableRecordCommonService.encryptItem(itemMap, fieldEncryptInfo);
        assertNotEquals("value1", result.get("field1"));
        assertEquals("value2", result.get("field2"));
    }

    @Test
    void testGetEncryptFields() {
        Long dataTableId = 1L;
        FieldEncryConfig config1 = new FieldEncryConfig();
        config1.setEnField("field1");
        config1.setSecretKey("key1");
        FieldEncryConfig config2 = new FieldEncryConfig();
        config2.setEnField("field2");
        config2.setSecretKey("key2");

        when(fieldEncryConfigMapper.selectByExample(any()))
                .thenReturn(Arrays.asList(config1, config2));

        Map<String, String> result = tableRecordCommonService.getEncryptFields(dataTableId);
        assertEquals(2, result.size());
        assertEquals("key1", result.get("field1"));
        assertEquals("key2", result.get("field2"));
    }

    @Test
    void testGetTableByTableName() {
        String tableName = "test_table";
        DataTableInfo expected = new DataTableInfo();
        expected.setId(1L);

        when(dataTableInfoMapper.selectByExample(any()))
                .thenReturn(Collections.singletonList(expected));

        DataTableInfo result = tableRecordCommonService.getTableByTableName(tableName);
        assertEquals(expected, result);
    }

    @Test
    void testGetFieldDetailByName() {
        String tableEnName = "test_table";
        String enField = "test_field";
        TableFieldMetaInfo expected = new TableFieldMetaInfo();
        expected.setId(1L);

        when(tableFieldMetaInfoMapper.selectByExample(any()))
                .thenReturn(Collections.singletonList(expected));

        TableFieldMetaInfo result = tableRecordCommonService.getFieldDetailByName(tableEnName, enField);
        assertEquals(expected, result);
    }

    @Test
    void testGetDataTableDetailByTenantId() {
        String tenantId = "tenant1";
        DataTableInfo expected = new DataTableInfo();
        expected.setId(1L);

        when(dataTableInfoMapper.selectByExample(any()))
                .thenReturn(Collections.singletonList(expected));

        DataTableInfo result = tableRecordCommonService.getDataTableDetail(tenantId);
        assertEquals(expected, result);
    }


    @Test
    void testIsJsonOrArrayJson() {
        // Test valid JSON object
        String validJson = "{\"key\":\"value\"}";
        assertTrue(TableRecordCommonService.isJsonOrArrayJson(validJson));

        // Test valid JSON array
        String validJsonArray = "[{\"key\":\"value\"}, {\"key2\":\"value2\"}]";
        assertTrue(TableRecordCommonService.isJsonOrArrayJson(validJsonArray));

        // Test invalid JSON
        String invalidJson = "not a json";
        assertFalse(TableRecordCommonService.isJsonOrArrayJson(invalidJson));
    }

    @Test
    void testRecordAesEncrypt() {
        Map<String, Object> itemMap = new HashMap<>();
        itemMap.put("field1", "value1");

        tableRecordCommonService.recordAesEncrypt(itemMap);
        // Just verify no exception thrown
        assertNotNull(itemMap);
    }

    @Test
    void testGetRequiredFields() {
        Long dataTableId = 1L;
        TableFieldMetaInfo metaInfo1 = new TableFieldMetaInfo();
        metaInfo1.setIsRequired(true);
        metaInfo1.setEnField("field1");
        TableFieldMetaInfo metaInfo2 = new TableFieldMetaInfo();
        metaInfo2.setIsRequired(false);
        metaInfo2.setEnField("field2");

        when(dataTableManageService.queryTableFieldsMetaInfo(dataTableId))
                .thenReturn(Arrays.asList(metaInfo1, metaInfo2));

        List<String> result = tableRecordCommonService.getRequiredFields(dataTableId);
        assertEquals(1, result.size());
        assertEquals("field1", result.get(0));
    }

    @Test
    void testGetAiobSecretKeyType1() {
        Integer secretType = 1;
        Long tenantId = 1L;
        String secretId = "123";
        DesSecretKey secretKey = new DesSecretKey();
        secretKey.setSecretKey("test_key");

        when(desSecretKeyMapper.selectByExample(any()))
                .thenReturn(Collections.singletonList(secretKey));

        String result = tableRecordCommonService.getAiobSecretKey(secretType, tenantId, secretId);
        assertEquals("test_key", result);
    }

    @Test
    void testGetDataTableDetailById() {
        Long dataTableId = 1L;
        DataTableInfo expected = new DataTableInfo();
        expected.setId(dataTableId);

        when(dataTableInfoMapper.selectByPrimaryKey(dataTableId))
                .thenReturn(expected);

        DataTableInfo result = tableRecordCommonService.getDataTableDetail(dataTableId);
        assertEquals(expected, result);
    }

    @Test
    void testGetDataTableDetailByTableNameAndTenantId() {
        String tableName = "test_table";
        String tenantId = "tenant1";
        DataTableInfo expected = new DataTableInfo();
        expected.setId(1L);

        when(dataTableInfoMapper.selectByExample(any()))
                .thenReturn(Collections.singletonList(expected));

        DataTableInfo result = tableRecordCommonService.getDataTableDetail(tableName, tenantId);
        assertEquals(expected, result);
    }

    @Test
    void testQueryFieldNumberMax() {
        String tableEnName = "test_table";
        TableFieldMetaInfo metaInfo1 = new TableFieldMetaInfo();
        metaInfo1.setNumber(10);
        TableFieldMetaInfo metaInfo2 = new TableFieldMetaInfo();
        metaInfo2.setNumber(5);

        when(tableFieldMetaInfoMapper.selectByExample(any()))
                .thenReturn(Arrays.asList(metaInfo1, metaInfo2));

        Integer result = tableRecordCommonService.queryFieldNumberMax(tableEnName);
        assertEquals(10, result);
    }

    @Test
    void testQueryTableFieldByTag() {
        Long dataTableId = 1L;
        TableFieldTagEnum tag = TableFieldTagEnum.PRIMARY;
        TableFieldMetaInfo expected = new TableFieldMetaInfo();
        expected.setId(1L);

        when(tableFieldMetaInfoMapper.selectByExample(any()))
                .thenReturn(Collections.singletonList(expected));

        TableFieldMetaInfo result = tableRecordCommonService.queryTableFieldByTag(dataTableId, tag);
        assertEquals(expected, result);
    }

    @Test
    void testRecordCheckValidDateField() {
        Map<String, Object> itemMap = new HashMap<>();
        itemMap.put("dateField", "2023-01-01");

        Map<String, String> fieldsType = new HashMap<>();
        fieldsType.put("dateField", "date");

        List<String> result = tableRecordCommonService.recordCheck(
                itemMap,
                List.of("dateField"),
                fieldsType,
                DbTypeEnum.DORIS_TYPE.getDbType()
        );

        assertTrue(result.isEmpty());
    }

    @Test
    void testRecordCheckValidJsonField() {
        Map<String, Object> itemMap = new HashMap<>();
        itemMap.put("jsonField", "{\"key\":\"value\"}");

        Map<String, String> fieldsType = new HashMap<>();
        fieldsType.put("jsonField", "json");

        List<String> result = tableRecordCommonService.recordCheck(
                itemMap,
                List.of("jsonField"),
                fieldsType,
                DbTypeEnum.DORIS_TYPE.getDbType()
        );

        assertTrue(result.isEmpty());
    }

    @Test
    void testRecordCheckFieldTypeValidationES() {
        Map<String, Object> itemMap = new HashMap<>();
        itemMap.put("stringField", "value");
        itemMap.put("numberField", "notANumber");

        Map<String, String> fieldsType = new HashMap<>();
        fieldsType.put("stringField", "keyword");
        fieldsType.put("numberField", "long");

        List<String> requiredFields = Arrays.asList("stringField", "numberField");

        DeepSightException.ParamsErrorException exception = assertThrows(DeepSightException.ParamsErrorException.class, () -> {
            tableRecordCommonService.recordCheck(
                    itemMap,
                    requiredFields,
                    fieldsType,
                    DbTypeEnum.ES_TYPE.getDbType()
            );
        });

        assertTrue(exception.getMessage().contains("numberField"));
    }

    @Test
    void testRecordCheckEmptyValueForNotEmptyType() {
        Map<String, Object> itemMap = new HashMap<>();
        itemMap.put("numberField", "");
        itemMap.put("jsonField", "");

        Map<String, String> fieldsType = new HashMap<>();
        fieldsType.put("numberField", "int");
        fieldsType.put("jsonField", "json");

        List<String> requiredFields = Arrays.asList("numberField", "jsonField");

        DeepSightException.ParamsErrorException exception = assertThrows(DeepSightException.ParamsErrorException.class, () -> {
            tableRecordCommonService.recordCheck(
                    itemMap,
                    requiredFields,
                    fieldsType,
                    DbTypeEnum.DORIS_TYPE.getDbType()
            );
        });

        assertTrue(exception.getMessage().contains("numberField") ||
                exception.getMessage().contains("jsonField"));
    }

    @Test
    void testRecordCheckFieldTypeValidationDoris() {
        Map<String, Object> itemMap = new HashMap<>();
        itemMap.put("stringField", "value");
        itemMap.put("numberField", "notANumber");

        Map<String, String> fieldsType = new HashMap<>();
        fieldsType.put("stringField", "varchar");
        fieldsType.put("numberField", "int");

        List<String> requiredFields = Arrays.asList("stringField", "numberField");

        DeepSightException.ParamsErrorException exception = assertThrows(DeepSightException.ParamsErrorException.class, () -> {
            tableRecordCommonService.recordCheck(
                    itemMap,
                    requiredFields,
                    fieldsType,
                    DbTypeEnum.DORIS_TYPE.getDbType()
            );
        });

        assertTrue(exception.getMessage().contains("numberField"));
    }

    @Test
    void testRecordCheckInvalidDateField() {
        Map<String, Object> itemMap = new HashMap<>();
        itemMap.put("dateField", "invalid-date");

        Map<String, String> fieldsType = new HashMap<>();
        fieldsType.put("dateField", "date");

        DeepSightException.ParamsErrorException exception = assertThrows(DeepSightException.ParamsErrorException.class, () -> {
            tableRecordCommonService.recordCheck(
                    itemMap,
                    List.of("dateField"),
                    fieldsType,
                    DbTypeEnum.DORIS_TYPE.getDbType()
            );
        });

        assertTrue(exception.getMessage().contains("dateField"));
    }

    @Test
    void testRecordCheckEmptyJsonField() {
        Map<String, Object> itemMap = new HashMap<>();
        itemMap.put("jsonField", "");

        Map<String, String> fieldsType = new HashMap<>();
        fieldsType.put("jsonField", "json");

        DeepSightException.ParamsErrorException exception = assertThrows(DeepSightException.ParamsErrorException.class, () -> {
            tableRecordCommonService.recordCheck(
                    itemMap,
                    List.of("jsonField"),
                    fieldsType,
                    DbTypeEnum.DORIS_TYPE.getDbType()
            );
        });

    }

    @Test
    void testRecordCheckRemoveRedundantFields() {
        Map<String, Object> itemMap = new HashMap<>();
        itemMap.put("validField", "value");
        itemMap.put("invalidField", "value");

        Map<String, String> fieldsType = new HashMap<>();
        fieldsType.put("validField", "string");
        try {
            List<String> result = tableRecordCommonService.recordCheck(
                    itemMap,
                    List.of("validField"),
                    fieldsType,
                    DbTypeEnum.DORIS_TYPE.getDbType()
            );

        } catch (Exception e) {

        }

    }

    @Test
    void testRecordCheckRequiredFieldMissing() {
        Map<String, Object> itemMap = new HashMap<>();
        itemMap.put("field1", "value");

        Map<String, String> fieldsType = new HashMap<>();
        fieldsType.put("field1", "string");
        fieldsType.put("requiredField", "number");

        List<String> requiredFields = Arrays.asList("field1", "requiredField");

        NullPointerException exception = assertThrows(NullPointerException.class, () -> {
            tableRecordCommonService.recordCheck(
                    itemMap,
                    requiredFields,
                    fieldsType,
                    DbTypeEnum.DORIS_TYPE.getDbType()
            );
        });

    }

    @Test
    void testSaveUpgradeFieldMetaInfo() {
        // Prepare test data
        String tableName = "test_table";
        List<String> addFieldNames = List.of("field1", "field2");

        DataTableInfo tableInfo = new DataTableInfo();
        tableInfo.setId(1L);
        tableInfo.setTableName(tableName);

        TableFieldMetaInfo field1 = new TableFieldMetaInfo();
        field1.setEnField("field1");
        TableFieldMetaInfo field2 = new TableFieldMetaInfo();
        field2.setEnField("field2");

        Map<String, TableFieldMetaInfo> fieldsInfoMap = Map.of(
                "field1", field1,
                "field2", field2
        );

        // Mock dependencies
        when(dataTableInfoMapper.selectByExample(any()))
                .thenReturn(List.of(tableInfo));

        // Execute the method
        tableRecordCommonService.saveUpgradeFieldMetaInfo(tableName, addFieldNames, fieldsInfoMap);

        // Verify interactions
        verify(tableFieldMetaInfoMapper, times(2)).insert(any(TableFieldMetaInfo.class));

        // Verify field properties were set correctly
        ArgumentCaptor<TableFieldMetaInfo> captor = ArgumentCaptor.forClass(TableFieldMetaInfo.class);
        verify(tableFieldMetaInfoMapper, times(2)).insert(captor.capture());

        List<TableFieldMetaInfo> capturedFields = captor.getAllValues();
        assertEquals(2, capturedFields.size());
        for (TableFieldMetaInfo field : capturedFields) {
            assertFalse(field.getFromBaidu());
            assertEquals(tableName, field.getTableEnName());
            assertEquals(1L, field.getDataTableId());
        }
    }

    @Test
    void testSaveUpgradeFieldMetaInfo_EmptyFieldList() {
        // Prepare test data
        String tableName = "test_table";
        List<String> addFieldNames = List.of();
        Map<String, TableFieldMetaInfo> fieldsInfoMap = Map.of();

        DataTableInfo tableInfo = new DataTableInfo();
        tableInfo.setId(1L);
        tableInfo.setTableName(tableName);

        // Mock dependencies
        when(dataTableInfoMapper.selectByExample(any()))
                .thenReturn(List.of(tableInfo));

        // Execute the method
        tableRecordCommonService.saveUpgradeFieldMetaInfo(tableName, addFieldNames, fieldsInfoMap);

        // Verify no interactions with mapper
        verify(tableFieldMetaInfoMapper, never()).insert(any());
    }

    @Test
    void testSaveUpgradeFieldMetaInfo_TableNotFound() {
        // Prepare test data
        String tableName = "non_existent_table";
        List<String> addFieldNames = List.of("field1");
        Map<String, TableFieldMetaInfo> fieldsInfoMap = Map.of("field1", new TableFieldMetaInfo());

        // Mock dependencies to return empty list
        when(dataTableInfoMapper.selectByExample(any()))
                .thenReturn(List.of());

        // Execute and verify exception
        assertThrows(DeepSightException.ParamsErrorException.class, () -> {
            tableRecordCommonService.saveUpgradeFieldMetaInfo(tableName, addFieldNames, fieldsInfoMap);
        });

        // Verify no interactions with field mapper
        verify(tableFieldMetaInfoMapper, never()).insert(any());
    }

    @Test
    void testGetAiobSecretKeyNullParameters() {
        assertNull(tableRecordCommonService.getAiobSecretKey(null, 1L, "123"));
        assertNull(tableRecordCommonService.getAiobSecretKey(1, null, "123"));
    }

    @Test
    void testGetAiobSecretKeyType1NullSecretId() {
        assertNull(tableRecordCommonService.getAiobSecretKey(1, 1L, null));
    }

    @Test
    void testGetAiobSecretKeyType1Valid() {
        Long secretId = 123L;
        String expectedKey = "secret_key_value";
        
        DesSecretKey secretKey = new DesSecretKey();
        secretKey.setSecretKey(expectedKey);
        
        when(desSecretKeyMapper.selectByExample(any()))
                .thenReturn(Collections.singletonList(secretKey));
    
        String result = tableRecordCommonService.getAiobSecretKey(1, 1L, secretId.toString());
        assertEquals(expectedKey, result);
    }

    @Test
    void testGetAiobSecretKeyType1EmptyResult() {
        Long secretId = 123L;
        
        when(desSecretKeyMapper.selectByExample(any()))
                .thenReturn(Collections.emptyList());
    
        assertNull(tableRecordCommonService.getAiobSecretKey(1, 1L, secretId.toString()));
    }

    @Test
    void testGetAiobSecretKeyType2Valid() {
        Integer secretType = 2;
        Long tenantId = 1L;
        String expectedKey = "system_key_value";
        
        DesSystemSecretKey secretKey = new DesSystemSecretKey();
        secretKey.setSystemSecretKey(expectedKey);
        
        when(desSystemSecretKeyMapper.selectByExample(any()))
                .thenReturn(Collections.singletonList(secretKey));
    
        String result = tableRecordCommonService.getAiobSecretKey(secretType, tenantId, null);
        assertEquals(expectedKey, result);
    }

    @Test
    void testGetAiobSecretKeyType4Valid() {
        Integer secretType = 4;
        Long tenantId = 1L;
        String expectedKey = "system_key_value";
        
        DesSystemSecretKey secretKey = new DesSystemSecretKey();
        secretKey.setSystemSecretKey(expectedKey);
        
        when(desSystemSecretKeyMapper.selectByExample(any()))
                .thenReturn(Collections.singletonList(secretKey));
    
        String result = tableRecordCommonService.getAiobSecretKey(secretType, tenantId, null);
        assertEquals(expectedKey, result);
    }

    @Test
    void testGetAiobSecretKeyType2EmptyResult() {
        Integer secretType = 2;
        Long tenantId = 1L;
        
        when(desSystemSecretKeyMapper.selectByExample(any()))
                .thenReturn(Collections.emptyList());
    
        assertNull(tableRecordCommonService.getAiobSecretKey(secretType, tenantId, null));
    }

    @Test
    void testGetAiobSecretKeyInvalidType() {
        assertNull(tableRecordCommonService.getAiobSecretKey(3, 1L, "123"));
    }

    @Test
    void testGetAiobSecretKeyType4Valid1() {
        Integer secretType = 4;
        Long tenantId = 1L;
        String expectedKey = "system_key_value";
        
        DesSystemSecretKey secretKey = new DesSystemSecretKey();
        secretKey.setSystemSecretKey(expectedKey);
        
        when(desSystemSecretKeyMapper.selectByExample(any()))
                .thenReturn(Collections.singletonList(secretKey));
    
        String result = tableRecordCommonService.getAiobSecretKey(secretType, tenantId, null);
        assertEquals(expectedKey, result);
    }

    @Test
    void testGetAiobSecretKeyType2EmptyResult1() {
        Integer secretType = 2;
        Long tenantId = 1L;
        
        when(desSystemSecretKeyMapper.selectByExample(any()))
                .thenReturn(Collections.emptyList());
    
        assertNull(tableRecordCommonService.getAiobSecretKey(secretType, tenantId, null));
    }

    @Test
    void testGetAiobSecretKeyType1Valid1() {
        Long secretId = 123L;
        String expectedKey = "secret_key_value";
        
        DesSecretKey secretKey = new DesSecretKey();
        secretKey.setSecretKey(expectedKey);
        
        when(desSecretKeyMapper.selectByExample(any()))
                .thenReturn(Collections.singletonList(secretKey));
    
        String result = tableRecordCommonService.getAiobSecretKey(1, 1L, secretId.toString());
        assertEquals(expectedKey, result);
    }

    @Test
    void testGetAiobSecretKeyType1EmptyResult1() {
        Long secretId = 123L;
        
        when(desSecretKeyMapper.selectByExample(any()))
                .thenReturn(Collections.emptyList());
    
        assertNull(tableRecordCommonService.getAiobSecretKey(1, 1L, secretId.toString()));
    }

    @Test
    void testGetAiobSecretKeyType2Valid1() {
        Integer secretType = 2;
        Long tenantId = 1L;
        String expectedKey = "system_key_value";
        
        DesSystemSecretKey secretKey = new DesSystemSecretKey();
        secretKey.setSystemSecretKey(expectedKey);
        
        when(desSystemSecretKeyMapper.selectByExample(any()))
                .thenReturn(Collections.singletonList(secretKey));
    
        String result = tableRecordCommonService.getAiobSecretKey(secretType, tenantId, null);
        assertEquals(expectedKey, result);
    }

}