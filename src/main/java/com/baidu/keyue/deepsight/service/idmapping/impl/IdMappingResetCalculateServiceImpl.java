package com.baidu.keyue.deepsight.service.idmapping.impl;

import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.idmapping.dto.DatasetKafkaMsgDTO;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingDataTableWithRule;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingGenerator;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingGeneratorCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRule;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendIdMappingDataTableMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingGeneratorMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.idmapping.IdMappingResetCalculateService;
import com.baidu.keyue.deepsight.service.tasks.TaskSchedulerService;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mortbay.log.Log;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @className: IdMappingResetCalculateServiceImpl
 * @description:
 * @author: wangzhongcheng
 * @date: 2025/3/20 17:48
 */
@Slf4j
@Service
public class IdMappingResetCalculateServiceImpl implements IdMappingResetCalculateService {

    @Autowired
    private TaskSchedulerService taskSchedulerService;

    @Autowired
    private ExtendIdMappingDataTableMapper idMappingDataTableMapper;

    @Autowired
    private IdMappingGeneratorMapper idMappingGeneratorMapper;

    @Autowired
    private TableFieldMetaInfoMapper fieldMetaInfoMapper;

    @Autowired
    private DorisService dorisService;

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    private RedissonClient redisson;

    @Value("${deepSight.kafka.topic.idMapping}")
    private String idMappingResetTopic;

    private static final Integer RESET_MAX_SIZE = 500;

    @Override
    public void execByManual(String tenantId, String userId) {
        IdMappingGeneratorCriteria idMappingGeneratorCriteria = new IdMappingGeneratorCriteria();
        idMappingGeneratorCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId).andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<IdMappingGenerator> idMappingGenerators = idMappingGeneratorMapper.selectByExample(idMappingGeneratorCriteria);
        if (CollectionUtils.isEmpty(idMappingGenerators)) {
            log.error("IdMappingResetCalculateServiceImpl execByManual no idMappingGenerator task");
            return;
        }

        IdMappingGenerator idMappingGenerator = idMappingGenerators.get(0);
        long schedulerId = taskSchedulerService.newTaskScheduler(idMappingGenerator.getTaskId(), userId);
        taskSchedulerService.updateSchedulerStatus(schedulerId, TaskExecStatusEnum.RUNNING);
        idMappingGenerator.setCalStatus(TaskExecStatusEnum.RUNNING.getCode());
        idMappingGeneratorMapper.updateByPrimaryKey(idMappingGenerator);

        doExec(tenantId, userId, schedulerId, idMappingGenerator);
    }

    /**
     * 实际执行reset Id-Mapping 逻辑
     */
    @Async
    public void doExec(String tenantId, String userId, long schedulerId, IdMappingGenerator idMappingGenerator) {
        Log.info("IdMappingResetCalculateServiceImpl do exec tenantId: {}", tenantId);
        try {
            // 获取需要重新刷新 oneId 的表 - 字段
            List<IdMappingDataTableWithRule> dataTableWithRules = idMappingDataTableMapper
                    .selectIdMappingDataTableWithRule(tenantId, DelEnum.NOT_DELETED.getBoolean());

            // 清空 oneId 表数据
            String idMappingTableName = TenantUtils.generateIdMappingTableName(tenantId);
            String clearOneIdSql = String.format("TRUNCATE TABLE %s", idMappingTableName);
            dorisService.execSql(clearOneIdSql);

            // 遍历带刷新 oneId 表数据，塞入 id-mapping 刷新结果kafka
            log.info("IdMappingResetCalculateServiceImpl do exec data refreshed start, oneId is reset");
            for (IdMappingDataTableWithRule dataTableWithRule : dataTableWithRules) {
                log.info("IdMappingResetCalculateServiceImpl do exec tableName: {}, dataTableId: {}",
                        dataTableWithRule.getTableEnName(), dataTableWithRule.getDataTableId());

                refreshTableData(dataTableWithRule);
            }

            updateTaskStatus(userId, schedulerId, idMappingGenerator, TaskExecStatusEnum.SUCCESS);
            log.info("IdMappingResetCalculateServiceImpl do exec success");
        } catch (Exception ex) {
            log.error("IdMappingResetCalculateServiceImpl do exec error", ex);
            updateTaskStatus(userId, schedulerId, idMappingGenerator, TaskExecStatusEnum.FAILED);
        }
    }

    public void updateTaskStatus(String userId,
                                 long schedulerId,
                                 IdMappingGenerator idMappingGenerator,
                                 TaskExecStatusEnum taskExecStatusEnum) {
        Date date = new Date();
        taskSchedulerService.updateSchedulerStatus(schedulerId, taskExecStatusEnum);
        idMappingGenerator.setCalStatus(taskExecStatusEnum.getCode());
        idMappingGenerator.setLastCalDate(date);
        idMappingGenerator.setModifier(userId);
        idMappingGenerator.setUpdateTime(date);
        idMappingGeneratorMapper.updateByPrimaryKey(idMappingGenerator);
    }

    /**
     * 刷新数据
     * @param dataTableWithRule dataTableWithRule
     */
    public void refreshTableData(IdMappingDataTableWithRule dataTableWithRule) {
        Long dataTableId = dataTableWithRule.getDataTableId();
        String tableName = dataTableWithRule.getTableEnName();
        List<String> enFields = dataTableWithRule.getIdMappingRules().stream()
                .map(IdMappingRule::getEnField).distinct().toList();
        if (CollectionUtils.isEmpty(enFields)) {
            log.info("IdMappingResetCalculateServiceImpl 表不存在字段 tableName: {}, dataTableId: {}", tableName, dataTableId);
            return;
        }
        TableFieldMetaInfoCriteria fieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
        fieldMetaInfoCriteria.createCriteria()
                .andDataTableIdEqualTo(dataTableId).andFieldTagEqualTo(TableFieldTagEnum.PRIMARY.getCode());
        List<TableFieldMetaInfo> tableFieldMetaInfos = fieldMetaInfoMapper.selectByExample(fieldMetaInfoCriteria);
        if (CollectionUtils.isEmpty(tableFieldMetaInfos)) {
            log.error("IdMappingResetCalculateServiceImpl 表不存在主键 tableName: {}, dataTableId: {}", tableName, dataTableId);
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "表不存在主键");
        }
        String primaryEnField = tableFieldMetaInfos.get(0).getEnField();
        if (!dorisService.existTable(tableName)) {
            log.warn("IdMappingResetCalculateServiceImpl 表不存在 tableName: {}, dataTableId: {}", tableName, dataTableId);
            return;
        }
        long total = dorisService.getSingleTableCount(tableName);
        if (total <= 0) {
            log.info("IdMappingResetCalculateServiceImpl 表数据为空 tableName: {}, dataTableId: {}", tableName, dataTableId);
            return;
        }
        log.info("IdMappingResetCalculateServiceImpl 刷新数据 tableName: {}, dataTableId: {}, total: {}",
                tableName, dataTableId, total);

        // 构建查询语句
        int offset = 0;
        for (; offset < total; offset += RESET_MAX_SIZE) {
            log.info("IdMappingResetCalculateServiceImpl 刷新数据 tableName: {}, dataTableId: {}, offset: {}, size: {}",
                    tableName, dataTableId, offset, RESET_MAX_SIZE);
            // 下游根据oneId聚合的任务需要依赖其他字段，此处全量传送
            String sql = String.format("SELECT * FROM %s ORDER BY %s LIMIT %d, %d",
                    tableName, primaryEnField, offset, RESET_MAX_SIZE);
            List<Map<String, Object>> dataList = dorisService.selectList(sql);
            dataList.forEach(data -> {
                DatasetKafkaMsgDTO datasetKafkaMsgDTO = new DatasetKafkaMsgDTO();
                datasetKafkaMsgDTO.setCode(tableName);
                datasetKafkaMsgDTO.setData(handleDataNode(data));
                kafkaTemplate.send(idMappingResetTopic, JsonUtils.toJsonUnchecked(datasetKafkaMsgDTO))
                        .whenComplete((result, ex) -> {
                            if (ex != null) {
                                log.error("IdMappingResetCalculateServiceImpl send kafka error", ex);
                            } else {
                                // 处理成功
                                log.debug("IdMappingResetCalculateServiceImpl Message sent successfully! " +
                                                "Topic: {}, Partition: {}, Offset: {}",
                                        result.getRecordMetadata().topic(),
                                        result.getRecordMetadata().partition(),
                                        result.getRecordMetadata().offset());
                            }
                        });
            });
        }

    }

    public Map<String, Object> handleDataNode(Map<String, Object> data) {
        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            Object value = entry.getValue();
            if (entry.getValue() == null) {
                value = StringUtils.EMPTY;
            } else if (value instanceof LocalDateTime dateTime) {
                value = DatetimeUtils.formatDate(dateTime);
            }

            map.put(entry.getKey(), value);
        }

        return map;
    }

}
