package com.baidu.keyue.deepsight.config.threadpool;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;


@Slf4j
@EnableAsync
@Configuration
public class AsyncExecutorConfig {

    @Bean(name = "baiduDataPullTaskExecutor")
    public ThreadPoolTaskExecutor baiduDataPullTaskExecutor(
            @Value("${baiduDataPullTask.coreNumber:10}") int threadCorePoolSize,
            @Value("${baiduDataPullTask.maxNumber:20}") int threadMaxPoolSize,
            @Value("${baiduDataPullTask.queueSize:2000}") int threadWorkQueueSize) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadCorePoolSize);
        executor.setMaxPoolSize(threadMaxPoolSize);
        executor.setQueueCapacity(threadWorkQueueSize);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("baiduDataPullTask-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        return executor;
    }


    @Bean(name = "customerDiffusionCheckTaskExecutor")
    public ThreadPoolTaskExecutor customerDiffusionCheckTaskExecutor(
            @Value("${customerDiffusionCheckTask.coreNumber:10}") int threadCorePoolSize,
            @Value("${customerDiffusionCheckTask.maxNumber:20}") int threadMaxPoolSize,
            @Value("${customerDiffusionCheckTask.queueSize:2000}") int threadWorkQueueSize) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadCorePoolSize);
        executor.setMaxPoolSize(threadMaxPoolSize);
        executor.setQueueCapacity(threadWorkQueueSize);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("customerDiffusionCheckTask-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        return executor;
    }

    @Bean(name = "dataPredictionTaskExecutor")
    public ThreadPoolTaskExecutor dataPredictionTaskExecutor(
            @Value("${dataPredictionTask.coreNumber:10}") int threadCorePoolSize,
            @Value("${dataPredictionTask.maxNumber:20}") int threadMaxPoolSize,
            @Value("${dataPredictionTask.queueSize:2000}") int threadWorkQueueSize) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadCorePoolSize);
        executor.setMaxPoolSize(threadMaxPoolSize);
        executor.setQueueCapacity(threadWorkQueueSize);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("dataPredictionTask-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        return executor;
    }

    @Bean(name = "memoryCalTaskExecutor")
    public ThreadPoolTaskExecutor memoryCalTaskExecutor(
            @Value("${memoryCalTask.coreNumber:10}") int threadCorePoolSize,
            @Value("${memoryCalTask.maxNumber:20}") int threadMaxPoolSize,
            @Value("${memoryCalTask.queueSize:2000}") int threadWorkQueueSize) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadCorePoolSize);
        executor.setMaxPoolSize(threadMaxPoolSize);
        executor.setQueueCapacity(threadWorkQueueSize);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("memoryCalTask-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        return executor;
    }

    @Bean(name = "metricAggTaskExecutor")
    public ThreadPoolTaskExecutor metricAggTaskExecutor(
            @Value("${metricAggTask.coreNumber:10}") int threadCorePoolSize,
            @Value("${metricAggTask.maxNumber:20}") int threadMaxPoolSize,
            @Value("${metricAggTask.queueSize:2000}") int threadWorkQueueSize) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadCorePoolSize);
        executor.setMaxPoolSize(threadMaxPoolSize);
        executor.setQueueCapacity(threadWorkQueueSize);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("metricAggTask-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        return executor;
    }

    @Bean(name = "aiobTaskInfoSyncTaskExecutor")
    public ThreadPoolTaskExecutor aiobTaskInfoSyncTaskExecutor(
            @Value("${aiobTaskInfoSyncTask.coreNumber:10}") int threadCorePoolSize,
            @Value("${aiobTaskInfoSyncTask.maxNumber:20}") int threadMaxPoolSize,
            @Value("${aiobTaskInfoSyncTask.queueSize:2000}") int threadWorkQueueSize) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadCorePoolSize);
        executor.setMaxPoolSize(threadMaxPoolSize);
        executor.setQueueCapacity(threadWorkQueueSize);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("aiobTaskInfoSyncTask-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        return executor;
    }


}
