package com.baidu.keyue.deepsight.service.datamanage.impl;

import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvRowHandler;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baidu.keyue.deepsight.config.BosConfig;
import com.baidu.keyue.deepsight.database.doris.DorisConfiguration;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.DbTypeEnum;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.ImportMappingTypeEnum;
import com.baidu.keyue.deepsight.enums.ImportStatusEnum;
import com.baidu.keyue.deepsight.enums.ImportTypeEnum;
import com.baidu.keyue.deepsight.models.datamanage.dto.FieldMappingDto;
import com.baidu.keyue.deepsight.models.datamanage.dto.FileDetailDto;
import com.baidu.keyue.deepsight.models.datamanage.dto.RowDataDto;
import com.baidu.keyue.deepsight.models.datamanage.request.FileImportFieldMappingRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.FileImportRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.FieldAiMappingResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.FileImportTaskResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskFileImportCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskFileImportWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.mapper.TaskFileImportMapper;
import com.baidu.keyue.deepsight.service.ai.AiBaseService;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.datamanage.TableContentService;
import com.baidu.keyue.deepsight.service.tool.BosUtils;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import com.baidu.keyue.deepsight.utils.GzipUtils;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import com.baidubce.services.bos.BosObjectInputStream;
import com.baidubce.services.bos.model.BosObject;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.elasticsearch.client.RestHighLevelClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
@TestPropertySource(properties = {
        "spring.data.elasticsearch.prefix=test_",
        "file.upload.enFiledPattern=[a-zA-Z0-9_]+",
        "file.upload.suffixNames=csv,xlsx",
        "local.file.tempFilePath=temp",
        "local.file.maxBatch=5",
        "data.sync.url=http://localhost:8080",
        "kafka.topics.dataSync=test_topic"
})
public class TableRecordServiceImplTest {


    private FileImportFieldMappingRequest mappingRequest;

    private List<RowDataDto> rowDataDtos;

    @Mock
    private DataTableManageService dataTableManageService;

    @Mock
    private AiBaseService aiBaseService;

    private List<VisibleFieldResponse> visibleFields;

    private List<FieldMappingDto> expectedMappings;

    private List<TaskFileImportWithBLOBs> taskList;

    private UserAuthInfo userAuthInfo;

    private DataTableInfo tableInfo;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private TableContentService tableContentService;

    private FileImportRequest fileImportRequest;
    private DataTableInfo dataTableInfo;

    private TaskFileImportWithBLOBs taskFileImport;

    @Mock
    private TaskFileImportMapper taskFileImportMapper;

    @Mock
    private TableRecordCommonService tableRecordCommonService;

    @Mock
    private BosConfig bosConfig;

    @Mock
    private DorisConfiguration dorisConfiguration;
    private List<FieldMappingDto> fieldMappings;

    private FieldMappingDto mapping1;

    private FieldMappingDto mapping2;

    @Mock
    private DorisService dorisService;

    FileImportTaskResponse res = new FileImportTaskResponse();
    //    @Spy

    @Mock
    private BosUtils bosUtils;

    @InjectMocks
    private TableRecordServiceImpl tableRecordService;

    @Mock
    private RestHighLevelClient client;

    private Map<String, Object> itemMap;

    @BeforeEach
    void setUp() {
        itemMap = new HashMap<>();
        itemMap.put("sourceField1", "value1");
        itemMap.put("sourceField2", 123);

        mapping1 = new FieldMappingDto();
        mapping1.setSourceEnName("sourceField1");
        mapping1.setTagEnName("tagField1");
        mapping1.setTagType("string");

        mapping2 = new FieldMappingDto();
        mapping2.setSourceEnName("sourceField2");
        mapping2.setTagEnName("tagField2");
        mapping2.setTagType("int");

        fieldMappings = Arrays.asList(mapping1, mapping2);
        init();
    }

    public void init() {
        ReflectionTestUtils.setField(tableRecordService, "tableRecordCommonService", tableRecordCommonService);
        ReflectionTestUtils.setField(tableRecordService, "taskFileImportMapper", taskFileImportMapper);
        ReflectionTestUtils.setField(tableRecordService, "dorisConfiguration", dorisConfiguration);
        ReflectionTestUtils.setField(tableRecordService, "dorisService", dorisService);
        ReflectionTestUtils.setField(tableRecordService, "indexPrefix", "dev_");
        ReflectionTestUtils.setField(tableRecordService, "applicationContext", applicationContext);
        ReflectionTestUtils.setField(tableRecordService, "bosConfig", bosConfig);
    }

    @Test
    void excFieldMappingShouldReturnMappedFieldsForDoris() {
        // Arrange
        when(dorisService.covertDorisValue(anyString(), any())).thenReturn("convertedValue");

        // Act
        Map<String, Object> result = tableRecordService.excFieldMapping(itemMap, fieldMappings, DbTypeEnum.DORIS_TYPE);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("convertedValue", result.get("tagField1"));
        assertEquals("convertedValue", result.get("tagField2"));
        verify(dorisService, times(2)).covertDorisValue(anyString(), any());
    }

    @Test
    void excFieldMappingShouldReturnMappedFieldsForES() {
        // Act
        Map<String, Object> result = tableRecordService.excFieldMapping(itemMap, fieldMappings, DbTypeEnum.ES_TYPE);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("value1", result.get("tagField1"));
        assertEquals(123, result.get("tagField2"));
    }

    @Test
    void excFieldMappingShouldHandleNullSourceField() {
        // Arrange
        FieldMappingDto nullMapping = new FieldMappingDto();
        nullMapping.setSourceEnName(null);
        nullMapping.setTagEnName("tagField3");
        nullMapping.setTagType("string");
        fieldMappings = Arrays.asList(nullMapping);

        // Act
        Map<String, Object> result = tableRecordService.excFieldMapping(itemMap, fieldMappings, DbTypeEnum.ES_TYPE);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNull(result.get("tagField3"));
    }

    @Test
    void excFieldMappingShouldHandleMissingSourceField() {
        // Arrange
        FieldMappingDto missingMapping = new FieldMappingDto();
        missingMapping.setSourceEnName("nonExistingField");
        missingMapping.setTagEnName("tagField4");
        missingMapping.setTagType("string");
        fieldMappings = Arrays.asList(missingMapping);

        // Act
        Map<String, Object> result = tableRecordService.excFieldMapping(itemMap, fieldMappings, DbTypeEnum.ES_TYPE);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNull(result.get("tagField4"));
    }

    @Test
    void fileImportShouldSuccessForDorisIncrement() throws Exception {
        fileImportRequest = new FileImportRequest();
        fileImportRequest.setGroupId("testGroupId");
        fileImportRequest.setDataTableId(1L);
        fileImportRequest.setImportTypeEnum(ImportTypeEnum.INCREMENT);
        fileImportRequest.setMappingTypeEnum(ImportMappingTypeEnum.EQUAL_NAME);

        userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(123L);
        userAuthInfo.setUserName("testUser");
        userAuthInfo.setTenantId(1L);

        tableInfo = new DataTableInfo();
        tableInfo.setDbType(DbTypeEnum.DORIS_TYPE.getDbType());
        tableInfo.setTableName("testTable");
        tableInfo.setTenantid("1");

        TaskFileImportWithBLOBs task = new TaskFileImportWithBLOBs();
        task.setGroupId("testGroupId");
        task.setStatus(ImportStatusEnum.UPLOAD.getStatus());
        task.setDel(false);
        task.setTenantId("testTenantId");
        task.setSourceName("test.csv");
        taskList = Arrays.asList(task);

        fieldMappings = new ArrayList<>();
        FieldMappingDto mapping = new FieldMappingDto();
        mapping.setSourceEnName("field1");
        mapping.setTagEnName("field2");
        fieldMappings.add(mapping);
        fileImportRequest.setFieldMappings(fieldMappings);

        when(tableRecordCommonService.getDataTableDetail(anyLong())).thenReturn(tableInfo);
        when(taskFileImportMapper.selectByExampleWithBLOBs(any())).thenReturn(taskList);
        when(applicationContext.getBean(TableContentService.class)).thenReturn(tableContentService);

        try (MockedStatic<GzipUtils> gzipUtils = mockStatic(GzipUtils.class);
             MockedStatic<DatetimeUtils> datetimeUtils = mockStatic(DatetimeUtils.class)) {
            gzipUtils.when(() -> GzipUtils.compressObj(any())).thenReturn(new byte[]{1, 2, 3});
            datetimeUtils.when(() -> DatetimeUtils.pureDateFormat(any())).thenReturn("20230101");

            FileImportTaskResponse response = tableRecordService.fileImport(fileImportRequest, userAuthInfo);
            assertNotNull(response);
            verify(dorisService, never()).execSql(anyString());
            verify(taskFileImportMapper, atLeastOnce()).updateByPrimaryKeyWithBLOBs(any());
        }
    }

    @Test
    void fileImportShouldSuccessForEsCover() throws Exception {
        fileImportRequest = new FileImportRequest();
        fileImportRequest.setGroupId("testGroupId");
        fileImportRequest.setDataTableId(1L);
        fileImportRequest.setImportTypeEnum(ImportTypeEnum.INCREMENT);
        fileImportRequest.setMappingTypeEnum(ImportMappingTypeEnum.EQUAL_NAME);

        userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(123L);
        userAuthInfo.setUserName("testUser");
        userAuthInfo.setTenantId(1L);

        tableInfo = new DataTableInfo();
        tableInfo.setDbType(DbTypeEnum.DORIS_TYPE.getDbType());
        tableInfo.setTableName("testTable");
        tableInfo.setTenantid("1");

        TaskFileImportWithBLOBs task = new TaskFileImportWithBLOBs();
        task.setGroupId("testGroupId");
        task.setStatus(ImportStatusEnum.UPLOAD.getStatus());
        task.setDel(false);
        task.setTenantId("testTenantId");
        task.setSourceName("test.csv");
        taskList = Arrays.asList(task);

        fieldMappings = new ArrayList<>();
        FieldMappingDto mapping = new FieldMappingDto();
        mapping.setSourceEnName("field1");
        mapping.setTagEnName("field2");
        fieldMappings.add(mapping);
        fileImportRequest.setFieldMappings(fieldMappings);
        // ES data
        when(tableRecordCommonService.getDataTableDetail(anyLong())).thenReturn(tableInfo);
        when(taskFileImportMapper.selectByExampleWithBLOBs(any())).thenReturn(taskList);
        when(applicationContext.getBean(TableContentService.class)).thenReturn(tableContentService);

        try (MockedStatic<GzipUtils> gzipUtils = mockStatic(GzipUtils.class);
             MockedStatic<DatetimeUtils> datetimeUtils = mockStatic(DatetimeUtils.class)) {
            gzipUtils.when(() -> GzipUtils.compressObj(any())).thenReturn(new byte[]{1, 2, 3});
            datetimeUtils.when(() -> DatetimeUtils.pureDateFormat(any())).thenReturn("20230101");

            tableInfo.setDbType(DbTypeEnum.ES_TYPE.getDbType());
            fileImportRequest.setImportTypeEnum(ImportTypeEnum.COVER);
            ReflectionTestUtils.setField(tableRecordService, "client", client);
            FileImportTaskResponse response = tableRecordService.fileImport(fileImportRequest, userAuthInfo);
            assertNotNull(response);
            verify(taskFileImportMapper, atLeastOnce()).updateByPrimaryKeyWithBLOBs(any());
        }

        // DORIS data
        when(tableRecordCommonService.getDataTableDetail(anyLong())).thenReturn(tableInfo);
        when(taskFileImportMapper.selectByExampleWithBLOBs(any())).thenReturn(taskList);
        when(applicationContext.getBean(TableContentService.class)).thenReturn(tableContentService);

        try (MockedStatic<GzipUtils> gzipUtils = mockStatic(GzipUtils.class);
             MockedStatic<DatetimeUtils> datetimeUtils = mockStatic(DatetimeUtils.class)) {
            gzipUtils.when(() -> GzipUtils.compressObj(any())).thenReturn(new byte[]{1, 2, 3});
            datetimeUtils.when(() -> DatetimeUtils.pureDateFormat(any())).thenReturn("20230101");

            tableInfo.setDbType(DbTypeEnum.DORIS_TYPE.getDbType());
            fileImportRequest.setImportTypeEnum(ImportTypeEnum.COVER);
            ReflectionTestUtils.setField(tableRecordService, "client", client);
            FileImportTaskResponse response = tableRecordService.fileImport(fileImportRequest, userAuthInfo);

            assertNotNull(response);
            verify(taskFileImportMapper, atLeastOnce()).updateByPrimaryKeyWithBLOBs(any());
        }
    }

    @Test
    void fileImportShouldThrowWhenTableNotExist() {
        fileImportRequest = new FileImportRequest();
        fileImportRequest.setGroupId("testGroupId");
        fileImportRequest.setDataTableId(1L);
        fileImportRequest.setImportTypeEnum(ImportTypeEnum.INCREMENT);
        fileImportRequest.setMappingTypeEnum(ImportMappingTypeEnum.EQUAL_NAME);

        userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(123L);
        userAuthInfo.setUserName("testUser");
        userAuthInfo.setTenantId(1L);

        tableInfo = new DataTableInfo();
        tableInfo.setDbType(DbTypeEnum.DORIS_TYPE.getDbType());
        tableInfo.setTableName("testTable");

        TaskFileImportWithBLOBs task = new TaskFileImportWithBLOBs();
        task.setGroupId("testGroupId");
        task.setStatus(ImportStatusEnum.UPLOAD.getStatus());
        task.setDel(false);
        task.setTenantId("testTenantId");
        task.setSourceName("test.csv");
        taskList = Arrays.asList(task);

        fieldMappings = new ArrayList<>();
        FieldMappingDto mapping = new FieldMappingDto();
        mapping.setSourceEnName("field1");
        mapping.setTagEnName("field2");
        fieldMappings.add(mapping);
        fileImportRequest.setFieldMappings(fieldMappings);

        when(tableRecordCommonService.getDataTableDetail(anyLong())).thenReturn(tableInfo);
        when(taskFileImportMapper.selectByExampleWithBLOBs(any())).thenReturn(taskList);
        when(applicationContext.getBean(TableContentService.class)).thenReturn(tableContentService);

        when(tableRecordCommonService.getDataTableDetail(anyLong())).thenReturn(null);

        assertThrows(RuntimeException.class, () ->
                tableRecordService.fileImport(fileImportRequest, userAuthInfo)
        );
    }

    @Test
    void fileImportShouldThrowWhenTaskListEmpty() {
        fileImportRequest = new FileImportRequest();
        fileImportRequest.setGroupId("testGroupId");
        fileImportRequest.setDataTableId(1L);
        fileImportRequest.setImportTypeEnum(ImportTypeEnum.INCREMENT);
        fileImportRequest.setMappingTypeEnum(ImportMappingTypeEnum.EQUAL_NAME);

        userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(123L);
        userAuthInfo.setUserName("testUser");
        userAuthInfo.setTenantId(1L);

        tableInfo = new DataTableInfo();
        tableInfo.setDbType(DbTypeEnum.DORIS_TYPE.getDbType());
        tableInfo.setTableName("testTable");

        TaskFileImportWithBLOBs task = new TaskFileImportWithBLOBs();
        task.setGroupId("testGroupId");
        task.setStatus(ImportStatusEnum.UPLOAD.getStatus());
        task.setDel(false);
        task.setTenantId("testTenantId");
        task.setSourceName("test.csv");
        taskList = Arrays.asList(task);

        fieldMappings = new ArrayList<>();
        FieldMappingDto mapping = new FieldMappingDto();
        mapping.setSourceEnName("field1");
        mapping.setTagEnName("field2");
        fieldMappings.add(mapping);
        fileImportRequest.setFieldMappings(fieldMappings);

        when(tableRecordCommonService.getDataTableDetail(anyLong())).thenReturn(tableInfo);
        when(taskFileImportMapper.selectByExampleWithBLOBs(any())).thenReturn(taskList);
        when(applicationContext.getBean(TableContentService.class)).thenReturn(tableContentService);

        when(taskFileImportMapper.selectByExampleWithBLOBs(any())).thenReturn(new ArrayList<>());

        assertThrows(RuntimeException.class, () ->
                tableRecordService.fileImport(fileImportRequest, userAuthInfo)
        );
    }

    @Test
    void fileImportShouldHandleErrorWhenCompressFail() throws Exception {
        fileImportRequest = new FileImportRequest();
        fileImportRequest.setGroupId("testGroupId");
        fileImportRequest.setDataTableId(1L);
        fileImportRequest.setImportTypeEnum(ImportTypeEnum.INCREMENT);
        fileImportRequest.setMappingTypeEnum(ImportMappingTypeEnum.EQUAL_NAME);

        userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(123L);
        userAuthInfo.setUserName("testUser");
        userAuthInfo.setTenantId(1L);

        tableInfo = new DataTableInfo();
        tableInfo.setDbType(DbTypeEnum.DORIS_TYPE.getDbType());
        tableInfo.setTableName("testTable");

        TaskFileImportWithBLOBs task = new TaskFileImportWithBLOBs();
        task.setGroupId("testGroupId");
        task.setStatus(ImportStatusEnum.UPLOAD.getStatus());
        task.setDel(false);
        task.setTenantId("testTenantId");
        task.setSourceName("test.csv");
        taskList = Arrays.asList(task);

        fieldMappings = new ArrayList<>();
        FieldMappingDto mapping = new FieldMappingDto();
        mapping.setSourceEnName("field1");
        mapping.setTagEnName("field2");
        fieldMappings.add(mapping);
        fileImportRequest.setFieldMappings(fieldMappings);

        when(tableRecordCommonService.getDataTableDetail(anyLong())).thenReturn(tableInfo);
        when(taskFileImportMapper.selectByExampleWithBLOBs(any())).thenReturn(taskList);
        when(applicationContext.getBean(TableContentService.class)).thenReturn(tableContentService);

        try (MockedStatic<GzipUtils> gzipUtils = mockStatic(GzipUtils.class)) {
            gzipUtils.when(() -> GzipUtils.compressObj(any())).thenReturn(null);

            assertThrows(RuntimeException.class, () ->
                    tableRecordService.fileImport(fileImportRequest, userAuthInfo)
            );
        }
    }

    @Test
    void fileImportShouldThrowExceptionWhenTableNotExist() {
        userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(123L);
        userAuthInfo.setUserName("testUser");
        userAuthInfo.setTenantId(1L);

        fileImportRequest = new FileImportRequest();
        fileImportRequest.setGroupId("testGroup");
        fileImportRequest.setDataTableId(1L);
        fileImportRequest.setImportTypeEnum(ImportTypeEnum.INCREMENT);
        fileImportRequest.setFieldMappings(Arrays.asList(new FieldMappingDto(), new FieldMappingDto()));

        dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        dataTableInfo.setTableName("testTable");
        dataTableInfo.setTenantid("2");

        taskFileImport = new TaskFileImportWithBLOBs();
        taskFileImport.setId(1L);
        taskFileImport.setGroupId("testGroup");
        taskFileImport.setTenantId("testTenant");
        taskFileImport.setStatus(ImportStatusEnum.UPLOAD.getStatus());
        taskFileImport.setDel(DelEnum.NOT_DELETED.getBoolean());
        taskFileImport.setSourceName("test.xlsx");
        taskFileImport.setSuffixName("xlsx");

        when(tableRecordCommonService.getDataTableDetail(anyLong())).thenReturn(null);

        assertThrows(DeepSightException.ParamsErrorException.class,
                () -> tableRecordService.fileImport(fileImportRequest, userAuthInfo));
    }

    @Test
    void fileImportShouldThrowExceptionWhenTenantNotMatch() {
        userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(123L);
        userAuthInfo.setUserName("testUser");
        userAuthInfo.setTenantId(1L);

        fileImportRequest = new FileImportRequest();
        fileImportRequest.setGroupId("testGroup");
        fileImportRequest.setDataTableId(1L);
        fileImportRequest.setImportTypeEnum(ImportTypeEnum.INCREMENT);
        fileImportRequest.setFieldMappings(Arrays.asList(new FieldMappingDto(), new FieldMappingDto()));

        dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        dataTableInfo.setTableName("testTable");
        dataTableInfo.setTenantid("2");

        taskFileImport = new TaskFileImportWithBLOBs();
        taskFileImport.setId(1L);
        taskFileImport.setGroupId("testGroup");
        taskFileImport.setTenantId("testTenant");
        taskFileImport.setStatus(ImportStatusEnum.UPLOAD.getStatus());
        taskFileImport.setDel(DelEnum.NOT_DELETED.getBoolean());
        taskFileImport.setSourceName("test.xlsx");
        taskFileImport.setSuffixName("xlsx");

        DataTableInfo wrongTenantTable = new DataTableInfo();
        wrongTenantTable.setTenantid("wrongTenant");
        when(tableRecordCommonService.getDataTableDetail(anyLong())).thenReturn(wrongTenantTable);

        assertThrows(DeepSightException.ParamsErrorException.class,
                () -> tableRecordService.fileImport(fileImportRequest, userAuthInfo));
    }

    @Test
    void fileImportShouldThrowExceptionWhenNoTasksFound() {
        userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(123L);
        userAuthInfo.setUserName("testUser");
        userAuthInfo.setTenantId(1L);

        fileImportRequest = new FileImportRequest();
        fileImportRequest.setGroupId("testGroup");
        fileImportRequest.setDataTableId(1L);
        fileImportRequest.setImportTypeEnum(ImportTypeEnum.INCREMENT);
        fileImportRequest.setFieldMappings(Arrays.asList(new FieldMappingDto(), new FieldMappingDto()));

        dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        dataTableInfo.setTableName("testTable");
        dataTableInfo.setTenantid("1");

        taskFileImport = new TaskFileImportWithBLOBs();
        taskFileImport.setId(1L);
        taskFileImport.setGroupId("testGroup");
        taskFileImport.setTenantId("1");
        taskFileImport.setStatus(ImportStatusEnum.UPLOAD.getStatus());
        taskFileImport.setDel(DelEnum.NOT_DELETED.getBoolean());
        taskFileImport.setSourceName("test.xlsx");
        taskFileImport.setSuffixName("xlsx");

        when(tableRecordCommonService.getDataTableDetail(anyLong())).thenReturn(dataTableInfo);
        when(taskFileImportMapper.selectByExampleWithBLOBs(any())).thenReturn(Collections.emptyList());

        assertThrows(DeepSightException.ParamsErrorException.class,
                () -> tableRecordService.fileImport(fileImportRequest, userAuthInfo));
    }

    @Test
    void fileImportShouldDeleteDataWhenCoverType() throws Exception {
        userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(123L);
        userAuthInfo.setUserName("testUser");
        userAuthInfo.setTenantId(1L);

        fileImportRequest = new FileImportRequest();
        fileImportRequest.setGroupId("testGroup");
        fileImportRequest.setDataTableId(1L);
        fileImportRequest.setImportTypeEnum(ImportTypeEnum.INCREMENT);
        fileImportRequest.setFieldMappings(Arrays.asList(new FieldMappingDto(), new FieldMappingDto()));

        dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        dataTableInfo.setTableName("testTable");
        dataTableInfo.setTenantid("1");

        taskFileImport = new TaskFileImportWithBLOBs();
        taskFileImport.setId(1L);
        taskFileImport.setGroupId("testGroup");
        taskFileImport.setTenantId("1");
        taskFileImport.setStatus(ImportStatusEnum.UPLOAD.getStatus());
        taskFileImport.setDel(DelEnum.NOT_DELETED.getBoolean());
        taskFileImport.setSourceName("test.xlsx");
        taskFileImport.setSuffixName("xlsx");

        fileImportRequest.setImportTypeEnum(ImportTypeEnum.COVER);
        when(tableRecordCommonService.getDataTableDetail(anyLong())).thenReturn(dataTableInfo);
        when(taskFileImportMapper.selectByExampleWithBLOBs(any())).thenReturn(Collections.singletonList(taskFileImport));
        when(dorisConfiguration.getDb()).thenReturn("testDb");
        when(applicationContext.getBean(TableContentService.class)).thenReturn(tableContentService);
        when(bosConfig.getEnv()).thenReturn("test");
        when(bosConfig.getBucket()).thenReturn(new BosConfig.Bucket());

        FileImportTaskResponse response = tableRecordService.fileImport(fileImportRequest, userAuthInfo);

        assertNotNull(response);
    }

    @Test
    void fileImportShouldHandleImportErrors() throws Exception {
        userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(123L);
        userAuthInfo.setUserName("testUser");
        userAuthInfo.setTenantId(1L);

        fileImportRequest = new FileImportRequest();
        fileImportRequest.setGroupId("testGroup");
        fileImportRequest.setDataTableId(1L);
        fileImportRequest.setImportTypeEnum(ImportTypeEnum.INCREMENT);
        fileImportRequest.setFieldMappings(Arrays.asList(new FieldMappingDto(), new FieldMappingDto()));

        dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        dataTableInfo.setTableName("testTable");
        dataTableInfo.setTenantid("1");

        taskFileImport = new TaskFileImportWithBLOBs();
        taskFileImport.setId(1L);
        taskFileImport.setGroupId("testGroup");
        taskFileImport.setTenantId("testTenant");
        taskFileImport.setStatus(ImportStatusEnum.UPLOAD.getStatus());
        taskFileImport.setDel(DelEnum.NOT_DELETED.getBoolean());
        taskFileImport.setSourceName("test.xlsx");
        taskFileImport.setSuffixName("xlsx");

        when(tableRecordCommonService.getDataTableDetail(anyLong())).thenReturn(dataTableInfo);
        when(taskFileImportMapper.selectByExampleWithBLOBs(any())).thenReturn(Collections.singletonList(taskFileImport));
        when(applicationContext.getBean(TableContentService.class)).thenReturn(tableContentService);
        when(bosConfig.getEnv()).thenReturn("test");
        when(bosConfig.getBucket()).thenReturn(new BosConfig.Bucket());
        BosObject bosObject = new BosObject();
        InputStream mockInputStream = mock(InputStream.class);
        bosObject.setObjectContent(new BosObjectInputStream(mockInputStream, mock(CloseableHttpResponse.class)));
        when(mockInputStream.read(any())).thenReturn(0);
        when(bosUtils.getObject(anyString(), anyString())).thenReturn(new BosObject());

        // Simulate import error
        List<Map<String, Object>> errors = new ArrayList<>();
        errors.add(Collections.singletonMap("error", "test error"));
        doReturn(errors).when(tableContentService).importFileData(any(), any(), any(), any());

        FileImportTaskResponse response = tableRecordService.fileImport(fileImportRequest, userAuthInfo);

        assertNotNull(response);
        verify(tableContentService, times(1)).updateImportTasksById(anyList());
    }

    @Test
    void fileImportShouldReturnSuccessResponse() throws Exception {
        userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(123L);
        userAuthInfo.setUserName("testUser");
        userAuthInfo.setTenantId(1L);

        fileImportRequest = new FileImportRequest();
        fileImportRequest.setGroupId("testGroup");
        fileImportRequest.setDataTableId(1L);
        fileImportRequest.setImportTypeEnum(ImportTypeEnum.INCREMENT);
        fileImportRequest.setFieldMappings(Arrays.asList(new FieldMappingDto(), new FieldMappingDto()));

        dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        dataTableInfo.setTableName("testTable");
        dataTableInfo.setTenantid("1");

        taskFileImport = new TaskFileImportWithBLOBs();
        taskFileImport.setId(1L);
        taskFileImport.setGroupId("testGroup");
        taskFileImport.setTenantId("testTenant");
        taskFileImport.setStatus(ImportStatusEnum.UPLOAD.getStatus());
        taskFileImport.setDel(DelEnum.NOT_DELETED.getBoolean());
        taskFileImport.setSourceName("test.xlsx");
        taskFileImport.setSuffixName("xlsx");

        when(bosConfig.getEnv()).thenReturn("test");
        when(bosConfig.getBucket()).thenReturn(new BosConfig.Bucket());
        when(tableRecordCommonService.getDataTableDetail(anyLong())).thenReturn(dataTableInfo);
        when(taskFileImportMapper.selectByExampleWithBLOBs(any())).thenReturn(Collections.singletonList(taskFileImport));
        when(applicationContext.getBean(TableContentService.class)).thenReturn(tableContentService);

        FileImportTaskResponse response = tableRecordService.fileImport(fileImportRequest, userAuthInfo);

        assertNotNull(response);
        assertEquals(ImportStatusEnum.IMPORT_SUCCESS.getStatus(), taskFileImport.getStatus());
    }

    @Test
    void getOneDataFromFileShouldThrowWhenFileNotExist() {
        // Arrange
        FileDetailDto file = new FileDetailDto("test.csv", "test_key");
        BosConfig.Bucket bucket = new BosConfig.Bucket();
        bucket.setDataSync("data-sync-bucket");
        when(bosConfig.getBucket()).thenReturn(bucket);
        when(bosUtils.getObject(anyString(), anyString())).thenReturn(null);
        // Act & Assert
        DeepSightException.ParamsErrorException paramsErrorException = assertThrows(DeepSightException.ParamsErrorException.class,
                () -> tableRecordService.getOneDataFromFile(file));
        assertEquals("文件不存在", paramsErrorException.getMessage());
    }

    @Test
    void getOneDataFromFileShouldThrowWhenInvalidRowCount() throws IOException {
        // Arrange
        FileDetailDto file = new FileDetailDto("test.xlsx", "test_key");
        BosObject bosObject = mock(BosObject.class);
        BosObjectInputStream inputStream = mock(BosObjectInputStream.class);
        BosConfig.Bucket bucket = new BosConfig.Bucket();
        bucket.setDataSync("data-sync-bucket");
        when(bosConfig.getBucket()).thenReturn(bucket);
        when(bosUtils.getObject(anyString(), anyString())).thenReturn(bosObject);
        when(bosObject.getObjectContent()).thenReturn(inputStream);
        try (MockedStatic<ExcelUtil> excelUtilMock = mockStatic(ExcelUtil.class)) {
            ExcelReader excelReader = mock(ExcelReader.class);
            excelUtilMock.when(() -> ExcelUtil.getReader(inputStream, 0)).thenReturn(excelReader);
            List<List<Object>> mockData = Arrays.asList(
                    List.of("field1"),
                    List.of("字段1")
            );
            when(excelReader.read(anyInt(), anyInt())).thenReturn(mockData);
            // Act & Assert
            DeepSightException.ParamsErrorException paramsErrorException = assertThrows(DeepSightException.ParamsErrorException.class,
                    () -> tableRecordService.getOneDataFromFile(file));
            assertEquals("数据行数错误，需大于等于3行", paramsErrorException.getMessage());
        }
    }


    @Test
    void getOneDataFromFileShouldReturnRowDataForCsvFile() throws IOException {
        // Arrange
        FileDetailDto file = new FileDetailDto("test.csv", "test_key");
        BosObject bosObject = mock(BosObject.class);
        BosObjectInputStream inputStream = mock(BosObjectInputStream.class);
        BosConfig.Bucket bucket = new BosConfig.Bucket();
        bucket.setDataSync("data-sync-bucket");
        when(bosConfig.getBucket()).thenReturn(bucket);
        when(bosUtils.getObject(anyString(), anyString())).thenReturn(bosObject);
        when(bosObject.getObjectContent()).thenReturn(inputStream);
        try (MockedStatic<CsvUtil> csvUtilMock = mockStatic(CsvUtil.class)) {
            CsvReader csvReader = mock(CsvReader.class);
            csvUtilMock.when(() -> CsvUtil.getReader(any(InputStreamReader.class))).thenReturn(csvReader);
            doAnswer(invocation -> null).when(csvReader).read(any(CsvRowHandler.class));
            // Act & Assert
            DeepSightException.ParamsErrorException paramsErrorException = assertThrows(DeepSightException.ParamsErrorException.class,
                    () -> tableRecordService.getOneDataFromFile(file));
        }
    }

    @Test
    void getOneDataFromFileShouldReturnRowDataForExcelFile() throws IOException {
        // Arrange
        FileDetailDto file = new FileDetailDto("test.xlsx", "test_key");
        BosObject bosObject = mock(BosObject.class);
        BosObjectInputStream inputStream = mock(BosObjectInputStream.class);
        BosConfig.Bucket bucket = new BosConfig.Bucket();
        bucket.setDataSync("data-sync-bucket");
        ReflectionTestUtils.setField(tableRecordService, "enFieldPattern", "^[A-Za-z0-9_]+$");
        when(bosConfig.getBucket()).thenReturn(bucket);
        when(bosUtils.getObject(anyString(), anyString())).thenReturn(bosObject);
        when(bosObject.getObjectContent()).thenReturn(inputStream);

        try (MockedStatic<ExcelUtil> excelUtilMock = mockStatic(ExcelUtil.class)) {
            ExcelReader excelReader = mock(ExcelReader.class);
            excelUtilMock.when(() -> ExcelUtil.getReader(inputStream, 0)).thenReturn(excelReader);
            List<List<Object>> mockData = Arrays.asList(
                    Arrays.asList("field1", "field2"),
                    Arrays.asList("字段1", "字段2"),
                    Arrays.asList("value1", new Date())
            );
            when(excelReader.read(anyInt(), anyInt())).thenReturn(mockData);
            // Act
            List<RowDataDto> result = tableRecordService.getOneDataFromFile(file);
            // Assert
            assertNotNull(result);
            assertEquals(2, result.size());
            assertEquals("field1", result.get(0).getEnName());
            assertEquals("字段1", result.get(0).getCnName());
            assertEquals("value1", result.get(0).getData());
        }
    }

    @Test
    void fieldMappingShouldThrowExceptionWhenRowDataIsEmpty() {
        mappingRequest = new FileImportFieldMappingRequest();
        mappingRequest.setDataTableId(1L);
        mappingRequest.setFileId(1L);
        taskFileImport = new TaskFileImportWithBLOBs();
        taskFileImport.setFirstRowData("compressedData".getBytes());
        RowDataDto rowData1 = new RowDataDto();
        rowData1.setEnName("field1");
        rowData1.setCnName("字段1");
        rowData1.setDataType("string");
        RowDataDto rowData2 = new RowDataDto();
        rowData2.setEnName("field2");
        rowData2.setCnName("字段2");
        rowData2.setDataType("int");
        rowDataDtos = Arrays.asList(rowData1, rowData2);
        VisibleFieldResponse visibleField1 = new VisibleFieldResponse();
        visibleField1.setEnName("field1");
        visibleField1.setCnName("字段1");
        visibleField1.setDataType("string");
        VisibleFieldResponse visibleField2 = new VisibleFieldResponse();
        visibleField2.setEnName("field2");
        visibleField2.setCnName("字段2");
        visibleField2.setDataType("int");
        visibleFields = Arrays.asList(visibleField1, visibleField2);
        FieldMappingDto mapping1 = new FieldMappingDto();
        mapping1.setSourceEnName("field1");
        mapping1.setTagEnName("field1");
        FieldMappingDto mapping2 = new FieldMappingDto();
        mapping2.setSourceEnName("field2");
        mapping2.setTagEnName("field2");
        expectedMappings = Arrays.asList(mapping1, mapping2);
        try(
                MockedStatic<GzipUtils> gzipUtilsMockedStatic = mockStatic(GzipUtils.class);
                ) {
            // Arrange
            when(taskFileImportMapper.selectByExampleWithBLOBs(any(TaskFileImportCriteria.class)))
                    .thenReturn(Collections.singletonList(taskFileImport));
            gzipUtilsMockedStatic.when(() -> GzipUtils.decompressToString(any(byte[].class))).thenReturn("");
            // Act & Assert
            DeepSightException.ParamsErrorException paramsErrorException = assertThrows(DeepSightException.ParamsErrorException.class, () -> {
                tableRecordService.fieldMapping(mappingRequest);
            });
            assertEquals("获取Excel表字段失败", paramsErrorException.getMessage());
        }
        
    }

    @Test
    void fieldMappingShouldThrowExceptionWhenVisibleFieldsIsEmpty() {
        mappingRequest = new FileImportFieldMappingRequest();
        mappingRequest.setDataTableId(1L);
        mappingRequest.setFileId(1L);
        taskFileImport = new TaskFileImportWithBLOBs();
        taskFileImport.setFirstRowData("compressedData".getBytes());
        RowDataDto rowData1 = new RowDataDto();
        rowData1.setEnName("field1");
        rowData1.setCnName("字段1");
        rowData1.setDataType("string");
        RowDataDto rowData2 = new RowDataDto();
        rowData2.setEnName("field2");
        rowData2.setCnName("字段2");
        rowData2.setDataType("int");
        rowDataDtos = Arrays.asList(rowData1, rowData2);
        VisibleFieldResponse visibleField1 = new VisibleFieldResponse();
        visibleField1.setEnName("field1");
        visibleField1.setCnName("字段1");
        visibleField1.setDataType("string");
        VisibleFieldResponse visibleField2 = new VisibleFieldResponse();
        visibleField2.setEnName("field2");
        visibleField2.setCnName("字段2");
        visibleField2.setDataType("int");
        visibleFields = Arrays.asList(visibleField1, visibleField2);
        FieldMappingDto mapping1 = new FieldMappingDto();
        mapping1.setSourceEnName("field1");
        mapping1.setTagEnName("field1");
        FieldMappingDto mapping2 = new FieldMappingDto();
        mapping2.setSourceEnName("field2");
        mapping2.setTagEnName("field2");
        expectedMappings = Arrays.asList(mapping1, mapping2);
        // Arrange
        when(taskFileImportMapper.selectByExampleWithBLOBs(any(TaskFileImportCriteria.class)))
                .thenReturn(Collections.singletonList(taskFileImport));
        try(
                MockedStatic<GzipUtils> gzipUtilsMockedStatic = mockStatic(GzipUtils.class);
        ) {
            // Arrange
            when(taskFileImportMapper.selectByExampleWithBLOBs(any(TaskFileImportCriteria.class)))
                    .thenReturn(Collections.singletonList(taskFileImport));
            gzipUtilsMockedStatic.when(() -> GzipUtils.decompressToString(any(byte[].class))).thenReturn(JSONUtil.toJsonStr(rowDataDtos));
            when(dataTableManageService.getVisibleFields(anyLong(), anyBoolean()))
                    .thenReturn(Collections.emptyList());
            // Act & Assert
            DeepSightException.ParamsErrorException paramsErrorException = assertThrows(DeepSightException.ParamsErrorException.class, () -> {
                tableRecordService.fieldMapping(mappingRequest);
            });
            assertEquals("获取数据集表字段失败", paramsErrorException.getMessage());
        }
    }

    @Test
    void fieldMappingShouldReturnResponseWithEqualNameMapping() throws Exception {
        mappingRequest = new FileImportFieldMappingRequest();
        mappingRequest.setDataTableId(1L);
        mappingRequest.setFileId(1L);
        taskFileImport = new TaskFileImportWithBLOBs();
        taskFileImport.setFirstRowData("compressedData".getBytes());
        RowDataDto rowData1 = new RowDataDto();
        rowData1.setEnName("field1");
        rowData1.setCnName("字段1");
        rowData1.setDataType("string");
        RowDataDto rowData2 = new RowDataDto();
        rowData2.setEnName("field2");
        rowData2.setCnName("字段2");
        rowData2.setDataType("int");
        rowDataDtos = Arrays.asList(rowData1, rowData2);
        VisibleFieldResponse visibleField1 = new VisibleFieldResponse();
        visibleField1.setEnName("field1");
        visibleField1.setCnName("字段1");
        visibleField1.setDataType("string");
        VisibleFieldResponse visibleField2 = new VisibleFieldResponse();
        visibleField2.setEnName("field2");
        visibleField2.setCnName("字段2");
        visibleField2.setDataType("int");
        visibleFields = Arrays.asList(visibleField1, visibleField2);
        FieldMappingDto mapping1 = new FieldMappingDto();
        mapping1.setSourceEnName("field1");
        mapping1.setTagEnName("field1");
        FieldMappingDto mapping2 = new FieldMappingDto();
        mapping2.setSourceEnName("field2");
        mapping2.setTagEnName("field2");
        expectedMappings = Arrays.asList(mapping1, mapping2);
        // Arrange
        mappingRequest.setMappingType(ImportMappingTypeEnum.EQUAL_NAME);
        when(taskFileImportMapper.selectByExampleWithBLOBs(any(TaskFileImportCriteria.class)))
                .thenReturn(Collections.singletonList(taskFileImport));
        try(
                MockedStatic<GzipUtils> gzipUtilsMockedStatic = mockStatic(GzipUtils.class);
        ) {
            // Arrange
            when(taskFileImportMapper.selectByExampleWithBLOBs(any(TaskFileImportCriteria.class)))
                    .thenReturn(Collections.singletonList(taskFileImport));
            gzipUtilsMockedStatic.when(() -> GzipUtils.decompressToString(any(byte[].class))).thenReturn(JSONUtil.toJsonStr(rowDataDtos));
            when(dataTableManageService.getVisibleFields(anyLong(), anyBoolean()))
                    .thenReturn(visibleFields);
            when(dataTableManageService.validDataTableByTenantId(anyLong()))
                    .thenReturn(new DataTableInfo());
            // Act
            FieldAiMappingResponse response = tableRecordService.fieldMapping(mappingRequest);
            // Assert
            assertNotNull(response);
            assertEquals(rowDataDtos, response.getExcelFields());
            assertEquals(visibleFields, response.getDorisFields());
            assertEquals(2, response.getMappingRes().size());
            verify(taskFileImportMapper, times(1)).selectByExampleWithBLOBs(any(TaskFileImportCriteria.class));
            verify(dataTableManageService, times(1)).getVisibleFields(anyLong(), anyBoolean());
        }
        
    }

    @Test
    void fieldMappingShouldReturnResponseWithAiMapping() throws Exception {
        mappingRequest = new FileImportFieldMappingRequest();
        mappingRequest.setDataTableId(1L);
        mappingRequest.setFileId(1L);

        taskFileImport = new TaskFileImportWithBLOBs();
        taskFileImport.setFirstRowData("compressedData".getBytes());

        RowDataDto rowData1 = new RowDataDto();
        rowData1.setEnName("field1");
        rowData1.setCnName("字段1");
        rowData1.setDataType("string");

        RowDataDto rowData2 = new RowDataDto();
        rowData2.setEnName("field2");
        rowData2.setCnName("字段2");
        rowData2.setDataType("int");

        rowDataDtos = Arrays.asList(rowData1, rowData2);

        VisibleFieldResponse visibleField1 = new VisibleFieldResponse();
        visibleField1.setEnName("field1");
        visibleField1.setCnName("字段1");
        visibleField1.setDataType("string");

        VisibleFieldResponse visibleField2 = new VisibleFieldResponse();
        visibleField2.setEnName("field2");
        visibleField2.setCnName("字段2");
        visibleField2.setDataType("int");

        visibleFields = Arrays.asList(visibleField1, visibleField2);

        FieldMappingDto mapping1 = new FieldMappingDto();
        mapping1.setSourceEnName("field1");
        mapping1.setTagEnName("field1");

        FieldMappingDto mapping2 = new FieldMappingDto();
        mapping2.setSourceEnName("field2");
        mapping2.setTagEnName("field2");

        expectedMappings = Arrays.asList(mapping1, mapping2);

        // Arrange
        mappingRequest.setMappingType(ImportMappingTypeEnum.AI_SEMANTEME);

        when(taskFileImportMapper.selectByExampleWithBLOBs(any(TaskFileImportCriteria.class)))
                .thenReturn(Collections.singletonList(taskFileImport));
        try(
                MockedStatic<GzipUtils> gzipUtilsMockedStatic = mockStatic(GzipUtils.class);
        ) {
            // Arrange
            when(taskFileImportMapper.selectByExampleWithBLOBs(any(TaskFileImportCriteria.class)))
                    .thenReturn(Collections.singletonList(taskFileImport));
            gzipUtilsMockedStatic.when(() -> GzipUtils.decompressToString(any(byte[].class))).thenReturn(JSONUtil.toJsonStr(rowDataDtos));
            when(dataTableManageService.getVisibleFields(anyLong(), anyBoolean()))
                    .thenReturn(visibleFields);
            when(aiBaseService.fieldMapping(anyString()))
                    .thenReturn(expectedMappings);
            when(dataTableManageService.validDataTableByTenantId(anyLong()))
                    .thenReturn(new DataTableInfo());
            // Act
            FieldAiMappingResponse response = tableRecordService.fieldMapping(mappingRequest);
            // Assert
            assertNotNull(response);
            assertEquals(rowDataDtos, response.getExcelFields());
            assertEquals(visibleFields, response.getDorisFields());
            assertEquals(2, response.getMappingRes().size());
            verify(aiBaseService, times(1)).fieldMapping(anyString());
        }
    }

    @Test
    void fieldMappingShouldThrowExceptionWhenTaskNotFound() {
        mappingRequest = new FileImportFieldMappingRequest();
        mappingRequest.setDataTableId(1L);
        mappingRequest.setFileId(1L);

        taskFileImport = new TaskFileImportWithBLOBs();
        taskFileImport.setFirstRowData("compressedData".getBytes());

        RowDataDto rowData1 = new RowDataDto();
        rowData1.setEnName("field1");
        rowData1.setCnName("字段1");
        rowData1.setDataType("string");

        RowDataDto rowData2 = new RowDataDto();
        rowData2.setEnName("field2");
        rowData2.setCnName("字段2");
        rowData2.setDataType("int");

        rowDataDtos = Arrays.asList(rowData1, rowData2);

        VisibleFieldResponse visibleField1 = new VisibleFieldResponse();
        visibleField1.setEnName("field1");
        visibleField1.setCnName("字段1");
        visibleField1.setDataType("string");

        VisibleFieldResponse visibleField2 = new VisibleFieldResponse();
        visibleField2.setEnName("field2");
        visibleField2.setCnName("字段2");
        visibleField2.setDataType("int");

        visibleFields = Arrays.asList(visibleField1, visibleField2);

        FieldMappingDto mapping1 = new FieldMappingDto();
        mapping1.setSourceEnName("field1");
        mapping1.setTagEnName("field1");

        FieldMappingDto mapping2 = new FieldMappingDto();
        mapping2.setSourceEnName("field2");
        mapping2.setTagEnName("field2");

        expectedMappings = Arrays.asList(mapping1, mapping2);

        // Arrange
        when(taskFileImportMapper.selectByExampleWithBLOBs(any(TaskFileImportCriteria.class)))
                .thenReturn(Collections.emptyList());

        // Act & Assert
        assertThrows(DeepSightException.ParamsErrorException.class, () -> {
            tableRecordService.fieldMapping(mappingRequest);
        });
    }
}