package com.baidu.keyue.deepsight.schedule;

import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTask;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerWithBLOBs;
import com.baidu.keyue.deepsight.service.diffusion.GroupDiffusionCalculateService;
import com.baidu.keyue.deepsight.service.tasks.TaskSchedulerService;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CustomerDiffusionSchedulerTest {

    @InjectMocks
    private CustomerDiffusionScheduler scheduler;

    @Mock
    private RedissonClient redisson;

    @Mock
    private GroupDiffusionCalculateService diffusionService;

    @Mock
    private TaskSchedulerService taskSchedulerService;

    @Mock
    private RLock rLock;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(scheduler, "customerDiffusion", true);
    }





    @Test
    void testCustomerDiffusionCheckScheduler_NoTasks() {
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(rLock.tryLock()).thenReturn(true);
        when(diffusionService.pullRunningTask()).thenReturn(Collections.emptyList());

        scheduler.customerDiffusionCheckScheduler();

        verify(diffusionService, times(1)).pullRunningTask();
        verify(rLock, times(1)).unlock();
    }

    @Test
    void testCustomerDiffusionCheckScheduler_ExceptionInProcess() {
        TaskSchedulerWithBLOBs task = new TaskSchedulerWithBLOBs();
        task.setTaskId(1L);
        task.setId(1L);
        List<TaskSchedulerWithBLOBs> tasks = List.of(task);

        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(rLock.tryLock()).thenReturn(true);
        when(diffusionService.pullRunningTask()).thenReturn(tasks);
        doThrow(new RuntimeException("test")).when(diffusionService).fetchGroupDiffusionResult(1L);

        scheduler.customerDiffusionCheckScheduler();

        verify(diffusionService, times(1)).pullRunningTask();
        verify(diffusionService, times(1)).fetchGroupDiffusionResult(1L);
        verify(rLock, times(1)).unlock();
    }


    @Test
    void testCustomerDiffusionCheckScheduler_WithTasks() {
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(rLock.tryLock()).thenReturn(true);

        TaskSchedulerWithBLOBs task = new TaskSchedulerWithBLOBs();
        task.setTaskId(1L);
        task.setId(1L);
        List<TaskSchedulerWithBLOBs> tasks = List.of(task);

        when(diffusionService.pullRunningTask()).thenReturn(tasks);

        scheduler.customerDiffusionCheckScheduler();

        verify(diffusionService, times(1)).fetchGroupDiffusionResult(task.getTaskId());
        verify(rLock, times(1)).unlock();
    }

    @Test
    void customerDiffusionScheduler_WhenLockFailed_ShouldThrowException() {
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(rLock.tryLock()).thenReturn(false);

        assertThrows(DeepSightException.BusyRequestException.class,
                () -> scheduler.customerDiffusionScheduler());

        verify(rLock, never()).unlock();
    }

    @Test
    void customerDiffusionScheduler_WhenNoTasks_ShouldReturnEarly() {
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(rLock.tryLock()).thenReturn(true);
        when(diffusionService.pullTaskByStatus(TaskExecStatusEnum.SUCCESS))
                .thenReturn(Collections.emptyList());

        scheduler.customerDiffusionScheduler();

        verify(diffusionService).pullTaskByStatus(TaskExecStatusEnum.SUCCESS);
        verify(rLock).unlock();
    }


    @Test
    void customerDiffusionScheduler_ShouldReturnEarly() {
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(rLock.tryLock()).thenReturn(true);
        List<Pair<CustomerDiffusionTask, TaskInfo>> taskList= new ArrayList<>();
        taskList.add(Pair.of(new CustomerDiffusionTask(), new TaskInfo()));
        when(diffusionService.pullTaskByStatus(TaskExecStatusEnum.SUCCESS))
                .thenReturn(taskList);

        scheduler.customerDiffusionScheduler();

        verify(diffusionService).pullTaskByStatus(TaskExecStatusEnum.SUCCESS);
        verify(rLock).unlock();
    }



    @Test
    void customerDiffusionScheduler_WhenExceptionOccurs_ShouldUnlock() {
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(rLock.tryLock()).thenReturn(true);
        when(diffusionService.pullTaskByStatus(TaskExecStatusEnum.SUCCESS))
                .thenThrow(new RuntimeException("Test exception"));

        assertDoesNotThrow(() -> scheduler.customerDiffusionScheduler());
        verify(rLock).unlock();
    }
}
