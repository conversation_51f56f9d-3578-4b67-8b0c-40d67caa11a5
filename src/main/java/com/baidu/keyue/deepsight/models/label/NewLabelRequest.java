package com.baidu.keyue.deepsight.models.label;

import com.baidu.keyue.deepsight.annotation.LabelNameConstraint;
import com.baidu.keyue.deepsight.annotation.LongIdConstraint;
import com.baidu.keyue.deepsight.enums.LabelValueSaveModEnum;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.enums.UpdateModEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class NewLabelRequest {

    /**
     * 标签名称
     */
    @LabelNameConstraint
    private String labelName;

    /**
     * 标签目录 ID
     */
    @LongIdConstraint
    private Long catalogId;

    /**
     * 标签更新取值逻辑
     */
    @NotNull(message = "标签更新取值逻辑模式不能为空")
    private UpdateModEnum updateMod;

    /**
     * 标签值保存类型
     */
    @NotNull(message = "标签值保存类型不能为空")
    private LabelValueSaveModEnum labelValueSaveMod;

    /**
     * 更新类型
     */
    @NotNull(message = "更新类型不能为空")
    private TriggerModeEnum triggerMod;

    /**
     * 执行频率
     */
    private TriggerFrequencyEnum triggerFrequency;

    /**
     * 执行频率具体时间配置
     * */
    private TriggerFrequencyValue triggerFrequencyValue;

    /**
     * 标签值规则
     * */
    @NotNull(message = "标签值规则不能为空")
    private LabelRule labelRule;

    /**
     * 是否为内置标签
     * 默认为 false（用户标签）
     */
    private Boolean isBuiltin = false;

    /**
     * 是否为动态标签名
     * 默认为 false（静态标签）
     */
    private Boolean isDynamic = false;
}
