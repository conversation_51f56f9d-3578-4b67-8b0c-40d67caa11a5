package com.baidu.keyue.deepsight.models.sop;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * SOP离线分析任务查询请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SopTaskQueryRequest extends SopTaskCfgRequestBase{

    /**
     * 任务状态：1-执行中，2-计算失败，3-成功
     */
    @Range(min = 1, max = 3, message = "任务状态参数不正确")
    private Byte status;
    /**
     * 页码
     */
    @Min(value = 1, message = "页码最小值为1")
    private Integer pageNo = 1;
    /**
     * 页大小
     */
    @Max(value = 100, message = "页大小最大值为100")
    private Integer pageSize = 10;

    /**
     * 名称搜索
     */
    private String keyword;
}