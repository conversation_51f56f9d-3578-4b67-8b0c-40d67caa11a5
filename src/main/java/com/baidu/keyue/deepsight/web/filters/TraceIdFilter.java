package com.baidu.keyue.deepsight.web.filters;

import com.baidu.keyue.deepsight.utils.TraceIdUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * TraceId过滤器
 * 用于在请求开始时生成或提取TraceId，并在请求结束时清理
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Component
@Order(1) // 确保在其他过滤器之前执行
@Slf4j
public class TraceIdFilter implements Filter {

    /**
     * TraceId请求头名称
     */
    private static final String TRACE_ID_HEADER = "X-Trace-Id";
    
    /**
     * 备用TraceId请求头名称
     */
    private static final String TRACE_ID_HEADER_ALT = "Trace-Id";
    
    /**
     * RequestId请求头名称（兼容现有系统）
     */
    private static final String REQUEST_ID_HEADER = "X-Request-Id";

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("TraceIdFilter initialized");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        if (!(request instanceof HttpServletRequest httpRequest) || 
            !(response instanceof HttpServletResponse httpResponse)) {
            chain.doFilter(request, response);
            return;
        }

        try {
            // 1. 提取或生成TraceId
            String traceId = extractOrGenerateTraceId(httpRequest);
            
            // 2. 设置TraceId到MDC
            TraceIdUtils.setTraceId(traceId);
            
            // 3. 设置请求信息到MDC
            TraceIdUtils.setRequestInfo(httpRequest.getRequestURI(), httpRequest.getMethod());
            
            // 4. 尝试从WebContextHolder获取用户信息并设置到MDC
            setUserInfoToMDC();
            
            // 5. 将TraceId添加到响应头
            httpResponse.setHeader(TRACE_ID_HEADER, traceId);
            
            // 6. 记录请求开始日志
            logRequestStart(httpRequest, traceId);
            
            // 7. 继续执行请求
            chain.doFilter(request, response);
            
        } catch (Exception e) {
            log.error("TraceIdFilter处理异常", e);
            throw e;
        } finally {
            // 8. 记录请求结束日志
            logRequestEnd(httpRequest);
            
            // 9. 清理MDC
            TraceIdUtils.clear();
        }
    }

    /**
     * 提取或生成TraceId
     * 
     * @param request HTTP请求
     * @return TraceId
     */
    private String extractOrGenerateTraceId(HttpServletRequest request) {
        // 1. 尝试从请求头获取TraceId
        String traceId = request.getHeader(TRACE_ID_HEADER);
        if (StringUtils.isNotBlank(traceId)) {
            return traceId;
        }
        
        // 2. 尝试从备用请求头获取
        traceId = request.getHeader(TRACE_ID_HEADER_ALT);
        if (StringUtils.isNotBlank(traceId)) {
            return traceId;
        }
        
        // 3. 尝试从RequestId请求头获取（兼容现有系统）
        traceId = request.getHeader(REQUEST_ID_HEADER);
        if (StringUtils.isNotBlank(traceId)) {
            return traceId;
        }
        
        // 4. 生成新的TraceId
        return TraceIdUtils.generateTraceId();
    }

    /**
     * 设置用户信息到MDC
     */
    private void setUserInfoToMDC() {
        try {
            // 尝试从WebContextHolder获取用户信息
            String userId = WebContextHolder.getUserId();
            String tenantId = WebContextHolder.getTenantId();
            
            if (StringUtils.isNotBlank(userId) || StringUtils.isNotBlank(tenantId)) {
                TraceIdUtils.setUserInfo(userId, tenantId);
            }
        } catch (Exception e) {
            // 忽略异常，可能是在用户认证之前
            log.debug("无法获取用户信息，可能是在用户认证之前: {}", e.getMessage());
        }
    }

    /**
     * 记录请求开始日志
     * 
     * @param request HTTP请求
     * @param traceId TraceId
     */
    private void logRequestStart(HttpServletRequest request, String traceId) {
        if (log.isDebugEnabled()) {
            String clientIp = getClientIp(request);
            String userAgent = request.getHeader("User-Agent");
            
            log.debug("请求开始 - URI: {}, Method: {}, ClientIP: {}, UserAgent: {}", 
                    request.getRequestURI(), 
                    request.getMethod(), 
                    clientIp, 
                    userAgent);
        }
    }

    /**
     * 记录请求结束日志
     * 
     * @param request HTTP请求
     */
    private void logRequestEnd(HttpServletRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("请求结束 - URI: {}, Method: {}", 
                    request.getRequestURI(), 
                    request.getMethod());
        }
    }

    /**
     * 获取客户端IP地址
     * 
     * @param request HTTP请求
     * @return 客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        
        // 处理多个IP的情况，取第一个
        if (StringUtils.isNotBlank(ip) && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        
        return ip;
    }

    @Override
    public void destroy() {
        log.info("TraceIdFilter destroyed");
    }
}
