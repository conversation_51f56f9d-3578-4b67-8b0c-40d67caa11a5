package com.baidu.keyue.deepsight.service.ai;

import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AssistantRecognitionService 测试类
 */
@ExtendWith(MockitoExtension.class)
class AssistantRecognitionServiceTest {

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private DorisService dorisService;

    @InjectMocks
    private AssistantRecognitionService assistantRecognitionService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(assistantRecognitionService, "assistantRecognitionUrl", 
            "http://10.234.52.167:8097/getAssistantRecognition/v1");
        ReflectionTestUtils.setField(assistantRecognitionService, "timeout", 5000);
        ReflectionTestUtils.setField(assistantRecognitionService, "objectMapper", objectMapper);
    }

    @Test
    void testRecognizeAssistant_Success() {
        // 准备测试数据
        String sessionId = "test_session_id";
        String taskId = "test_task_id";
        String conversationContent = "AI: 喂，您好\n用户: 我是通话助理。\nAI: 好的，谢谢。";
        String tenantId = "test_tenant_id";

        // Mock nodeInfo数据 (Base64编码的JSON)
        Map<String, Object> nodeData = new HashMap<>();
        nodeData.put("systemEvent", "hungUp");
        nodeData.put("hungUpType", 6);
        
        String nodeInfoJson = "{\"systemEvent\":\"hungUp\",\"hungUpType\":6}";
        String encodedNodeInfo = Base64.getEncoder().encodeToString(nodeInfoJson.getBytes());
        
        Map<String, Object> recordData = new HashMap<>();
        recordData.put("nodeInfo", encodedNodeInfo);
        
        // Mock 数据库查询
        try (MockedStatic<TenantUtils> mockedTenantUtils = mockStatic(TenantUtils.class)) {
            mockedTenantUtils.when(() -> TenantUtils.generateAiobRecordTableName(tenantId))
                .thenReturn("aiob_conversation_record_" + tenantId);
            
            when(dorisService.selectList(anyString())).thenReturn(List.of(recordData));
            
            // Mock 算法接口调用
            Map<String, Object> apiResponse = new HashMap<>();
            apiResponse.put("result", true);
            ResponseEntity<Map> responseEntity = new ResponseEntity<>(apiResponse, HttpStatus.OK);
            
            when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(Map.class)))
                .thenReturn(responseEntity);
            
            // 执行测试
            Boolean result = assistantRecognitionService.recognizeAssistant(sessionId, taskId, conversationContent, tenantId);
            
            // 验证结果
            assertTrue(result);
            verify(dorisService).selectList(anyString());
            verify(restTemplate).postForEntity(anyString(), any(HttpEntity.class), eq(Map.class));
        }
    }

    @Test
    void testRecognizeAssistant_PreconditionNotMet() {
        // 准备测试数据
        String sessionId = "test_session_id";
        String taskId = "test_task_id";
        String conversationContent = "AI: 喂，您好\n用户: 你好。";
        String tenantId = "test_tenant_id";

        // Mock 空的record数据（不满足挂断条件）
        try (MockedStatic<TenantUtils> mockedTenantUtils = mockStatic(TenantUtils.class)) {
            mockedTenantUtils.when(() -> TenantUtils.generateAiobRecordTableName(tenantId))
                .thenReturn("aiob_conversation_record_" + tenantId);
            
            when(dorisService.selectList(anyString())).thenReturn(List.of());
            
            // 执行测试
            Boolean result = assistantRecognitionService.recognizeAssistant(sessionId, taskId, conversationContent, tenantId);
            
            // 验证结果
            assertNull(result);
            verify(dorisService).selectList(anyString());
            verify(restTemplate, never()).postForEntity(anyString(), any(HttpEntity.class), eq(Map.class));
        }
    }

    @Test
    void testRecognizeAssistant_ApiCallFailed() {
        // 准备测试数据
        String sessionId = "test_session_id";
        String taskId = "test_task_id";
        String conversationContent = "AI: 喂，您好\n用户: 我是通话助理。";
        String tenantId = "test_tenant_id";

        // Mock nodeInfo数据
        String nodeInfoJson = "{\"systemEvent\":\"hungUp\",\"hungUpType\":6}";
        String encodedNodeInfo = Base64.getEncoder().encodeToString(nodeInfoJson.getBytes());
        
        Map<String, Object> recordData = new HashMap<>();
        recordData.put("nodeInfo", encodedNodeInfo);
        
        try (MockedStatic<TenantUtils> mockedTenantUtils = mockStatic(TenantUtils.class)) {
            mockedTenantUtils.when(() -> TenantUtils.generateAiobRecordTableName(tenantId))
                .thenReturn("aiob_conversation_record_" + tenantId);
            
            when(dorisService.selectList(anyString())).thenReturn(List.of(recordData));
            
            // Mock 算法接口调用失败
            when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(Map.class)))
                .thenThrow(new RuntimeException("API call failed"));
            
            // 执行测试
            Boolean result = assistantRecognitionService.recognizeAssistant(sessionId, taskId, conversationContent, tenantId);
            
            // 验证结果
            assertNull(result);
            verify(dorisService).selectList(anyString());
            verify(restTemplate).postForEntity(anyString(), any(HttpEntity.class), eq(Map.class));
        }
    }

    @Test
    void testCheckHangupCondition_ValidCondition() throws Exception {
        // 准备测试数据
        Map<String, Object> nodeData = new HashMap<>();
        nodeData.put("systemEvent", "hungUp");
        nodeData.put("hungUpType", 6);
        
        String nodeInfoJson = objectMapper.writeValueAsString(nodeData);
        String encodedNodeInfo = Base64.getEncoder().encodeToString(nodeInfoJson.getBytes());
        
        // 使用反射调用私有方法
        Boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
            assistantRecognitionService, "checkHangupCondition", encodedNodeInfo);
        
        assertTrue(result);
    }

    @Test
    void testCheckHangupCondition_InvalidCondition() throws Exception {
        // 准备测试数据 - 不满足条件
        Map<String, Object> nodeData = new HashMap<>();
        nodeData.put("systemEvent", "other");
        nodeData.put("hungUpType", 5);
        
        String nodeInfoJson = objectMapper.writeValueAsString(nodeData);
        String encodedNodeInfo = Base64.getEncoder().encodeToString(nodeInfoJson.getBytes());
        
        // 使用反射调用私有方法
        Boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
            assistantRecognitionService, "checkHangupCondition", encodedNodeInfo);
        
        assertFalse(result);
    }

    @Test
    void testParseRecognitionResult_BooleanResult() {
        // 准备测试数据
        Map<String, Object> response = new HashMap<>();
        response.put("result", true);
        
        // 使用反射调用私有方法
        Boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
            assistantRecognitionService, "parseRecognitionResult", response);
        
        assertTrue(result);
    }

    @Test
    void testParseRecognitionResult_StringResult() {
        // 准备测试数据
        Map<String, Object> response = new HashMap<>();
        response.put("result", "true");
        
        // 使用反射调用私有方法
        Boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
            assistantRecognitionService, "parseRecognitionResult", response);
        
        assertTrue(result);
    }

    @Test
    void testParseRecognitionResult_NumberResult() {
        // 准备测试数据
        Map<String, Object> response = new HashMap<>();
        response.put("result", 1);
        
        // 使用反射调用私有方法
        Boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
            assistantRecognitionService, "parseRecognitionResult", response);
        
        assertTrue(result);
    }
}
