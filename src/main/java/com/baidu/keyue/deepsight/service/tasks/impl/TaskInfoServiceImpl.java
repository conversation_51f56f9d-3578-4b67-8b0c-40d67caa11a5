package com.baidu.keyue.deepsight.service.tasks.impl;

import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.TaskTypeEnum;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.label.TriggerFrequencyValue;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendTaskInfoMapper;
import com.baidu.keyue.deepsight.service.tasks.TaskInfoService;
import com.baidu.keyue.deepsight.utils.CronUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class TaskInfoServiceImpl implements TaskInfoService {
    @Autowired
    private ExtendTaskInfoMapper taskInfoMapper;

    private String cronExpress(TriggerFrequencyEnum frequency, TriggerFrequencyValue value) {
        String cron = null;
        switch (frequency) {
            case DAY -> {
                cron = CronUtils.everyDay(value.getHour());
            }
            case WEEK -> {
                cron = CronUtils.everyWeek(value.getHour(), value.getDayOfWeek());
            }
            case MONTH -> {
                cron = CronUtils.everyMonth(value.getHour(), value.getDayOfMonth());
            }
            default -> {
                throw new DeepSightException.LabelOperatorFailedException(ErrorCode.BAD_REQUEST, "更新方式配置无效");
            }
        }
        return cron;
    }

    @Override
    public long createNewLabelCalTask(TriggerModeEnum triggerMod,
                                      TriggerFrequencyEnum frequency, TriggerFrequencyValue value) {
        String userId = WebContextHolder.getUserId();
        Date now = new Date();
        TaskInfo l = new TaskInfo();
        l.setTaskType(TaskTypeEnum.LABEL_DWS_TASK.getCode());
        if (TriggerModeEnum.CRON.equals(triggerMod)) {
            String cron = cronExpress(frequency, value);
            Date nextExecDate = CronUtils.nextExecDate(cron);
            l.setTriggerCron(cron);
            l.setNextExecDate(nextExecDate);
        }
        l.setTriggerMod(triggerMod.getCode());
        l.setDel(DelEnum.NOT_DELETED.getBoolean());
        l.setCreator(WebContextHolder.getUserAuthInfo().getUserName());
        l.setModifier(WebContextHolder.getUserAuthInfo().getUserName());
        l.setCreateTime(now);
        l.setUpdateTime(now);

        taskInfoMapper.insert(l);
        return l.getId();
    }

    @Override
    public void deleteFieldById(long id) {
        TaskInfo taskInfo = new TaskInfo();
        taskInfo.setId(id);
        taskInfo.setDel(true);
        taskInfoMapper.updateByPrimaryKeySelective(taskInfo);
    }

    @Override
    public void updateLabelCalTaskTrigger(long id, TriggerModeEnum triggerMod,
                                          TriggerFrequencyEnum frequency, TriggerFrequencyValue value) {
        TaskInfo l = taskInfoMapper.selectByPrimaryKey(id);
        if (Objects.isNull(l)) {
            throw new DeepSightException.LabelOperatorFailedException(ErrorCode.INTERNAL_ERROR, "标签更新任务异常");
        }

        String userId = WebContextHolder.getUserId();
        Date now = new Date();

        if (TriggerModeEnum.CRON.equals(triggerMod)) {
            String cron = cronExpress(frequency, value);
            if (StringUtils.isNotBlank(l.getTriggerCron()) && l.getTriggerCron().equals(cron)) {
                return;
            }
            Date nextExecDate = CronUtils.nextExecDate(cron);
            taskInfoMapper.taskTriggerUpdate(id, cron, nextExecDate, userId, now, triggerMod.getCode());
        } else {
            taskInfoMapper.taskTriggerUpdate(id, "", null, userId, now, triggerMod.getCode());
        }
    }

    @Override
    public TaskInfo getTaskDetailWithId(Long id) {
        if (Objects.isNull(id)) {
            throw new DeepSightException.TaskInfoIdInvalidException();
        }
        TaskInfo taskInfo = taskInfoMapper.selectByPrimaryKey(id);
        if (Objects.isNull(taskInfo)) {
            throw new DeepSightException.TaskInfoNotFoundException(id);
        }
        return taskInfo;
    }

    @Override
    public List<TaskInfo> getTaskDetailWithIds(TaskTypeEnum taskTypeEnum, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        TaskInfoCriteria taskInfoCriteria = new TaskInfoCriteria();
        TaskInfoCriteria.Criteria criteria = taskInfoCriteria.createCriteria();
        criteria.andTaskTypeEqualTo(taskTypeEnum.getCode());
        criteria.andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        criteria.andIdIn(ids);
        return taskInfoMapper.selectByExample(taskInfoCriteria);
    }

    @Override
    public List<TaskInfo> pullWaitExecTask(TaskTypeEnum taskTypeEnum, TriggerModeEnum triggerMod) {
        Date now = new Date();
        TaskInfoCriteria taskInfoCriteria = new TaskInfoCriteria();
        TaskInfoCriteria.Criteria criteria = taskInfoCriteria.createCriteria();

        criteria.andTaskTypeEqualTo(taskTypeEnum.getCode());
        criteria.andNextExecDateLessThanOrEqualTo(now);
        criteria.andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        criteria.andTriggerModEqualTo(triggerMod.getCode());

        return taskInfoMapper.selectByExample(taskInfoCriteria);
    }

    @Override
    public void updateNextCalDate(TaskInfo task) {
        String cron = task.getTriggerCron();
        if (StringUtils.isBlank(cron)) {
            log.warn("updateNextCalDate got blank cron, taskId: {}", task.getId());
            return;
        }
        Date next = CronUtils.nextExecDate(cron);
        if (Objects.isNull(next)) {
            log.warn("updateNextCalDate got null exec-date, taskId: {}, cron: {}", task.getId(), cron);
            return;
        }
        taskInfoMapper.updateTaskNextExecDate(task.getId(), next);
    }

    @Override
    public long createCalTask(TaskTypeEnum taskTypeEnum,
                              TriggerModeEnum triggerMod,
                              TriggerFrequencyEnum frequency,
                              TriggerFrequencyValue value) {
        String userId = WebContextHolder.getUserId();
        Date now = new Date();
        TaskInfo l = new TaskInfo();
        l.setTaskType(taskTypeEnum.getCode());
        if (TriggerModeEnum.CRON.equals(triggerMod)) {
            String cron = cronExpress(frequency, value);
            Date nextExecDate = CronUtils.nextExecDate(cron);
            l.setTriggerCron(cron);
            l.setNextExecDate(nextExecDate);
        }
        l.setTriggerMod(triggerMod.getCode());
        l.setDel(DelEnum.NOT_DELETED.getBoolean());
        l.setCreator(WebContextHolder.getUserAuthInfo().getUserName());
        l.setModifier(WebContextHolder.getUserAuthInfo().getUserName());
        l.setCreateTime(now);
        l.setUpdateTime(now);

        taskInfoMapper.insert(l);
        return l.getId();
    }

    @Override
    public long createMemoryExtractTask(TriggerModeEnum triggerMod, TriggerFrequencyEnum frequency, TriggerFrequencyValue value, String userId) {
        Date now = new Date();
        TaskInfo l = new TaskInfo();
        l.setTaskType(TaskTypeEnum.MEMORY_EXTRACT.getCode());
        if (TriggerModeEnum.CRON.equals(triggerMod)) {
            String cron = cronExpress(frequency, value);
            Date nextExecDate = CronUtils.nextExecDate(cron);
            l.setTriggerCron(cron);
            l.setNextExecDate(nextExecDate);
        }
        l.setTriggerMod(triggerMod.getCode());
        l.setDel(DelEnum.NOT_DELETED.getBoolean());
        l.setCreator(WebContextHolder.getUserAuthInfo().getUserName());
        l.setModifier(WebContextHolder.getUserAuthInfo().getUserName());
        l.setCreateTime(now);
        l.setUpdateTime(now);

        taskInfoMapper.insert(l);
        return l.getId();
    }

    @Override
    public void updateMemoryExtractTaskTrigger(long id, TriggerModeEnum triggerMod,
                                               TriggerFrequencyEnum frequency, TriggerFrequencyValue value) {
        TaskInfo l = taskInfoMapper.selectByPrimaryKey(id);
        if (Objects.isNull(l)) {
            throw new DeepSightException.LabelOperatorFailedException(ErrorCode.INTERNAL_ERROR, "更新任务配置异常");
        }

        String userId = WebContextHolder.getUserId();
        Date now = new Date();

        if (TriggerModeEnum.CRON.equals(triggerMod)) {
            String cron = cronExpress(frequency, value);
            if (StringUtils.isNotBlank(l.getTriggerCron()) && l.getTriggerCron().equals(cron)) {
                return;
            }
            Date nextExecDate = CronUtils.nextExecDate(cron);
            taskInfoMapper.taskTriggerUpdate(id, cron, nextExecDate, userId, now, triggerMod.getCode());
        } else {
            taskInfoMapper.taskTriggerUpdate(id, "", null, userId, now, triggerMod.getCode());
        }
    }

    @Override
    public void updateCalTaskTrigger(Long taskId, TriggerModeEnum triggerMode,
                                     TriggerFrequencyEnum triggerFrequency,
                                     TriggerFrequencyValue triggerFrequencyValue) {
        TaskInfo l = taskInfoMapper.selectByPrimaryKey(taskId);
        if (Objects.isNull(l)) {
            throw new DeepSightException.LabelOperatorFailedException(ErrorCode.INTERNAL_ERROR, "更新任务配置异常");
        }

        String userId = WebContextHolder.getUserId();
        Date now = new Date();

        if (TriggerModeEnum.CRON.equals(triggerMode)) {
            String cron = cronExpress(triggerFrequency, triggerFrequencyValue);
            if (StringUtils.isNotBlank(l.getTriggerCron()) && l.getTriggerCron().equals(cron)) {
                return;
            }
            Date nextExecDate = CronUtils.nextExecDate(cron);
            taskInfoMapper.taskTriggerUpdate(taskId, cron, nextExecDate, userId, now, triggerMode.getCode());
        } else {
            taskInfoMapper.taskTriggerUpdate(taskId, "", null, userId, now, triggerMode.getCode());
        }
    }

    @Override
    public Long createTask(TaskTypeEnum taskTypeEnum, 
                           TriggerModeEnum triggerMod, 
                           TriggerFrequencyEnum triggerFrequency, 
                           TriggerFrequencyValue triggerFrequencyValue) {
        String userId = WebContextHolder.getUserId();
        Date now = new Date();
        TaskInfo taskInfo = new TaskInfo();
        taskInfo.setTaskType(taskTypeEnum.getCode());
        if (TriggerModeEnum.CRON.equals(triggerMod)) {
            String cron = cronExpress(triggerFrequency, triggerFrequencyValue);
            Date nextExecDate = CronUtils.nextExecDate(cron);
            taskInfo.setTriggerCron(cron);
            taskInfo.setNextExecDate(nextExecDate);
        }
        taskInfo.setTaskDesc(taskTypeEnum.getDesc());
        taskInfo.setTriggerMod(triggerMod.getCode());
        taskInfo.setDel(DelEnum.NOT_DELETED.getBoolean());
        taskInfo.setCreator(WebContextHolder.getUserAuthInfo().getUserName());
        taskInfo.setModifier(WebContextHolder.getUserAuthInfo().getUserName());
        taskInfo.setCreateTime(now);
        taskInfo.setUpdateTime(now);
        taskInfoMapper.insert(taskInfo);
        return taskInfo.getId();
    }
}
